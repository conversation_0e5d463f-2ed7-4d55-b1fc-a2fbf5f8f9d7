<template>
  <div id="TestMCPComponent-test" class="testmcpcomponent-component">
    <h2>{{ title }}</h2>
    <div class="content">
      <!-- Component content will be generated based on Figma data -->
      <div class="figma-frame" style="">
        <div class="figma-rectangle" style="width: 400px; height: 300px; display: flex; flex-direction: column; padding: 20px; background-color: #f8f9fa"></div>
        <div class="figma-rectangle" style="width: 120px; height: 40px; display: flex; flex-direction: row; padding: 10px; background-color: #007bff"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// Component props and data
const title = ref('TestMCPComponent')

// Figma data
const figmaData = {
  "nodes": [
    {
      "type": "FRAME",
      "name": "<PERSON><PERSON>rame",
      "children": [
        {
          "type": "RECTANGLE",
          "name": "Background",
          "layout": "layout_1",
          "fills": "fill_1"
        },
        {
          "type": "RECTANGLE",
          "name": "Button",
          "layout": "layout_2",
          "fills": "fill_2"
        }
      ]
    }
  ],
  "globalVars": {
    "styles": {
      "layout_1": {
        "dimensions": {
          "width": 400,
          "height": 300
        },
        "mode": "column",
        "padding": "20px"
      },
      "layout_2": {
        "dimensions": {
          "width": 120,
          "height": 40
        },
        "mode": "row",
        "padding": "10px"
      },
      "fill_1": [
        "#f8f9fa"
      ],
      "fill_2": [
        "#007bff"
      ]
    }
  }
}

// Component logic here
</script>

<style scoped>
.testmcpcomponent-component {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
}

.content {
  margin-top: 16px;
}

/* Styles based on Figma data will be generated here */

/* Base Figma component styles */
.figma-frame {
  box-sizing: border-box;
}

.figma-rectangle {
  box-sizing: border-box;
}

.figma-group {
  position: relative;
}

.figma-image-svg {
  display: flex;
  align-items: center;
  justify-content: center;
}

.fill_1 { background-color: #f8f9fa; }
.fill_2 { background-color: #007bff; }
</style>