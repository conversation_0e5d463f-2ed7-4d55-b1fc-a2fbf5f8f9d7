<template>
  <div class="redemption-success-page">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="time">9:41</div>
      <div class="status-icons">
        <div class="signal"></div>
        <div class="wifi"></div>
        <div class="battery"></div>
      </div>
    </div>

    <!-- Logo区域 -->
    <div class="logo-section">
      <div class="logo-icon"></div>
      <div class="logo-text">扫描全能王<span class="tm">TM</span></div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 会员卡片背景 -->
      <div class="card-background"></div>
      
      <!-- 会员卡片 -->
      <div class="membership-card">
        <div class="card-info">
          <div class="info-item">
            <div class="label">帐户：</div>
            <div class="value"><EMAIL></div>
          </div>
          <div class="info-item">
            <div class="label">有效期至：</div>
            <div class="value">2026-09-06</div>
          </div>
        </div>
        <div class="card-decoration"></div>
      </div>
      
      <!-- 卡片阴影 -->
      <div class="card-shadow"></div>
    </div>

    <!-- 特权功能区域 -->
    <div class="privileges-section">
      <!-- 标题区域 -->
      <div class="privileges-title">
        <span class="star">★</span>
        <span class="title-text">尊享 20+ 高级会员特权</span>
        <span class="star">★</span>
      </div>

      <!-- 功能图标网格 -->
      <div class="features-grid">
        <div class="feature-item">
          <div class="feature-icon"></div>
          <span class="feature-text">证件扫描</span>
        </div>
        <div class="feature-item">
          <div class="feature-icon"></div>
          <span class="feature-text">PDF转换</span>
        </div>
        <div class="feature-item">
          <div class="feature-icon"></div>
          <span class="feature-text">文字识别</span>
        </div>
        <div class="feature-item">
          <div class="feature-icon"></div>
          <span class="feature-text">表格识别</span>
        </div>
        <div class="feature-item">
          <div class="feature-icon"></div>
          <span class="feature-text">Word转换</span>
        </div>
        <div class="feature-item">
          <div class="feature-icon"></div>
          <span class="feature-text">去水印</span>
        </div>
        <div class="feature-item">
          <div class="feature-icon"></div>
          <span class="feature-text">云端存储</span>
        </div>
        <div class="feature-item">
          <div class="feature-icon"></div>
          <span class="feature-text">去广告</span>
        </div>
      </div>
    </div>

    <!-- 立即体验按钮 -->
    <div class="action-section">
      <button class="experience-button">
        <span class="crown-icon">👑</span>
        <span class="button-text">高级会员兑换成功</span>
      </button>
    </div>

    <!-- 下载提示 -->
    <div class="download-hint">下载扫描全能王 App 查看高级会员特权</div>

    <!-- 底部导航栏 -->
    <div class="bottom-nav">
      <div class="nav-back">←</div>
      <button class="nav-button">完成</button>
    </div>

    <!-- Home指示器 -->
    <div class="home-indicator">
      <div class="indicator-bar"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RedemptionSuccess'
}
</script>

<style scoped>
.redemption-success-page {
  width: 375px;
  height: 812px;
  background: #1A1D29;
  position: relative;
  overflow: hidden;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  margin: 0 auto;
}

/* 状态栏 */
.status-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 375px;
  height: 44px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 21px;
  box-sizing: border-box;
}

.time {
  color: white;
  font-size: 14px;
  font-weight: 600;
}

.status-icons {
  display: flex;
  align-items: center;
  gap: 5px;
}

.signal, .wifi {
  width: 17px;
  height: 11px;
  background: white;
  border-radius: 1px;
}

.battery {
  width: 24px;
  height: 11px;
  border: 1px solid rgba(255,255,255,0.35);
  border-radius: 2.5px;
  background: white;
}

/* Logo区域 */
.logo-section {
  position: absolute;
  top: 104px;
  left: 120px;
  width: 134px;
  height: 32px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-icon {
  width: 27px;
  height: 27px;
  background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
  border-radius: 6px;
}

.logo-text {
  color: white;
  font-size: 16px;
  font-weight: 500;
}

.tm {
  font-size: 6px;
  opacity: 0.8;
  margin-left: 2px;
}

/* 主要内容区域 */
.main-content {
  position: absolute;
  top: 256px;
  left: 14px;
  width: 347px;
  height: 295px;
}

.card-background {
  position: absolute;
  top: 53px;
  left: 17.5px;
  width: 311px;
  height: 220px;
  background: #30384E;
  border-radius: 8px;
}

.membership-card {
  position: absolute;
  top: 0;
  left: 31.5px;
  width: 283px;
  height: 212px;
  background: linear-gradient(180deg, #FFF3CE 0%, #F6E1A7 100%);
  border-radius: 8px;
  border: 1px solid #E8CB91;
  padding: 24px 16px;
  box-sizing: border-box;
}

.card-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.label, .value {
  color: #1A1D29;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5;
}

.card-decoration {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 94px;
  height: 90px;
  background: linear-gradient(135deg, rgba(232, 203, 145, 0.4) 0%, rgba(232, 203, 145, 0) 100%);
  border: 1px solid rgba(232, 203, 145, 0.3);
  border-radius: 4px;
}

.card-shadow {
  position: absolute;
  top: 128px;
  left: 17.5px;
  width: 311px;
  height: 148px;
  background: radial-gradient(circle at center, #39425B 0%, #252B3D 100%);
  border-radius: 8px;
  filter: blur(4px);
  opacity: 0.8;
}

/* 特权功能区域 */
.privileges-section {
  position: absolute;
  top: 573px;
  left: 24px;
  width: 327px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.privileges-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.star {
  color: #FBDEBC;
  font-size: 12px;
}

.title-text {
  color: #D5B87F;
  font-size: 18px;
  font-weight: 500;
  line-height: 1.44;
  text-align: center;
}

.features-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 21px;
  width: 100%;
  justify-content: flex-start;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  width: 66px;
}

.feature-icon {
  width: 40px;
  height: 40px;
  background: #333949;
  border: 1px solid #5B6171;
  border-radius: 50%;
}

.feature-text {
  color: #D5B87F;
  font-size: 12px;
  font-weight: 400;
  line-height: 1.33;
  text-align: center;
  width: 66px;
}

/* 立即体验按钮 */
.action-section {
  position: absolute;
  top: 160px;
  left: 73.5px;
  display: flex;
  align-items: center;
}

.experience-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(180deg, #F6E2A7 0%, #CCAD74 100%);
  border: none;
  border-radius: 14px;
  padding: 8px 16px;
  cursor: pointer;
}

.crown-icon {
  font-size: 16px;
}

.button-text {
  color: #1A1D29;
  font-size: 24px;
  font-weight: 700;
  line-height: 1.5;
  font-family: 'HarmonyOS Sans SC', sans-serif;
}

/* 下载提示 */
.download-hint {
  position: absolute;
  top: 208px;
  left: 47px;
  width: 281px;
  height: 24px;
  color: #D5B87F;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.5;
  text-align: center;
  font-family: 'PingFang SC', sans-serif;
}

/* 底部导航栏 */
.bottom-nav {
  position: absolute;
  top: 44px;
  left: 0;
  width: 375px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  box-sizing: border-box;
}

.nav-back {
  color: white;
  font-size: 18px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-button {
  background: rgba(255, 255, 255, 0.2);
  border: 0.5px solid #EAEAEA;
  border-radius: 16px;
  padding: 6px 24px;
  color: #D8D8D8;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
}

/* Home指示器 */
.home-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 375px;
  height: 34px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.indicator-bar {
  width: 134px;
  height: 5px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2.5px;
}
</style>