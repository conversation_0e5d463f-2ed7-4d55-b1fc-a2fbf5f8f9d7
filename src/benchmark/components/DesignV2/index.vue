<template>
  <div id="DesignV2-test" class="design-v2">
    <div class="design-v2__layout">
      <div class="design-v2__frame">
        <div class="design-v2__square design-v2__square--gray"></div>
        <div class="design-v2__square design-v2__square--purple"></div>
      </div>
      <div class="design-v2__frame">
        <div class="design-v2__square design-v2__square--gray"></div>
        <div class="design-v2__square design-v2__square--gray"></div>
      </div>
      <div class="design-v2__group">
        <img :src="vectorImageSrc" alt="Vector" class="design-v2__vector" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 定义组件名称
defineOptions({
  name: 'DesignV2'
})

// 导入SVG图像
const vectorImageSrc = new URL('./vector.svg', import.meta.url).href
</script>

<style scoped>
.design-v2 {
  /* 根容器样式 - 对应 Design v2 节点 */
  box-sizing: border-box;
  width: 194px;
  height: 292px;
  border: 2px solid #000000;
  background: transparent;
}

.design-v2__layout {
  /* Layout 容器样式 - 垂直布局 */
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  width: fit-content;
  height: fit-content;
}

.design-v2__frame {
  /* Frame 容器样式 - 水平布局 */
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  gap: 12px;
}

.design-v2__square {
  /* 正方形基础样式 */
  box-sizing: border-box;
  width: 75px;
  height: 75px;
  flex-shrink: 0;
}

.design-v2__square--gray {
  /* 灰色正方形 */
  background-color: #D9D9D9;
}

.design-v2__square--purple {
  /* 紫色正方形 */
  background-color: #7B61FF;
}

.design-v2__group {
  /* Group 容器样式 */
  box-sizing: border-box;
  width: 162px;
  height: 77.25px;
  flex-shrink: 0;
}

.design-v2__vector {
  /* Vector 图像样式 */
  width: 162px;
  height: 77.25px;
  display: block;
}
</style>
