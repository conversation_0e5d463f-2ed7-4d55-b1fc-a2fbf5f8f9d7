<template>
  <div id="DesignV2Test-test" class="designv2test-component">
    <h2>{{ title }}</h2>
    <div class="content">
      <!-- Component content will be generated based on Figma data -->
      <div class="figma-frame" style="width: 194px; height: 292px">
        <div class="figma-frame" style="display: flex; flex-direction: column; gap: 16px; padding: 16px; position: relative; left: 0px; top: 0px">
        <div class="figma-frame" style="display: flex; flex-direction: row; gap: 12px">
        <div class="figma-rectangle" style="width: 75px; height: 75px; background-color: #D9D9D9"></div>
        <div class="figma-rectangle" style="width: 75px; height: 75px; background-color: #7B61FF"></div>
      </div>
        <div class="figma-frame" style="display: flex; flex-direction: row; gap: 12px">
        <div class="figma-rectangle" style="width: 75px; height: 75px; background-color: #D9D9D9"></div>
        <div class="figma-rectangle" style="width: 75px; height: 75px; background-color: #D9D9D9"></div>
      </div>
        <div class="figma-group" style="width: 162px; height: 77.25px">
        <div class="figma-image-svg" style="width: 162px; height: 77.25px; background-color: #FFFFFF; position: relative; left: 0px; top: 0px">
        <!-- SVG content would go here -->
        <svg width="100%" height="100%">
          <rect width="100%" height="100%" fill="currentColor"/>
        </svg>
      </div>
      </div>
      </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// Component props and data
const title = ref('DesignV2Test')

// Figma data
const figmaData = {
  "metadata": {
    "name": "Dev Mode playground (Community)",
    "lastModified": "2025-07-08T02:03:44Z",
    "thumbnailUrl": "https://s3-alpha.figma.com/thumbnails/caa23ac8-f6b9-466e-9b29-f6e90188d715?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAQ4GOSFWCU3RHYTUS%2F20250710%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250710T000000Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=10d793f997625b0d30f66de2691be31f19fa50129d8ac77a63356df830da8786"
  },
  "nodes": [
    {
      "id": "2836:1477",
      "name": "Design v2",
      "type": "FRAME",
      "strokes": "stroke_1FFZW7",
      "layout": "layout_Y29894",
      "children": [
        {
          "id": "2827:8516",
          "name": "Layout",
          "type": "FRAME",
          "layout": "layout_FCSOT3",
          "children": [
            {
              "id": "2827:8517",
              "name": "Frame 1",
              "type": "FRAME",
              "layout": "layout_M8DZ3X",
              "children": [
                {
                  "id": "2827:8518",
                  "name": "Square 1",
                  "type": "RECTANGLE",
                  "fills": "fill_A9WJOK",
                  "layout": "layout_OSSF6Q"
                },
                {
                  "id": "2827:8519",
                  "name": "Square 2",
                  "type": "RECTANGLE",
                  "fills": "fill_XUFDII",
                  "layout": "layout_OSSF6Q"
                }
              ]
            },
            {
              "id": "2827:8521",
              "name": "Frame 2",
              "type": "FRAME",
              "layout": "layout_M8DZ3X",
              "children": [
                {
                  "id": "2827:8522",
                  "name": "Square 1",
                  "type": "RECTANGLE",
                  "fills": "fill_A9WJOK",
                  "layout": "layout_OSSF6Q"
                },
                {
                  "id": "2827:8523",
                  "name": "Square 2",
                  "type": "RECTANGLE",
                  "fills": "fill_A9WJOK",
                  "layout": "layout_OSSF6Q"
                }
              ]
            },
            {
              "id": "5206:1365",
              "name": "Group 1321317659",
              "type": "GROUP",
              "layout": "layout_YLSD3D",
              "children": [
                {
                  "id": "5206:1368",
                  "name": "Vector",
                  "type": "IMAGE-SVG",
                  "fills": "fill_MPQ7HR",
                  "layout": "layout_D3AVHJ"
                }
              ]
            }
          ]
        }
      ]
    }
  ],
  "globalVars": {
    "styles": {
      "stroke_1FFZW7": {
        "colors": [
          "#000000"
        ],
        "strokeWeight": "2px"
      },
      "layout_Y29894": {
        "mode": "none",
        "dimensions": {
          "width": 194,
          "height": 292
        }
      },
      "layout_FCSOT3": {
        "mode": "column",
        "gap": "16px",
        "padding": "16px",
        "sizing": {
          "horizontal": "hug",
          "vertical": "hug"
        },
        "locationRelativeToParent": {
          "x": 0,
          "y": 0
        }
      },
      "layout_M8DZ3X": {
        "mode": "row",
        "gap": "12px",
        "sizing": {
          "horizontal": "hug",
          "vertical": "hug"
        }
      },
      "fill_A9WJOK": [
        "#D9D9D9"
      ],
      "layout_OSSF6Q": {
        "mode": "none",
        "sizing": {
          "horizontal": "fixed",
          "vertical": "fixed"
        },
        "dimensions": {
          "width": 75,
          "height": 75
        }
      },
      "fill_XUFDII": [
        "#7B61FF"
      ],
      "layout_YLSD3D": {
        "mode": "none",
        "sizing": {
          "horizontal": "fixed",
          "vertical": "fixed"
        },
        "dimensions": {
          "width": 162,
          "height": 77.25
        }
      },
      "fill_MPQ7HR": [
        "#FFFFFF"
      ],
      "layout_D3AVHJ": {
        "mode": "none",
        "locationRelativeToParent": {
          "x": 0,
          "y": 0
        },
        "dimensions": {
          "width": 162,
          "height": 77.25
        }
      }
    }
  }
}

// Component logic here
</script>

<style scoped>
.designv2test-component {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
}

.content {
  margin-top: 16px;
}

/* Styles based on Figma data will be generated here */

/* Base Figma component styles */
.figma-frame {
  box-sizing: border-box;
}

.figma-rectangle {
  box-sizing: border-box;
}

.figma-group {
  position: relative;
}

.figma-image-svg {
  display: flex;
  align-items: center;
  justify-content: center;
}

.stroke_1FFZW7 { border: 2px solid #000000; }
.fill_A9WJOK { background-color: #D9D9D9; }
.fill_XUFDII { background-color: #7B61FF; }
.fill_MPQ7HR { background-color: #FFFFFF; }
</style>