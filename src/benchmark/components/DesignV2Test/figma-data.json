{"metadata": {"name": "Dev Mode playground (Community)", "lastModified": "2025-07-08T02:03:44Z", "thumbnailUrl": "https://s3-alpha.figma.com/thumbnails/caa23ac8-f6b9-466e-9b29-f6e90188d715?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAQ4GOSFWCU3RHYTUS%2F20250710%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250710T000000Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=10d793f997625b0d30f66de2691be31f19fa50129d8ac77a63356df830da8786"}, "nodes": [{"id": "2836:1477", "name": "Design v2", "type": "FRAME", "strokes": "stroke_1FFZW7", "layout": "layout_Y29894", "children": [{"id": "2827:8516", "name": "Layout", "type": "FRAME", "layout": "layout_FCSOT3", "children": [{"id": "2827:8517", "name": "Frame 1", "type": "FRAME", "layout": "layout_M8DZ3X", "children": [{"id": "2827:8518", "name": "Square 1", "type": "RECTANGLE", "fills": "fill_A9WJOK", "layout": "layout_OSSF6Q"}, {"id": "2827:8519", "name": "Square 2", "type": "RECTANGLE", "fills": "fill_XUFDII", "layout": "layout_OSSF6Q"}]}, {"id": "2827:8521", "name": "Frame 2", "type": "FRAME", "layout": "layout_M8DZ3X", "children": [{"id": "2827:8522", "name": "Square 1", "type": "RECTANGLE", "fills": "fill_A9WJOK", "layout": "layout_OSSF6Q"}, {"id": "2827:8523", "name": "Square 2", "type": "RECTANGLE", "fills": "fill_A9WJOK", "layout": "layout_OSSF6Q"}]}, {"id": "5206:1365", "name": "Group 1321317659", "type": "GROUP", "layout": "layout_YLSD3D", "children": [{"id": "5206:1368", "name": "Vector", "type": "IMAGE-SVG", "fills": "fill_MPQ7HR", "layout": "layout_D3AVHJ"}]}]}]}], "globalVars": {"styles": {"stroke_1FFZW7": {"colors": ["#000000"], "strokeWeight": "2px"}, "layout_Y29894": {"mode": "none", "dimensions": {"width": 194, "height": 292}}, "layout_FCSOT3": {"mode": "column", "gap": "16px", "padding": "16px", "sizing": {"horizontal": "hug", "vertical": "hug"}, "locationRelativeToParent": {"x": 0, "y": 0}}, "layout_M8DZ3X": {"mode": "row", "gap": "12px", "sizing": {"horizontal": "hug", "vertical": "hug"}}, "fill_A9WJOK": ["#D9D9D9"], "layout_OSSF6Q": {"mode": "none", "sizing": {"horizontal": "fixed", "vertical": "fixed"}, "dimensions": {"width": 75, "height": 75}}, "fill_XUFDII": ["#7B61FF"], "layout_YLSD3D": {"mode": "none", "sizing": {"horizontal": "fixed", "vertical": "fixed"}, "dimensions": {"width": 162, "height": 77.25}}, "fill_MPQ7HR": ["#FFFFFF"], "layout_D3AVHJ": {"mode": "none", "locationRelativeToParent": {"x": 0, "y": 0}, "dimensions": {"width": 162, "height": 77.25}}}}}