import type { Component } from 'vue';

// 自动导入所有组件
const modules = import.meta.glob('./*/index.vue', { eager: true });

// 组件映射表
const BENCHMARK_COMPONENTS: Record<string, Component> = {};

// 注册组件
Object.entries(modules).forEach(([path, module]) => {
  // 从路径中提取组件名
  // 例如: './DocListItem/index.vue' -> 'DocListItem'
  const componentName = path.split('/')[1];
  BENCHMARK_COMPONENTS[componentName] = (module as any).default;
});

export { BENCHMARK_COMPONENTS };
export default BENCHMARK_COMPONENTS;

// 导出组件配置，用于测试
export const BENCHMARK_CONFIG = Object.keys(BENCHMARK_COMPONENTS).map(name => ({
  name,
  component: BENCHMARK_COMPONENTS[name],
  // 每个组件的测试配置
  test: {
    selector: `#${name}-test`,
    viewport: {
      width: 1920,
      height: 1080
    }
  }
})); 