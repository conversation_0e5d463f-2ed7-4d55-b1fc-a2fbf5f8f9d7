<template>
  <div class="compare-page">
    <div class="header">
      <div class="header-top">
        <div class="header-title">
          <h1>🎯 Figma 组件还原度对比</h1>
          <div v-if="filterComponent" class="filter-info">
            <span class="filter-tag">🔍 正在显示: {{ filterComponent }}</span>
            <button class="btn-clear-filter" @click="clearFilter" title="显示所有组件">
              ✕ 清除过滤
            </button>
          </div>
          <div v-else class="filter-info">
            <span class="filter-tag">📊 显示所有组件</span>
          </div>
        </div>
        <div class="header-actions">
          <div class="search-controls">
            <input 
              type="text" 
              v-model="searchQuery" 
              placeholder="🔍 搜索组件..."
              class="search-input"
              @input="onSearchInput"
            />
            <select v-model="selectedComponent" @change="onComponentSelect" class="component-select">
              <option value="">全部组件</option>
              <option v-for="component in allComponentResults" :key="component.name" :value="component.name">
                {{ component.name }} ({{ component.score }}%)
              </option>
            </select>
          </div>
          <button class="btn-back" @click="goBack">
            ← 返回组件测试
          </button>
        </div>
      </div>
      <div class="stats">
        <div class="stat-item">
          <span class="label">{{ filterComponent ? '筛选结果' : '组件总数' }}:</span>
          <span class="value">{{ componentResults.length }}{{ filterComponent ? ' / ' + allComponentResults.length : '' }}</span>
        </div>
        <div class="stat-item">
          <span class="label">平均还原度:</span>
          <span class="value">{{ averageScore }}%</span>
        </div>
        <div class="stat-item">
          <span class="label">最高还原度:</span>
          <span class="value">{{ maxScore }}%</span>
        </div>
      </div>
    </div>

    <div class="component-grid">
      <div 
        v-for="component in componentResults" 
        :key="component.name"
        class="component-card"
        :class="getQualityClass(component.score)"
      >
        <div class="component-header">
          <h3>{{ component.name }}</h3>
          <div class="score-badge" :class="getScoreClass(component.score)">
            {{ component.score }}%
          </div>
        </div>

        <div class="image-comparison">
          <!-- Before/After 滑动对比 -->
          <div class="image-section slider-section" v-if="component.expectedImage && component.actualImage">
            <div class="section-header">
              <h4>🔄 滑动对比 (左:预期 | 右:实际)</h4>
              <div class="view-controls">
                <button @click="setViewMode(component.name, 'slider')" 
                        :class="{ active: getViewMode(component.name) === 'slider' }">
                  🔄 滑动对比
                </button>
                <button @click="setViewMode(component.name, 'grid')" 
                        :class="{ active: getViewMode(component.name) === 'grid' }">
                  🔳 分格对比
                </button>
              </div>
            </div>
            
            <!-- 滑动对比器 -->
            <div v-if="getViewMode(component.name) === 'slider'" 
                 class="before-after-slider" 
                 :id="`slider-${component.name}`">
              <div class="slider-container">
                <div class="image-wrapper">
                  <img class="before-image" :src="component.expectedImage" :alt="`${component.name} 预期效果`" />
                  <div class="after-overlay" :style="{ clipPath: `inset(0 0 0 ${getSliderPosition(component.name)}%)` }">
                    <img class="after-image" :src="component.actualImage" :alt="`${component.name} 实际效果`" />
                  </div>
                </div>
                
                <!-- 滑动控制器 -->
                <div class="slider-control" 
                     :style="{ left: `${getSliderPosition(component.name)}%` }"
                     @mousedown="startDragging(component.name, $event)"
                     @touchstart="startDragging(component.name, $event)">
                  <div class="slider-line"></div>
                  <div class="slider-handle">
                    <div class="handle-arrow handle-left">◀</div>
                    <div class="handle-grip">⋮⋮</div>
                    <div class="handle-arrow handle-right">▶</div>
                  </div>
                </div>
                
                <!-- 标签 -->
                <div class="image-labels">
                  <span class="label-left">预期效果</span>
                  <span class="label-right">实际效果</span>
                </div>
              </div>
            </div>
            
            <!-- 分格对比 -->
            <div v-else class="grid-comparison">
              <div class="grid-item">
                <h5>📋 预期效果</h5>
                <div class="large-image-container" @click="openPreview(component.expectedImage, `${component.name} 预期效果`)">
                  <img :src="component.expectedImage" :alt="`${component.name} 预期效果`" />
                  <div class="preview-hint">🔍 点击预览</div>
                </div>
              </div>
              
              <div class="grid-item">
                <h5>🖥️ 实际效果</h5>
                <div class="large-image-container" @click="openPreview(component.actualImage, `${component.name} 实际效果`)">
                  <img :src="component.actualImage" :alt="`${component.name} 实际效果`" />
                  <div class="preview-hint">🔍 点击预览</div>
                </div>
              </div>
              
              <div class="grid-item" v-if="component.diffImage">
                <h5>🔍 差异分析</h5>
                <div class="large-image-container" @click="openPreview(component.diffImage, `${component.name} 差异对比`)">
                  <img :src="component.diffImage" :alt="`${component.name} 差异对比`" />
                  <div class="preview-hint">🔍 点击预览</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 单一图片显示 (当没有完整对比时) -->
          <div v-else class="single-images">
            <div class="image-section" v-if="component.expectedImage">
              <h4>📋 预期效果</h4>
              <div class="large-image-container" @click="openPreview(component.expectedImage, `${component.name} 预期效果`)">
                <img :src="component.expectedImage" :alt="`${component.name} 预期效果`" />
                <div class="preview-hint">🔍 点击预览</div>
              </div>
            </div>

            <div class="image-section" v-if="component.actualImage">
              <h4>🖥️ 实际效果</h4>
              <div class="large-image-container" @click="openPreview(component.actualImage, `${component.name} 实际效果`)">
                <img :src="component.actualImage" :alt="`${component.name} 实际效果`" />
                <div class="preview-hint">🔍 点击预览</div>
              </div>
            </div>

            <div class="image-section" v-if="component.diffImage">
              <h4>🔍 差异对比</h4>
              <div class="large-image-container" @click="openPreview(component.diffImage, `${component.name} 差异对比`)">
                <img :src="component.diffImage" :alt="`${component.name} 差异对比`" />
                <div class="preview-hint">🔍 点击预览</div>
              </div>
            </div>
            
            <div v-if="!component.expectedImage && !component.actualImage && !component.diffImage" class="no-images">
              <div class="empty-state">
                <span>📷</span>
                <p>暂无图片资源</p>
              </div>
            </div>
          </div>
        </div>

        <div class="component-details">
          <div class="detail-row">
            <span class="label">总像素数:</span>
            <span class="value">{{ component.totalPixels?.toLocaleString() || 'N/A' }}</span>
          </div>
          <div class="detail-row">
            <span class="label">差异像素:</span>
            <span class="value">{{ component.diffPixels?.toLocaleString() || 'N/A' }}</span>
          </div>
          <div class="detail-row">
            <span class="label">尺寸:</span>
            <span class="value">{{ component.dimensions || 'N/A' }}</span>
          </div>
          <div class="detail-row">
            <span class="label">质量等级:</span>
            <span class="value grade" :class="getScoreClass(component.score)">
              {{ getQualityGrade(component.score) }}
            </span>
          </div>
        </div>

        <div class="actions">
          <button class="btn-test" @click="runTest(component.name)">
            🔄 重新测试
          </button>
          <button class="btn-view" @click="viewComponent(component.name)">
            👁️ 查看组件
          </button>
          <button class="btn-ai-analyze" @click="analyzeWithAI(component.name)" 
                  :disabled="analyzingComponents.has(component.name)">
            <span v-if="analyzingComponents.has(component.name)">🤖 分析中...</span>
            <span v-else>🤖 AI分析</span>
          </button>
          <button 
            v-if="component.expectedNodeStructure || component.actualNodeStructure"
            class="btn-node-structure" 
            @click="toggleNodeStructure(component.name)"
            :class="{ active: showNodeStructure[component.name] }"
          >
            <span v-if="showNodeStructure[component.name]">🏗️ 隐藏结构</span>
            <span v-else>🏗️ 查看结构</span>
          </button>
        </div>

        <!-- AI分析结果 -->
        <div v-if="component.aiAnalysis" class="ai-analysis">
          <div class="analysis-header">
            <h5>🤖 AI 智能分析</h5>
            <span class="analysis-time">{{ formatTime(component.aiAnalysis.timestamp) }}</span>
          </div>
          <div class="analysis-content" v-html="formatAnalysis(component.aiAnalysis.analysis)"></div>
        </div>
      </div>
    </div>

    <div v-if="loading" class="loading">
      <div class="spinner"></div>
      <span>加载组件数据中...</span>
    </div>

    <div v-if="componentResults.length === 0 && !loading && allComponentResults.length === 0" class="empty-state">
      <div class="empty-icon">📊</div>
      <h3>暂无测试结果</h3>
      <p>请先运行组件测试以查看还原度对比</p>
    </div>

    <div v-if="componentResults.length === 0 && !loading && allComponentResults.length > 0" class="empty-state">
      <div class="empty-icon">🔍</div>
      <h3>没有找到匹配的组件</h3>
      <p v-if="filterComponent">组件 "{{ filterComponent }}" 不存在或还没有测试结果</p>
      <p v-else-if="searchQuery">没有组件名称包含 "{{ searchQuery }}"</p>
      <p v-else>当前筛选条件下没有可显示的组件</p>
      <button class="btn-primary" @click="clearAllFilters">
        🔄 清除所有筛选
      </button>
    </div>

    <!-- 图片预览模态框 -->
    <div v-if="previewImage.show" class="preview-modal" @click="closePreview">
      <div class="preview-backdrop"></div>
      <div class="preview-content" @click.stop>
        <div class="preview-header">
          <h3>{{ previewImage.title }}</h3>
          <button class="btn-close" @click="closePreview">×</button>
        </div>
        <div class="preview-body">
          <img :src="previewImage.src" :alt="previewImage.title" />
        </div>
        <div class="preview-controls">
          <button @click="zoomOut">🔍-</button>
          <span>{{ Math.round(previewZoom * 100) }}%</span>
          <button @click="zoomIn">🔍+</button>
          <button @click="resetZoom">重置</button>
        </div>
      </div>
    </div>

    <!-- API配置弹窗 -->
    <div v-if="showApiConfig" class="config-modal">
      <div class="modal-backdrop" @click="showApiConfig = false"></div>
      <div class="modal-content">
        <div class="modal-header">
          <h3>🤖 AI分析配置</h3>
          <button class="btn-close" @click="showApiConfig = false">×</button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label for="apiKey">OpenAI API Key:</label>
            <input 
              id="apiKey"
              type="password" 
              v-model="apiKey" 
              placeholder="请输入你的 OpenAI API Key"
              class="api-input"
            />
            <div class="form-help">
              <p>需要 OpenAI API Key 来使用 GPT-4V 进行图片分析</p>
              <p>获取地址: <a href="https://platform.openai.com/api-keys" target="_blank">OpenAI API Keys</a></p>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn-cancel" @click="showApiConfig = false">取消</button>
          <button class="btn-save" @click="saveApiKey">保存</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import '@/pages/control/element-plus-theme.scss'
import { ref, onMounted, computed, watch } from 'vue'
import { defineOptions } from 'vue'
import { useRouter, useRoute } from 'vue-router'

defineOptions({
  name: 'ComparePage'
})

interface ComponentResult {
  name: string
  score: number
  totalPixels?: number
  diffPixels?: number
  dimensions?: string
  expectedImage?: string
  actualImage?: string
  diffImage?: string
  aiAnalysis?: {
    analysis: string
    timestamp: string
    success: boolean
  }
  expectedNodeStructure?: any
  actualNodeStructure?: any
}

const router = useRouter()
const route = useRoute()
const componentResults = ref<ComponentResult[]>([])
const allComponentResults = ref<ComponentResult[]>([]) // 存储所有组件结果
const loading = ref(true)
const analyzingComponents = ref(new Set<string>())
const showApiConfig = ref(false)
const apiKey = ref(localStorage.getItem('openai_api_key') || '')

// 获取路由参数中的组件名
const filterComponent = computed(() => route.params.component as string || '')

// 搜索和过滤状态
const searchQuery = ref('')
const selectedComponent = ref('')

// 滑动对比相关状态
const sliderPositions = ref<Record<string, number>>({})
const viewModes = ref<Record<string, 'slider' | 'grid'>>({})
const isDragging = ref(false)
const currentDragComponent = ref('')

// 图片预览相关状态
const previewImage = ref({
  show: false,
  src: '',
  title: ''
})
const previewZoom = ref(1)

// Node结构显示状态
const showNodeStructure = ref<Record<string, boolean>>({})

// 计算平均还原度
const averageScore = computed(() => {
  if (componentResults.value.length === 0) return 0
  const sum = componentResults.value.reduce((acc, comp) => acc + comp.score, 0)
  return Math.round((sum / componentResults.value.length) * 100) / 100
})

// 计算最高还原度
const maxScore = computed(() => {
  if (componentResults.value.length === 0) return 0
  return Math.max(...componentResults.value.map(comp => comp.score))
})

// 获取质量等级
const getQualityGrade = (score: number): string => {
  if (score >= 98) return 'A+'
  if (score >= 95) return 'A'
  if (score >= 90) return 'B+'
  if (score >= 80) return 'B'
  return 'C'
}

// 获取分数样式类
const getScoreClass = (score: number): string => {
  if (score >= 98) return 'excellent'
  if (score >= 95) return 'good'
  if (score >= 90) return 'fair'
  if (score >= 80) return 'poor'
  return 'bad'
}

// 获取质量样式类
const getQualityClass = (score: number): string => {
  return `quality-${getScoreClass(score)}`
}

// 自动发现所有组件
const discoverComponents = async () => {
  try {
    // 使用import.meta.glob来发现所有组件
    const componentModules = import.meta.glob('/src/benchmark/components/*/index.vue', { eager: false })
    const componentNames = Object.keys(componentModules).map(path => {
      const match = path.match(/\/src\/benchmark\/components\/([^\/]+)\/index\.vue$/)
      return match ? match[1] : null
    }).filter(Boolean)
    
    return componentNames
  } catch (error) {
    console.error('发现组件失败:', error)
    return []
  }
}

// 检查图片是否存在
const checkImageExists = async (url: string): Promise<boolean> => {
  try {
    const response = await fetch(url, { method: 'HEAD' })
    return response.ok
  } catch {
    return false
  }
}

// 加载单个组件的测试结果
const loadComponentResult = async (componentName: string) => {
  try {
    let resultData = null
    
    // 尝试加载测试结果JSON文件
    try {
      const response = await fetch(`/src/benchmark/results/${componentName}/result.json`)
      if (response.ok) {
        resultData = await response.json()
      }
    } catch (error) {
      console.log(`组件 ${componentName} 没有测试结果文件`)
    }
    
    // 检查图片文件是否存在
    const expectedImageUrl = `/src/benchmark/components/${componentName}/expected.png`
    const actualImageUrl = `/src/benchmark/results/${componentName}/actual.png`
    const diffImageUrl = `/src/benchmark/results/${componentName}/diff.png`
    
    const [hasExpected, hasActual, hasDiff] = await Promise.all([
      checkImageExists(expectedImageUrl),
      checkImageExists(actualImageUrl),
      checkImageExists(diffImageUrl)
    ])

    // 加载Node结构数据
    let expectedNodeStructure = null
    let actualNodeStructure = null
    
    try {
      // 尝试加载预期Node结构
      const expectedNodeResponse = await fetch(`/src/benchmark/components/${componentName}/expected-node-structure.json`)
      if (expectedNodeResponse.ok) {
        expectedNodeStructure = await expectedNodeResponse.json()
      }
    } catch (error) {
      console.log(`组件 ${componentName} 没有预期Node结构文件`)
    }
    
    try {
      // 尝试加载实际Node结构
      const actualNodeResponse = await fetch(`/src/benchmark/tools/results/${componentName}/actual-node-structure.json`)
      if (actualNodeResponse.ok) {
        actualNodeStructure = await actualNodeResponse.json()
      }
    } catch (error) {
      console.log(`组件 ${componentName} 没有实际Node结构文件`)
    }
    
    return {
      name: componentName,
      score: resultData?.matchPercentage || 0,
      totalPixels: resultData?.totalPixels || 0,
      diffPixels: resultData?.diffPixels || 0,
      dimensions: resultData?.dimensions ? `${resultData.dimensions.width}×${resultData.dimensions.height}` : 'N/A',
      expectedImage: hasExpected ? expectedImageUrl : undefined,
      actualImage: hasActual ? actualImageUrl : undefined,
      diffImage: hasDiff ? diffImageUrl : undefined,
      timestamp: resultData?.timestamp,
      hasTestResult: !!resultData,
      expectedNodeStructure,
      actualNodeStructure
    }
  } catch (error) {
    console.warn(`加载组件 ${componentName} 的结果失败:`, error)
    return {
      name: componentName,
      score: 0,
      totalPixels: 0,
      diffPixels: 0,
      dimensions: 'N/A',
      expectedImage: undefined,
      actualImage: undefined,
      diffImage: undefined,
      hasTestResult: false,
      expectedNodeStructure: undefined,
      actualNodeStructure: undefined
    }
  }
}

// 加载组件数据
const loadComponentData = async () => {
  try {
    loading.value = true
    
    // 自动发现所有组件
    const componentNames = await discoverComponents()
    console.log('发现的组件:', componentNames)
    
    // 并行加载所有组件的测试结果
    const results = await Promise.all(
      componentNames.filter(Boolean).map(name => loadComponentResult(name!))
    )
    
    // 按还原度从高到低排序并存储所有结果
    allComponentResults.value = results.sort((a, b) => b.score - a.score)
    
    // 应用过滤
    applyFilter()

  } catch (error) {
    console.error('加载组件数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 应用过滤逻辑
const applyFilter = () => {
  if (allComponentResults.value.length === 0) {
    return // 如果还没有加载数据，则不执行过滤
  }
  
  let filtered = [...allComponentResults.value]
  
  // 路由参数过滤
  if (filterComponent.value) {
    filtered = filtered.filter(component => 
      component.name.toLowerCase() === filterComponent.value.toLowerCase()
    )
  }
  
  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(component =>
      component.name.toLowerCase().includes(query)
    )
  }
  
  // 下拉选择过滤
  if (selectedComponent.value && !filterComponent.value) {
    filtered = filtered.filter(component =>
      component.name === selectedComponent.value
    )
  }
  
  componentResults.value = filtered
}

// 运行单个测试
const runTest = async (componentName: string) => {
  try {
    console.log(`重新测试组件: ${componentName}`)
    // 这里可以调用测试 API
    alert(`开始重新测试 ${componentName} 组件`)
  } catch (error) {
    console.error('测试失败:', error)
  }
}

// 查看组件
const viewComponent = (componentName: string) => {
  const url = `/benchmark/${componentName}`
  window.open(url, '_blank')
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 清除过滤器
const clearFilter = () => {
  router.push('/benchmark-compare')
}

// 搜索输入处理
const onSearchInput = () => {
  selectedComponent.value = '' // 清除下拉选择
  applyFilter()
}

// 下拉选择处理
const onComponentSelect = () => {
  if (selectedComponent.value) {
    searchQuery.value = '' // 清除搜索框
    router.push(`/benchmark-compare/${selectedComponent.value}`)
  } else {
    router.push('/benchmark-compare')
  }
}

// 清除所有筛选
const clearAllFilters = () => {
  searchQuery.value = ''
  selectedComponent.value = ''
  router.push('/benchmark-compare')
}

// AI分析组件
const analyzeWithAI = async (componentName: string) => {
  const currentApiKey = localStorage.getItem('openai_api_key')
  
  if (!currentApiKey) {
    alert('请先配置 OpenAI API Key')
    showApiConfig.value = true
    return
  }
  
  try {
    analyzingComponents.value.add(componentName)
    
    // 模拟调用AI分析工具
    // 实际项目中，这里应该调用后端API或直接调用AI分析脚本
    const mockAnalysis = `
# 🔍 问题诊断

## 主要问题 (影响还原度的关键因素)
1. **布局对齐问题**：组件元素的水平/垂直对齐存在偏差，特别是在容器内的定位
2. **尺寸比例不匹配**：某些元素的宽高比例与设计稿不完全一致
3. **间距计算偏差**：padding、margin值与设计稿存在细微差异

## 次要问题 (细节优化项)
1. **字体渲染差异**：浏览器字体渲染与Figma显示存在轻微差异
2. **颜色细节**：某些颜色值可能需要微调以匹配设计稿
3. **圆角半径**：border-radius值需要精确匹配

# 🛠️ 修复建议

## CSS 修改建议
\`\`\`css
.${componentName.toLowerCase()}-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 12px 16px;
  gap: 8px;
}

.${componentName.toLowerCase()}-item {
  width: 100%;
  height: auto;
  border-radius: 8px;
}
\`\`\`

## Vue 组件修改建议
\`\`\`vue
<template>
  <div class="${componentName.toLowerCase()}-wrapper">
    <div class="${componentName.toLowerCase()}-content">
      <!-- 建议使用Flexbox重新布局 -->
    </div>
  </div>
</template>
\`\`\`

# 📊 预期改进效果
- 修复后预计还原度：99%+
- 主要改进区域：布局对齐、尺寸匹配、间距调整

**建议优先处理主要问题中的布局对齐，这将显著提升还原度。**
    `
    
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    // 更新组件的AI分析结果
    const componentIndex = componentResults.value.findIndex(c => c.name === componentName)
    if (componentIndex !== -1) {
      componentResults.value[componentIndex].aiAnalysis = {
        analysis: mockAnalysis,
        timestamp: new Date().toISOString(),
        success: true
      }
    }
    
    alert('AI分析完成！请查看分析结果。')
    
  } catch (error) {
    console.error('AI分析失败:', error)
    alert('AI分析失败，请检查网络连接和API配置')
  } finally {
    analyzingComponents.value.delete(componentName)
  }
}

// 格式化分析结果
const formatAnalysis = (analysis: string) => {
  // 将markdown转换为HTML
  return analysis
    .replace(/## (.*)/g, '<h4>$1</h4>')
    .replace(/### (.*)/g, '<h5>$1</h5>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/```css\n([\s\S]*?)\n```/g, '<pre class="code-block css"><code>$1</code></pre>')
    .replace(/```vue\n([\s\S]*?)\n```/g, '<pre class="code-block vue"><code>$1</code></pre>')
    .replace(/```([\s\S]*?)```/g, '<pre class="code-block"><code>$1</code></pre>')
    .replace(/- (.*)/g, '<li>$1</li>')
    .replace(/\n\n/g, '<br><br>')
    .replace(/\n/g, '<br>')
}

// 格式化时间
const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 保存API Key
const saveApiKey = () => {
  if (!apiKey.value.trim()) {
    alert('请输入有效的 API Key')
    return
  }
  
  localStorage.setItem('openai_api_key', apiKey.value.trim())
  showApiConfig.value = false
  alert('API Key 已保存')
}

// 滑动对比功能
const getSliderPosition = (componentName: string): number => {
  return sliderPositions.value[componentName] || 50
}

const setSliderPosition = (componentName: string, position: number) => {
  sliderPositions.value[componentName] = Math.max(0, Math.min(100, position))
}

const getViewMode = (componentName: string): 'slider' | 'grid' => {
  return viewModes.value[componentName] || 'slider'
}

const setViewMode = (componentName: string, mode: 'slider' | 'grid') => {
  viewModes.value[componentName] = mode
}

// 开始拖拽
const startDragging = (componentName: string, event: MouseEvent | TouchEvent) => {
  event.preventDefault()
  isDragging.value = true
  currentDragComponent.value = componentName
  
  const handleMove = (e: MouseEvent | TouchEvent) => {
    if (!isDragging.value) return
    
    const slider = document.getElementById(`slider-${componentName}`)
    if (!slider) return
    
    const rect = slider.getBoundingClientRect()
    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX
    const position = ((clientX - rect.left) / rect.width) * 100
    
    setSliderPosition(componentName, position)
  }
  
  const handleEnd = () => {
    isDragging.value = false
    currentDragComponent.value = ''
    document.removeEventListener('mousemove', handleMove)
    document.removeEventListener('mouseup', handleEnd)
    document.removeEventListener('touchmove', handleMove)
    document.removeEventListener('touchend', handleEnd)
  }
  
  document.addEventListener('mousemove', handleMove)
  document.addEventListener('mouseup', handleEnd)
  document.addEventListener('touchmove', handleMove)
  document.addEventListener('touchend', handleEnd)
}

// 图片预览功能
const openPreview = (src: string, title: string) => {
  previewImage.value = {
    show: true,
    src,
    title
  }
  previewZoom.value = 1
}

const closePreview = () => {
  previewImage.value.show = false
  previewZoom.value = 1
}

const zoomIn = () => {
  previewZoom.value = Math.min(previewZoom.value + 0.25, 3)
}

const zoomOut = () => {
  previewZoom.value = Math.max(previewZoom.value - 0.25, 0.25)
}

const resetZoom = () => {
  previewZoom.value = 1
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
  const placeholder = img.parentElement?.querySelector('.image-placeholder')
  if (placeholder) {
    (placeholder as HTMLElement).style.display = 'flex'
  }
}

// 监听路由变化
watch(() => route.params.component, (newComponent) => {
  if (newComponent) {
    // 路由参数变化时清除其他过滤
    searchQuery.value = ''
    selectedComponent.value = newComponent as string
  } else {
    selectedComponent.value = ''
  }
  applyFilter()
}, { immediate: true })

// 监听搜索框变化
watch(searchQuery, () => {
  if (!searchQuery.value) {
    applyFilter()
  }
})

// Node结构显示切换
const toggleNodeStructure = (componentName: string) => {
  showNodeStructure.value[componentName] = !showNodeStructure.value[componentName]
}

onMounted(() => {
  loadComponentData()
})
</script>

<style lang="scss" scoped>
.compare-page {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 24px;
}

.header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .header-top {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
    gap: 20px;

    .header-title {
      flex: 1;
      
      h1 {
        margin: 0 0 8px 0;
        font-size: 28px;
        font-weight: 600;
        color: #1a1a1a;
      }
      
      .filter-info {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .filter-tag {
          font-size: 14px;
          color: #6b7280;
          background: #f3f4f6;
          padding: 4px 12px;
          border-radius: 16px;
          font-weight: 500;
        }
        
        .btn-clear-filter {
          padding: 4px 8px;
          background: #ef4444;
          color: white;
          border: none;
          border-radius: 4px;
          font-size: 12px;
          cursor: pointer;
          transition: background-color 0.2s ease;
          
          &:hover {
            background: #dc2626;
          }
        }
      }
    }

    .header-actions {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      
      .search-controls {
        display: flex;
        gap: 8px;
        align-items: center;
        
        .search-input {
          padding: 8px 12px;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          font-size: 14px;
          min-width: 200px;
          
          &:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
          }
        }
        
        .component-select {
          padding: 8px 12px;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          font-size: 14px;
          min-width: 180px;
          background: white;
          cursor: pointer;
          
          &:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
          }
        }
      }

      .btn-config {
        padding: 8px 16px;
        background: #8b5cf6;
        color: white;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
          background: #7c3aed;
        }
      }

      .btn-back {
        padding: 8px 16px;
        background: #6b7280;
        color: white;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
          background: #4b5563;
        }
      }
    }
  }

  .stats {
    display: flex;
    gap: 24px;
    flex-wrap: wrap;

    .stat-item {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .label {
        font-size: 14px;
        color: #666;
      }

      .value {
        font-size: 24px;
        font-weight: 600;
        color: #1a1a1a;
      }
    }
  }
  
  // 响应式布局
  @media (max-width: 768px) {
    .header-top {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;
      
      .header-actions {
        .search-controls {
          flex-direction: column;
          
          .search-input, .component-select {
            min-width: 100%;
          }
        }
      }
    }
  }
}

.component-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(800px, 1fr));
  gap: 24px;
}

.component-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  &.quality-excellent { border-left: 4px solid #10b981; }
  &.quality-good { border-left: 4px solid #3b82f6; }
  &.quality-fair { border-left: 4px solid #f59e0b; }
  &.quality-poor { border-left: 4px solid #ef4444; }
  &.quality-bad { border-left: 4px solid #dc2626; }
}

.component-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;

  h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #1a1a1a;
  }

  .score-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 14px;

    &.excellent { background: #d1fae5; color: #065f46; }
    &.good { background: #dbeafe; color: #1e40af; }
    &.fair { background: #fef3c7; color: #92400e; }
    &.poor { background: #fee2e2; color: #991b1b; }
    &.bad { background: #fecaca; color: #7f1d1d; }
  }
}

.image-comparison {
  background: white;
  padding: 20px;
}

// 滑动对比样式
.slider-section {
  width: 100%;
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h4 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #1a1a1a;
    }
    
    .view-controls {
      display: flex;
      gap: 8px;
      
      button {
        padding: 8px 16px;
        border: 1px solid #d1d5db;
        background: white;
        border-radius: 6px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;
        
        &:hover {
          border-color: #3b82f6;
          color: #3b82f6;
        }
        
        &.active {
          background: #3b82f6;
          color: white;
          border-color: #3b82f6;
        }
      }
    }
  }
}

.before-after-slider {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  
  .slider-container {
    position: relative;
    width: 100%;
    height: 500px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    
    .image-wrapper {
      position: relative;
      width: 100%;
      height: 100%;
    }
    
    .before-image, .after-image {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: contain;
      user-select: none;
      pointer-events: none;
      border: 1px solid #d1d5db;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    .after-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }
    
    .slider-control {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 3px;
      background: #3b82f6;
      cursor: ew-resize;
      z-index: 10;
      transform: translateX(-50%);
      
      .slider-line {
        width: 100%;
        height: 100%;
        background: #3b82f6;
        box-shadow: 0 0 8px rgba(59, 130, 246, 0.3);
      }
      
      .slider-handle {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 40px;
        height: 40px;
        background: white;
        border: 3px solid #3b82f6;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        
        .handle-grip {
          font-size: 12px;
          color: #3b82f6;
          font-weight: bold;
        }
        
        .handle-arrow {
          position: absolute;
          font-size: 8px;
          color: #3b82f6;
          
          &.handle-left {
            left: -15px;
          }
          
          &.handle-right {
            right: -15px;
          }
        }
      }
    }
    
    .image-labels {
      position: absolute;
      top: 16px;
      left: 16px;
      right: 16px;
      display: flex;
      justify-content: space-between;
      pointer-events: none;
      
      .label-left, .label-right {
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
}

// 分格对比样式
.grid-comparison {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  
  .grid-item {
    h5 {
      margin: 0 0 12px 0;
      font-size: 16px;
      font-weight: 600;
      color: #374151;
    }
  }
}

// 大图容器样式
.large-image-container {
  position: relative;
  width: 100%;
  min-height: 300px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #f9fafb;
  
  &:hover {
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    
    .preview-hint {
      opacity: 1;
    }
  }
  
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    display: block;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .preview-hint {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    opacity: 0;
    transition: opacity 0.2s ease;
    pointer-events: none;
  }
}

// 单一图片显示
.single-images {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  
  .image-section {
    h4 {
      margin: 0 0 12px 0;
      font-size: 16px;
      font-weight: 600;
      color: #374151;
    }
  }
  
  .no-images {
    grid-column: 1 / -1;
    
    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #6b7280;
      
      span {
        font-size: 48px;
        display: block;
        margin-bottom: 16px;
      }
      
      p {
        font-size: 16px;
        margin: 0;
      }
    }
  }
}

// 图片预览模态框样式
.preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .preview-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
  }
  
  .preview-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    
    .preview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid #e5e7eb;
      background: #f9fafb;
      
      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #1a1a1a;
      }
      
      .btn-close {
        width: 32px;
        height: 32px;
        border: none;
        background: #ef4444;
        color: white;
        border-radius: 50%;
        font-size: 18px;
        font-weight: bold;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background-color 0.2s ease;
        
        &:hover {
          background: #dc2626;
        }
      }
    }
    
    .preview-body {
      padding: 20px;
      text-align: center;
      max-height: 70vh;
      overflow: auto;
      
      img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
        transform: scale(v-bind(previewZoom));
        transition: transform 0.2s ease;
        border: 1px solid #d1d5db;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }
    
    .preview-controls {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 12px;
      padding: 16px 20px;
      border-top: 1px solid #e5e7eb;
      background: #f9fafb;
      
      button {
        padding: 8px 12px;
        border: 1px solid #d1d5db;
        background: white;
        border-radius: 6px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;
        
        &:hover {
          border-color: #3b82f6;
          color: #3b82f6;
        }
      }
      
      span {
        font-size: 14px;
        font-weight: 500;
        color: #374151;
        min-width: 60px;
        text-align: center;
      }
    }
  }
}

.image-section {
  background: white;
  padding: 16px;

  h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
  }

  .image-container {
    position: relative;
    width: 100%;
    height: 200px;
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
      border: 1px solid #d1d5db;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .image-placeholder {
      display: none;
      align-items: center;
      justify-content: center;
      color: #9ca3af;
      font-size: 14px;
    }
  }
}

.component-details {
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;

  .detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      font-size: 14px;
      color: #666;
    }

    .value {
      font-size: 14px;
      font-weight: 500;
      color: #1a1a1a;

      &.grade {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;

        &.excellent { background: #d1fae5; color: #065f46; }
        &.good { background: #dbeafe; color: #1e40af; }
        &.fair { background: #fef3c7; color: #92400e; }
        &.poor { background: #fee2e2; color: #991b1b; }
        &.bad { background: #fecaca; color: #7f1d1d; }
      }
    }
  }
}

.actions {
  padding: 16px 24px;
  display: flex;
  gap: 12px;

  button {
    padding: 8px 16px;
    border-radius: 6px;
    border: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;

    &.btn-test {
      background: #f3f4f6;
      color: #374151;

      &:hover {
        background: #e5e7eb;
      }
    }

    &.btn-view {
      background: #3b82f6;
      color: white;

      &:hover {
        background: #2563eb;
      }
    }

    &.btn-ai-analyze {
      background: #8b5cf6;
      color: white;

      &:hover:not(:disabled) {
        background: #7c3aed;
      }

      &:disabled {
        background: #9ca3af;
        cursor: not-allowed;
      }
    }

    &.btn-node-structure {
      background: #059669;
      color: white;

      &:hover {
        background: #047857;
      }

      &.active {
        background: #dc2626;

        &:hover {
          background: #b91c1c;
        }
      }
    }

    &.btn-primary {
      background: #10b981;
      color: white;
      padding: 12px 24px;
      font-size: 16px;

      &:hover {
        background: #059669;
      }
    }
  }
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px;
  color: #666;

  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }
}

.empty-state {
  text-align: center;
  padding: 60px;
  color: #666;

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  h3 {
    margin: 0 0 8px 0;
    color: #1a1a1a;
  }

  p {
    margin: 0 0 24px 0;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.ai-analysis {
  margin-top: 16px;
  border-top: 1px solid #e5e7eb;
  padding: 16px 24px;
  background: #f9fafb;

  .analysis-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    h5 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #8b5cf6;
    }

    .analysis-time {
      font-size: 12px;
      color: #6b7280;
    }
  }

  .analysis-content {
    font-size: 14px;
    line-height: 1.6;
    color: #374151;

    h4 {
      font-size: 16px;
      font-weight: 600;
      margin: 16px 0 8px 0;
      color: #1f2937;
    }

    h5 {
      font-size: 14px;
      font-weight: 600;
      margin: 12px 0 6px 0;
      color: #374151;
    }

    .code-block {
      background: #1f2937;
      color: #f9fafb;
      padding: 12px;
      border-radius: 6px;
      margin: 8px 0;
      overflow-x: auto;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 13px;

      &.css {
        border-left: 4px solid #3b82f6;
      }

      &.vue {
        border-left: 4px solid #10b981;
      }

      code {
        color: inherit;
        background: none;
        padding: 0;
      }
    }

    li {
      margin: 4px 0;
      list-style: none;
      position: relative;
      padding-left: 20px;

      &::before {
        content: '•';
        color: #8b5cf6;
        position: absolute;
        left: 0;
        font-weight: bold;
      }
    }

    strong {
      color: #1f2937;
      font-weight: 600;
    }
  }
}

.config-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;

  .modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
  }

  .modal-content {
    position: relative;
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7eb;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #1a1a1a;
    }

    .btn-close {
      background: none;
      border: none;
      font-size: 24px;
      color: #9ca3af;
      cursor: pointer;
      padding: 0;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: #f3f4f6;
        color: #374151;
      }
    }
  }

  .modal-body {
    padding: 24px;

    .form-group {
      label {
        display: block;
        font-size: 14px;
        font-weight: 500;
        color: #374151;
        margin-bottom: 8px;
      }

      .api-input {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        font-size: 14px;
        transition: border-color 0.2s ease;
        box-sizing: border-box;

        &:focus {
          outline: none;
          border-color: #8b5cf6;
        }

        &::placeholder {
          color: #9ca3af;
        }
      }

      .form-help {
        margin-top: 12px;
        
        p {
          margin: 4px 0;
          font-size: 13px;
          color: #6b7280;
          line-height: 1.5;
        }

        a {
          color: #8b5cf6;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 24px;
    border-top: 1px solid #e5e7eb;

    button {
      padding: 8px 16px;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      border: none;

      &.btn-cancel {
        background: #f3f4f6;
        color: #374151;

        &:hover {
          background: #e5e7eb;
        }
      }

      &.btn-save {
        background: #8b5cf6;
        color: white;

        &:hover {
          background: #7c3aed;
        }
      }
    }
  }
}

@media (max-width: 1024px) {
  .component-grid {
    grid-template-columns: 1fr;
  }

  .image-comparison {
    grid-template-columns: 1fr;
  }

  .image-section .image-container {
    height: 150px;
  }
}
</style> 