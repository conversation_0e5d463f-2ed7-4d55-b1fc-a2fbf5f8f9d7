<template>
  <div class="benchmark-page">
    <!-- 导航栏 -->
    <div class="benchmark-nav">
      <div class="nav-left">
        <h2>Figma 组件测试</h2>
        <span v-if="componentName" class="current-component">{{ componentName }}</span>
      </div>
      <div class="nav-right">
        <button class="btn-compare" @click="goToCompare">
          📊 查看还原度对比
        </button>
      </div>
    </div>

    <ElConfigProvider namespace="cs">
      <div
        v-if="component"
        id="benchmark-container-for-screenshot"
        class="benchmark-container"
        :style="{ width: componentWidth }"
      >
        <component :is="component" v-bind="componentProps" />
      </div>
      <div v-else class="benchmark-empty">
        Component not found: {{ componentName }}
      </div>
    </ElConfigProvider>
  </div>
</template>

<script setup lang="ts">
import '@/pages/control/element-plus-theme.scss'
import { ElConfigProvider } from 'element-plus'
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { BENCHMARK_COMPONENTS } from '@/benchmark/components';
import { COMPONENT_CONFIGS } from '@/benchmark/components.config.js';

const route = useRoute();
const router = useRouter();
const componentName = computed(() => route.params.component as string);
const component = computed(() => BENCHMARK_COMPONENTS[componentName.value] || null);

// 从配置中获取组件宽度
const componentWidth = computed(() => {
  const width = COMPONENT_CONFIGS[componentName.value]?.width;
  return width ? `${width}px` : 'auto';
});

// 从配置中获取组件props
const componentProps = computed(() => {
  return COMPONENT_CONFIGS[componentName.value]?.props || {};
});

// 跳转到对比页面
const goToCompare = () => {
  // 如果当前有组件名，则跳转到对应组件的对比页面
  if (componentName.value) {
    router.push(`/benchmark-compare/${componentName.value}`);
  } else {
    router.push('/benchmark-compare');
  }
};
</script>

<style lang="scss" scoped>
.benchmark-page {
  min-height: 100vh;
  background-color: #f0f2f5;
  display: flex;
  flex-direction: column;
}

.benchmark-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .nav-left {
    display: flex;
    align-items: center;
    gap: 16px;

    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #1a1a1a;
    }

    .current-component {
      padding: 4px 12px;
      background: #f3f4f6;
      border-radius: 16px;
      font-size: 14px;
      color: #374151;
      font-weight: 500;
    }
  }

  .nav-right {
    .btn-compare {
      padding: 8px 16px;
      background: #3b82f6;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover {
        background: #2563eb;
      }
    }
  }
}

.benchmark-container {
  margin-top: 80px; /* 为固定导航栏留出空间 */
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  padding: 20px;
  
  /* 容器样式可以根据需要调整 */
  overflow: hidden; /* 确保内容不会溢出容器 */
  box-sizing: content-box;
  align-self: center;
  background-color: #fff;
}

.benchmark-empty {
  margin-top: 80px; /* 为固定导航栏留出空间 */
  font-size: 24px;
  color: #909399;
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
}
</style> 