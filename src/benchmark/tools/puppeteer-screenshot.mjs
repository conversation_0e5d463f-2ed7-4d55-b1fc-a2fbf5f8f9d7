import puppeteer from 'puppeteer';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

function getComponentConfig(componentName) {
  const benchmarkDir = path.resolve(path.dirname(fileURLToPath(import.meta.url)), '..'); // in src/benchmark
  const componentDir = path.join(benchmarkDir, 'components', componentName);
  const resultsDir = path.join(benchmarkDir, 'results', componentName);
  
  return {
    name: componentName,
    url: `http://localhost:81/benchmark/${componentName}`,
    selector: `#${componentName}-test`,
    expectedPath: path.join(componentDir, 'expected.png'),
    actualPath: path.join(resultsDir, 'actual.png'),
    diffPath: path.join(resultsDir, 'diff.png'),
  };
}

// 添加未处理的 Promise 拒绝处理器
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的 Promise 拒绝 (puppeteer):', reason);
  process.exit(1);
});

function log(...args) {
  // fs.appendFileSync('/tmp/screenshot.log', args.join(' ') + '\n');
  console.log(...args);
}

/**
 * 精确检测元素的阴影属性并计算边距
 * 使用理论精确值而非经验算法
 */
async function detectShadow(page, selector) {
  const shadowInfo = await page.evaluate((sel) => {
    const element = document.querySelector(sel);
    if (!element) return null;
    
    const styles = window.getComputedStyle(element);
    const boxShadow = styles.boxShadow;
    
    if (!boxShadow || boxShadow === 'none') {
      return { hasShadow: false };
    }
    
    // 更精确的box-shadow解析
    // 格式: [inset] offset-x offset-y [blur-radius] [spread-radius] color
    // 支持多个阴影，用逗号分隔
    const shadows = [];
    
    // 修复：正确分割阴影，避免颜色中的逗号干扰
    // 使用更智能的方式分割，考虑到rgba()中可能包含逗号
    const shadowList = [];
    let currentShadow = '';
    let parenDepth = 0;
    
    for (let i = 0; i < boxShadow.length; i++) {
      const char = boxShadow[i];
      if (char === '(') parenDepth++;
      if (char === ')') parenDepth--;
      
      if (char === ',' && parenDepth === 0) {
        shadowList.push(currentShadow.trim());
        currentShadow = '';
      } else {
        currentShadow += char;
      }
    }
    if (currentShadow.trim()) {
      shadowList.push(currentShadow.trim());
    }
    
    shadowList.forEach(shadowStr => {
      const trimmed = shadowStr.trim();
      
      // 移除颜色部分，只保留数值部分
      // 先移除 rgba()/rgb()/hsl()/颜色名称等
      let cleanShadow = trimmed;
      
      // 移除 rgba(), rgb(), hsl() 等颜色函数
      cleanShadow = cleanShadow.replace(/rgba?\([^)]+\)/g, '');
      cleanShadow = cleanShadow.replace(/hsla?\([^)]+\)/g, '');
      
      // 移除十六进制颜色 (#fff, #ffffff)
      cleanShadow = cleanShadow.replace(/#[0-9a-fA-F]{3,6}/g, '');
      
      // 移除命名颜色 (但要小心不要移除inset)
      const namedColors = ['transparent', 'currentColor', 'inherit', 'initial', 'unset'];
      namedColors.forEach(color => {
        cleanShadow = cleanShadow.replace(new RegExp('\\\\b' + color + '\\\\b', 'gi'), '');
      });
      
      // 提取连续的像素值
      const valueRegex = /(-?\d+(?:\.\d+)?px)/g;
      const values = [];
      let match;
      while ((match = valueRegex.exec(cleanShadow)) !== null) {
        values.push(parseFloat(match[1]));
      }
      
      // 至少需要 offset-x 和 offset-y
      if (values.length >= 2) {
        const shadow = {
          offsetX: values[0] || 0,
          offsetY: values[1] || 0,
          blur: values[2] || 0,
          spread: values[3] || 0
        };
        shadows.push(shadow);
      }
    });
    
    if (shadows.length === 0) {
      return { hasShadow: false };
    }
    
    // 使用精确的理论计算，不再使用经验系数
    let maxLeft = 0, maxRight = 0, maxTop = 0, maxBottom = 0;
    
    shadows.forEach(shadow => {
      // 阴影边界的精确计算公式：
      // 每个方向的阴影范围 = |offset| + spread + blur
      // 这是CSS标准的理论值，与Figma导出应该完全一致
      
      const left = -shadow.offsetX + shadow.spread + shadow.blur;
      const right = shadow.offsetX + shadow.spread + shadow.blur;
      const top = -shadow.offsetY + shadow.spread + shadow.blur;
      const bottom = shadow.offsetY + shadow.spread + shadow.blur;
      
      // 只取正值（阴影不会向内收缩）
      maxLeft = Math.max(maxLeft, Math.max(left, 0));
      maxRight = Math.max(maxRight, Math.max(right, 0));
      maxTop = Math.max(maxTop, Math.max(top, 0));
      maxBottom = Math.max(maxBottom, Math.max(bottom, 0));
    });
    
    // 精确的四方向边距
    const shadowMargin = {
      top: Math.ceil(maxTop),
      right: Math.ceil(maxRight),
      bottom: Math.ceil(maxBottom),
      left: Math.ceil(maxLeft)
    };
    
    // 计算统一边距（取最大值确保包含所有阴影）
    const calculatedMargin = Math.max(
      shadowMargin.top,
      shadowMargin.right,
      shadowMargin.bottom,
      shadowMargin.left
    );
    
    return {
      hasShadow: true,
      rawBoxShadow: boxShadow,
      shadows: shadows,
      shadowMargin,
      calculatedMargin,
      debugInfo: {
        shadowCount: shadows.length,
        maxBounds: { maxLeft, maxRight, maxTop, maxBottom },
        parsedShadows: shadows.map(s => `offset(${s.offsetX},${s.offsetY}) blur:${s.blur} spread:${s.spread}`),
        theoreticalBounds: {
          top: maxTop,
          right: maxRight,
          bottom: maxBottom,
          left: maxLeft
        }
      }
    };
  }, selector);
  
  return shadowInfo;
}

export async function takeScreenshot(url, selector, outputPath, options = {}) {
  log('\\n开始启动浏览器...');
  let browser;
  try {
    const executablePath = process.env.PUPPETEER_EXECUTABLE_PATH || '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome';
    if (!fs.existsSync(executablePath)) {
      log(`错误：在指定路径找不到 Chrome: ${executablePath}`);
      log('请确认 Chrome 已安装或设置 PUPPETEER_EXECUTABLE_PATH 环境变量。');
      return { success: false, domTree: null };
    }
    browser = await puppeteer.launch({
      executablePath,
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    const page = await browser.newPage();
    await page.setViewport({ 
      width: 1152, 
      height: 772, 
      deviceScaleFactor: 3
    });
    await page.goto(url, { waitUntil: 'networkidle2' });
    await page.waitForSelector(selector);
    const element = await page.$(selector);
    if (!element) {
      throw new Error(`找不到元素: ${selector}`);
    }
    const domTree = await page.evaluate(el => {
      if (!el) return null;
      function serializeNode(node) {
        if (node.nodeType === Node.TEXT_NODE) {
          const text = node.textContent.trim();
          return text ? { type: 'text', content: text } : null;
        }
        if (node.nodeType !== Node.ELEMENT_NODE) {
          return null;
        }
        const obj = {
          tag: node.tagName.toLowerCase(),
          attributes: {},
          children: []
        };
        for (const attr of node.attributes) {
          obj.attributes[attr.name] = attr.value;
    }
        for (const child of node.childNodes) {
          const serializedChild = serializeNode(child);
          if (serializedChild) {
            obj.children.push(serializedChild);
          }
        }
        return obj;
      }
      return serializeNode(el);
    }, element);
    await page.hover(selector);
    await new Promise(r => setTimeout(r, 200));
    const screenshotOptions = {
        path: outputPath,
      omitBackground: true
    };
    await element.screenshot(screenshotOptions);
    log(`✅ 截图已保存到: ${outputPath}`);
    return { success: true, domTree };
  } catch (error) {
    log('❌ 截图过程中发生错误:', error);
    return { success: false, domTree: null };
  } finally {
    if (browser) {
      log('关闭浏览器...');
      await browser.close();
    }
  }
}

// 命令行调用
async function main() {
    try {
      const componentName = process.argv.find(arg => arg.startsWith('--component='))?.split('=')[1];
      if (!componentName) {
      console.error('Usage: node src/benchmark/tools/puppeteer-screenshot.mjs --component=<ComponentName>');
        process.exit(1);
      }
      const config = getComponentConfig(componentName);
      log(`开始为组件 ${componentName} 截图...`);
    const result = await takeScreenshot(config.url, config.selector, config.actualPath, {});
    if (result.success) {
        log(`组件 ${componentName} 截图完成。`);
      // Here you could write the domTree to a file if needed for debugging
      // fs.writeFileSync('dom-tree.json', JSON.stringify(result.domTree, null, 2));
        process.exit(0);
      } else {
        log(`组件 ${componentName} 截图失败。`);
        process.exit(1);
      }
    } catch (error) {
      console.error('执行截图时发生致命错误:', error);
      process.exit(1);
    }
}

const __filename = fileURLToPath(import.meta.url);
if (process.argv[1] === path.resolve(__filename)) {
  main();
} 