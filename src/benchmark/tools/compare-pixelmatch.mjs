import fs from 'fs';
import path from 'path';
import { PNG } from 'pngjs';
import pixelmatch from 'pixelmatch';
import { fileURLToPath } from 'url';
import sharp from 'sharp';

function getComponentConfig(componentName) {
  const configPath = path.join(path.dirname(fileURLToPath(import.meta.url)), '../components.config.ts');
  if (fs.existsSync(configPath)) {
    // 简单的配置解析，实际项目中可能需要更复杂的解析
    return { threshold: 0.1 };
  }
  return { threshold: 0.1 };
}

// 简化的差异分析，避免堆栈溢出
function analyzeDiffRegions(diffData, width, height, threshold = 10) {
  let diffPixelCount = 0;
  let minX = width, maxX = 0, minY = height, maxY = 0;
  
  // 只计算差异像素数量和边界框，不进行复杂的聚类
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const idx = (width * y + x) << 2;
      const r = diffData[idx];
      const g = diffData[idx + 1];
      const b = diffData[idx + 2];
      
      // 检查是否为差异像素（红色像素）
      if (r > 200 && g < 50 && b < 50) {
        diffPixelCount++;
        minX = Math.min(minX, x);
        maxX = Math.max(maxX, x);
        minY = Math.min(minY, y);
        maxY = Math.max(maxY, y);
      }
    }
  }
  
  if (diffPixelCount === 0) {
    return { 
      diffPixels: [], 
      diffRegions: [], 
      summary: '没有发现差异像素',
      overallBounds: null
    };
  }
  
  const summary = `发现差异像素 ${diffPixelCount} 个，主要分布在区域 (${minX}, ${minY}) 到 (${maxX}, ${maxY})`;
  
  return {
    diffPixels: [],
    diffRegions: [{
      boundingBox: {
        x: minX,
        y: minY,
        width: maxX - minX + 1,
        height: maxY - minY + 1
      },
      pixelCount: diffPixelCount,
      center: {
        x: Math.round((minX + maxX) / 2),
        y: Math.round((minY + maxY) / 2)
      }
    }],
    summary,
    overallBounds: { minX, maxX, minY, maxY, width: maxX - minX + 1, height: maxY - minY + 1 }
  };
}

export async function compareImages(image1Path, image2Path, diffOutputPath) {
  console.log('Reading images with sharp...');
  let img1Buffer = fs.readFileSync(image1Path);
  let img2Buffer = fs.readFileSync(image2Path);

  const img1Sharp = sharp(img1Buffer);
  const img2Sharp = sharp(img2Buffer);

  const meta1 = await img1Sharp.metadata();
  const meta2 = await img2Sharp.metadata();

  // 输出详细的图片尺寸信息
  console.log('\n=== 图片尺寸分析 ===');
  console.log(`📐 设计图尺寸: ${meta1.width} × ${meta1.height} 像素`);
  console.log(`📐 实际图尺寸: ${meta2.width} × ${meta2.height} 像素`);
  
  if (meta1.width !== meta2.width || meta1.height !== meta2.height) {
    console.warn('⚠️  图片尺寸不匹配，正在调整设计图尺寸以进行比较...');
    console.warn(`   宽度差异: ${meta2.width - meta1.width} 像素 (${meta1.width} → ${meta2.width})`);
    console.warn(`   高度差异: ${meta2.height - meta1.height} 像素 (${meta1.height} → ${meta2.height})`);
    
    // 计算缩放比例
    const scaleX = (meta2.width / meta1.width).toFixed(3);
    const scaleY = (meta2.height / meta1.height).toFixed(3);
    console.warn(`   缩放比例: X轴 ${scaleX}x, Y轴 ${scaleY}x`);
    
    img1Buffer = await img1Sharp
      .resize(meta2.width, meta2.height, { 
        fit: 'fill',
        kernel: sharp.kernel.nearest 
      })
      .png()
      .toBuffer();
    console.log('✅ 设计图已调整为实际图尺寸进行对比');
  } else {
    console.log('✅ 图片尺寸完全匹配，无需调整');
  }

  // 使用 PNG.js 解析图片
  const img1 = PNG.sync.read(img1Buffer);
  const img2 = PNG.sync.read(img2Buffer);
  const { width, height } = img2;

  // 创建差异图
  const diff = new PNG({ width, height });

  // 进行像素匹配
  const diffPixelCount = pixelmatch(img1.data, img2.data, diff.data, width, height, {
    threshold: 0.1,
    includeAA: false,
    alpha: 0.1,
    diffColor: [255, 0, 0], // 红色
    aaColor: [255, 255, 0]  // 黄色
  });

  // 保存差异图
  fs.writeFileSync(diffOutputPath, PNG.sync.write(diff));

  // 分析差异区域
  const diffAnalysis = analyzeDiffRegions(diff.data, width, height);

  const totalPixels = width * height;
  const matchPercentage = ((totalPixels - diffPixelCount) / totalPixels * 100).toFixed(2);

  const result = {
    totalPixels,
    diffPixels: diffPixelCount,
    matchPercentage: parseFloat(matchPercentage),
    dimensions: { width, height },
    diffAnalysis
  };

  // 输出详细的差异分析
  console.log('\n=== 详细差异分析 ===');
  console.log(diffAnalysis.summary);
  
  if (diffAnalysis.diffRegions.length > 0) {
    console.log('\n前 5 个最大的差异区域：');
    diffAnalysis.diffRegions.slice(0, 5).forEach((region, index) => {
      console.log(`${index + 1}. 位置: (${region.boundingBox.x}, ${region.boundingBox.y})`);
      console.log(`   尺寸: ${region.boundingBox.width}x${region.boundingBox.height}`);
      console.log(`   中心点: (${region.center.x}, ${region.center.y})`);
      console.log(`   差异像素数: ${region.pixelCount}`);
      console.log('');
    });
    
    console.log('💡 修正建议：');
    console.log('1. 重点检查上述差异区域中心点周围的元素');
    console.log('2. 差异区域可能对应文本错位、图标位置偏移、边距不正确等问题');
    console.log('3. 最大的差异区域通常是最需要优先修正的问题');
  }

  return result;
}

// 如果直接运行此脚本
if (process.argv[1] === new URL(import.meta.url).pathname) {
  const [,, image1Path, image2Path, diffOutputPath] = process.argv;
  
  if (!image1Path || !image2Path) {
    console.error('用法: node compare-pixelmatch.mjs <图片1路径> <图片2路径> [差异图输出路径]');
    process.exit(1);
  }
  
  const outputPath = diffOutputPath || 'diff.png';
  
  try {
    const result = await compareImages(image1Path, image2Path, outputPath);
    console.log('\n=== 对比结果 ===');
    console.log(`匹配度: ${result.matchPercentage}%`);
    console.log(`差异像素: ${result.diffPixels}/${result.totalPixels}`);
    console.log(`差异图已保存: ${outputPath}`);
  } catch (error) {
    console.error('对比失败:', error.message);
    process.exit(1);
  }
} 