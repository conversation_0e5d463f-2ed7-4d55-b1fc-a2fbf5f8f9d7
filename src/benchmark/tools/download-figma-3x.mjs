#!/usr/bin/env node

/**
 * Figma 三倍图下载工具
 * 用于下载高分辨率的设计稿进行像素级对比
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Figma API Token
const FIGMA_TOKEN = '*********************************************';

/**
 * 下载Figma图片 (三倍图)
 * @param {string} fileKey - Figma文件ID
 * @param {string} nodeId - 节点ID
 * @param {string} outputPath - 输出路径
 * @param {string} format - 图片格式 (png/svg)
 * @param {number} scale - 缩放倍数 (默认3)
 */
async function downloadFigmaImage(fileKey, nodeId, outputPath, format = 'png', scale = 3) {
  try {
    console.log(`🔄 开始下载 ${format.toUpperCase()} 图片...`);
    console.log(`   文件ID: ${fileKey}`);
    console.log(`   节点ID: ${nodeId}`);
    console.log(`   缩放倍数: ${scale}x`);
    console.log(`   输出路径: ${outputPath}`);

    // 第一步：获取图片下载链接
    const apiUrl = `https://api.figma.com/v1/images/${fileKey}?ids=${nodeId}&format=${format}&scale=${scale}`;
    console.log(`📡 请求API: ${apiUrl}`);

    const response = await fetch(apiUrl, {
      headers: {
        'X-FIGMA-TOKEN': FIGMA_TOKEN
      }
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('📦 API响应:', JSON.stringify(data, null, 2));

    if (data.err) {
      throw new Error(`Figma API错误: ${data.err}`);
    }

    const imageUrl = data.images[nodeId];
    if (!imageUrl) {
      throw new Error(`未找到节点 ${nodeId} 的图片URL`);
    }

    console.log(`🔗 图片URL: ${imageUrl}`);

    // 第二步：下载图片
    console.log('⬇️  开始下载图片...');
    const imageResponse = await fetch(imageUrl);
    
    if (!imageResponse.ok) {
      throw new Error(`图片下载失败: ${imageResponse.status} ${imageResponse.statusText}`);
    }

    // 确保输出目录存在
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`📁 创建目录: ${dir}`);
    }

    // 保存图片
    const buffer = await imageResponse.arrayBuffer();
    fs.writeFileSync(outputPath, Buffer.from(buffer));

    console.log(`✅ 图片下载成功!`);
    console.log(`   文件大小: ${(buffer.byteLength / 1024).toFixed(1)} KB`);
    console.log(`   保存路径: ${outputPath}`);

    return true;

  } catch (error) {
    console.error('❌ 下载失败:', error.message);
    return false;
  }
}

/**
 * 批量更新组件的预期图片为三倍图
 * @param {string} componentName - 组件名称
 * @param {string} fileKey - Figma文件ID  
 * @param {string} nodeId - 节点ID
 */
async function updateComponentTo3x(componentName, fileKey, nodeId) {
  try {
    console.log(`\n🎯 更新组件 ${componentName} 为三倍图...`);

    const benchmarkDir = path.resolve(path.dirname(fileURLToPath(import.meta.url)), '..');
    const componentDir = path.join(benchmarkDir, 'components', componentName);
    const expectedPath = path.join(componentDir, 'expected.png');

    // 备份原有图片
    if (fs.existsSync(expectedPath)) {
      const backupPath = path.join(componentDir, 'expected_1x_backup.png');
      fs.copyFileSync(expectedPath, backupPath);
      console.log(`📋 备份原图片到: expected_1x_backup.png`);
    }

    // 下载三倍图
    const success = await downloadFigmaImage(fileKey, nodeId, expectedPath, 'png', 3);
    
    if (success) {
      console.log(`✅ ${componentName} 更新为三倍图成功!`);
      
      // 显示图片信息
      const stats = fs.statSync(expectedPath);
      console.log(`   文件大小: ${(stats.size / 1024).toFixed(1)} KB`);
    } else {
      console.log(`❌ ${componentName} 更新失败`);
    }

    return success;

  } catch (error) {
    console.error(`❌ 更新组件 ${componentName} 失败:`, error.message);
    return false;
  }
}

/**
 * 批量更新所有组件为三倍图
 */
async function updateAllComponentsTo3x() {
  console.log('🚀 开始批量更新所有组件为三倍图...');
  
  // 这里需要手动配置每个组件的信息
  // 实际使用时，可以从配置文件或数据库中读取
  const components = [
    {
      name: 'ContextMenuDocuments',
      fileKey: 'p8SnwxuE0a4p92MUxQL2bA',
      nodeId: '1530:36301'
    },
    // 可以添加更多组件...
  ];

  let successCount = 0;
  let totalCount = components.length;

  for (const component of components) {
    const success = await updateComponentTo3x(component.name, component.fileKey, component.nodeId);
    if (success) {
      successCount++;
    }
    
    // 添加延迟避免API限制
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  console.log(`\n📊 批量更新完成:`);
  console.log(`   成功: ${successCount}/${totalCount}`);
  console.log(`   失败: ${totalCount - successCount}/${totalCount}`);
}

// 命令行工具
if (process.argv[1] === fileURLToPath(import.meta.url)) {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(`
🎯 Figma 三倍图下载工具

用法:
  node download-figma-3x.mjs <命令> [参数...]

命令:
  download <fileKey> <nodeId> <outputPath>  - 下载单个图片
  update <componentName> <fileKey> <nodeId> - 更新组件为三倍图
  update-all                                - 批量更新所有组件

示例:
  # 下载单个图片
  node download-figma-3x.mjs download p8SnwxuE0a4p92MUxQL2bA 1530:36301 ./expected.png
  
  # 更新组件
  node download-figma-3x.mjs update ContextMenuDocuments p8SnwxuE0a4p92MUxQL2bA 1530:36301
  
  # 批量更新
  node download-figma-3x.mjs update-all
    `);
    process.exit(0);
  }

  const command = args[0];

  switch (command) {
    case 'download':
      if (args.length < 4) {
        console.error('❌ 参数不足: download <fileKey> <nodeId> <outputPath>');
        process.exit(1);
      }
      downloadFigmaImage(args[1], args[2], args[3])
        .then(success => process.exit(success ? 0 : 1));
      break;

    case 'update':
      if (args.length < 4) {
        console.error('❌ 参数不足: update <componentName> <fileKey> <nodeId>');
        process.exit(1);
      }
      updateComponentTo3x(args[1], args[2], args[3])
        .then(success => process.exit(success ? 0 : 1));
      break;

    case 'update-all':
      updateAllComponentsTo3x()
        .then(() => process.exit(0));
      break;

    default:
      console.error(`❌ 未知命令: ${command}`);
      process.exit(1);
  }
}

export { downloadFigmaImage, updateComponentTo3x, updateAllComponentsTo3x }; 