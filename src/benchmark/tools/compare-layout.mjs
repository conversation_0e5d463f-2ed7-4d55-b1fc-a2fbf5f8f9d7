import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const GEOMETRY_TOLERANCE = 1.5; // Allow 1.5px tolerance for rounding/rendering differences

function compareNodes(expectedNode, actualNode, path) {
    const diffs = [];
    if (!expectedNode || !actualNode) return diffs;
    
    // Use layout as fallback for expected, and normalize structure
    const expectedGeom = expectedNode.geometry || expectedNode.layout;
    const actualGeom = actualNode.geometry;

    if (!expectedGeom || !actualGeom) return diffs;

    // Compare geometry properties
    for (const key of ['x', 'y', 'width', 'height']) {
        const diff = Math.abs(actualGeom[key] - expectedGeom[key]);
        if (diff > GEOMETRY_TOLERANCE) {
            diffs.push({
                path,
                property: `geometry.${key}`,
                expected: expectedGeom[key],
                actual: actualGeom[key],
                diff,
            });
        }
    }

    // Safely compare margin
    if (expectedGeom.margin && actualGeom.margin) {
        for (const side of ['top', 'left', 'right', 'bottom']) {
            const diff = Math.abs(actualGeom.margin[side] - expectedGeom.margin[side]);
            if (diff > GEOMETRY_TOLERANCE) {
                diffs.push({ path, property: `geometry.margin.${side}`, expected: expectedGeom.margin[side], actual: actualGeom.margin[side], diff });
            }
        }
    }

    // Safely compare padding
    if (expectedGeom.padding && actualGeom.padding) {
        for (const side of ['top', 'left', 'right', 'bottom']) {
            const diff = Math.abs(actualGeom.padding[side] - expectedGeom.padding[side]);
            if (diff > GEOMETRY_TOLERANCE) {
                diffs.push({ path, property: `geometry.padding.${side}`, expected: expectedGeom.padding[side], actual: actualGeom.padding[side], diff });
            }
        }
    }

    // Recursively compare children
    const expectedChildren = expectedNode.children || [];
    const actualChildren = actualNode.children || [];

    const len = Math.min(expectedChildren.length, actualChildren.length);
    for(let i=0; i<len; i++){
        diffs.push(...compareNodes(expectedChildren[i], actualChildren[i], `${path}.children[${i}]`));
    }

    if (expectedChildren.length !== actualChildren.length) {
        diffs.push({
            path,
            property: 'children.length',
            expected: expectedChildren.length,
            actual: actualChildren.length,
            diff: Math.abs(expectedChildren.length - actualChildren.length)
        })
    }

    return diffs;
}

// --- Main Execution ---
const componentName = process.argv.find(arg => arg.startsWith('--component='))?.split('=')[1] || 'DocItemCard';
const resultsDir = path.resolve(__dirname, `../results/${componentName}`);

try {
    const expected = JSON.parse(fs.readFileSync(path.resolve(__dirname, 'figma-tree.json'), 'utf-8'));
    const actual = JSON.parse(fs.readFileSync(path.join(resultsDir, 'actual_with_geometry.json'), 'utf-8'));

    // We need to find the specific component in the figma tree
    const findComponent = (node, name) => {
        if(node.name.includes(name)) return node;
        if(node.children) {
            for(const child of node.children) {
                const found = findComponent(child, name);
                if(found) return found;
            }
        }
        return null;
    }

    // This is a simplification, assuming the component name matches a node name in the tree.
    // In a real scenario, you might have a more robust way to map component to figma node.
    const expectedRootNode = findComponent(expected, '图标') || expected; // '图标' is the root name in figma-tree.json

    const layoutDiffs = compareNodes(expectedRootNode, actual, 'root');

    const report = {
        component: componentName,
        totalDiffs: layoutDiffs.length,
        diffs: layoutDiffs
    };
    
    fs.writeFileSync(path.join(resultsDir, 'layout-diff-report.json'), JSON.stringify(report, null, 2));

    if (layoutDiffs.length > 0) {
        console.error(`❌ Found ${layoutDiffs.length} layout inconsistencies.`);
        console.log(JSON.stringify(report, null, 2))
    } else {
        console.log(`✅ Perfect layout match for component: ${componentName}!`);
    }

} catch (error) {
    console.error(`❌ Error generating layout diff report for component ${componentName}:`, error);
    process.exit(1);
} 