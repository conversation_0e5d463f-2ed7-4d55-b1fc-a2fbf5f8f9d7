#!/usr/bin/env node

/**
 * 比较不同scale图片的效果工具
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

/**
 * 获取图片信息
 */
function getImageInfo(imagePath) {
  if (!fs.existsSync(imagePath)) {
    return null;
  }
  
  const stats = fs.statSync(imagePath);
  return {
    path: imagePath,
    size: stats.size,
    sizeKB: (stats.size / 1024).toFixed(1),
    exists: true
  };
}

/**
 * 比较组件的不同scale图片
 */
function compareComponentScales(componentName) {
  console.log(`\n🔍 比较组件 ${componentName} 的不同scale图片:`);
  console.log('=' .repeat(60));

  const benchmarkDir = path.resolve(path.dirname(fileURLToPath(import.meta.url)), '..');
  const componentDir = path.join(benchmarkDir, 'components', componentName);
  const resultsDir = path.join(benchmarkDir, 'results', componentName);

  // 预期图片对比
  console.log('\n📋 预期图片 (设计稿):');
  const expected3x = getImageInfo(path.join(componentDir, 'expected.png'));
  const expected1x = getImageInfo(path.join(componentDir, 'expected_1x_backup.png'));
  
  if (expected3x && expected1x) {
    console.log(`   三倍图: ${expected3x.sizeKB} KB`);
    console.log(`   一倍图: ${expected1x.sizeKB} KB`);
    console.log(`   倍数比: ${(expected3x.size / expected1x.size).toFixed(1)}x`);
  } else if (expected3x) {
    console.log(`   三倍图: ${expected3x.sizeKB} KB (当前)`);
    console.log(`   一倍图: 未找到备份`);
  } else {
    console.log(`   ❌ 未找到预期图片`);
  }

  // 实际截图对比
  console.log('\n🖥️  实际截图:');
  const actual = getImageInfo(path.join(resultsDir, 'actual.png'));
  
  if (actual) {
    console.log(`   当前截图: ${actual.sizeKB} KB`);
  } else {
    console.log(`   ❌ 未找到实际截图`);
  }

  // 差异图对比
  console.log('\n🔍 差异分析:');
  const diff = getImageInfo(path.join(resultsDir, 'diff.png'));
  
  if (diff) {
    console.log(`   差异图: ${diff.sizeKB} KB`);
  } else {
    console.log(`   ❌ 未找到差异图`);
  }

  // 测试结果对比
  console.log('\n📊 测试结果:');
  const resultPath = path.join(resultsDir, 'comprehensive-result.json');
  
  if (fs.existsSync(resultPath)) {
    try {
      const result = JSON.parse(fs.readFileSync(resultPath, 'utf8'));
      console.log(`   匹配度: ${result.matchPercentage}%`);
      console.log(`   图片尺寸: ${result.dimensions.width} × ${result.dimensions.height}`);
      console.log(`   总像素: ${result.totalPixels.toLocaleString()}`);
      console.log(`   差异像素: ${result.diffPixels.toLocaleString()}`);
      
      // 计算像素密度
      const pixelDensity = result.totalPixels / (result.dimensions.width * result.dimensions.height);
      if (pixelDensity > 1) {
        console.log(`   像素密度: ${pixelDensity.toFixed(1)}x (高DPI)`);
      }
    } catch (error) {
      console.log(`   ❌ 解析测试结果失败: ${error.message}`);
    }
  } else {
    console.log(`   ❌ 未找到测试结果文件`);
  }

  console.log('\n💡 建议:');
  if (expected3x && expected1x) {
    console.log(`   ✅ 已使用三倍图进行测试`);
    console.log(`   📈 图片质量提升: ${((expected3x.size / expected1x.size) - 1) * 100}%`);
  } else if (expected3x && !expected1x) {
    console.log(`   ✅ 当前使用三倍图`);
  } else {
    console.log(`   ⚠️  建议下载三倍图以提高对比精度`);
    console.log(`   🔧 命令: node download-figma-3x.mjs update ${componentName} <fileKey> <nodeId>`);
  }
}

/**
 * 比较所有组件
 */
function compareAllComponents() {
  console.log('🚀 比较所有组件的scale效果...\n');

  const benchmarkDir = path.resolve(path.dirname(fileURLToPath(import.meta.url)), '..');
  const componentsDir = path.join(benchmarkDir, 'components');

  if (!fs.existsSync(componentsDir)) {
    console.error('❌ 组件目录不存在');
    return;
  }

  const components = fs.readdirSync(componentsDir)
    .filter(name => {
      const componentPath = path.join(componentsDir, name);
      return fs.statSync(componentPath).isDirectory() && name !== 'index.ts';
    });

  console.log(`找到 ${components.length} 个组件:\n`);

  let total3x = 0;
  let total1x = 0;

  components.forEach(componentName => {
    compareComponentScales(componentName);
    
    // 统计
    const componentDir = path.join(componentsDir, componentName);
    const expected3x = getImageInfo(path.join(componentDir, 'expected.png'));
    const expected1x = getImageInfo(path.join(componentDir, 'expected_1x_backup.png'));
    
    if (expected3x && expected1x) {
      total3x++;
    } else if (expected3x) {
      total1x++;
    }
  });

  console.log('\n📈 总体统计:');
  console.log('=' .repeat(60));
  console.log(`   使用三倍图的组件: ${total3x}个`);
  console.log(`   使用一倍图的组件: ${total1x}个`);
  console.log(`   总组件数: ${components.length}个`);
  console.log(`   三倍图覆盖率: ${((total3x / components.length) * 100).toFixed(1)}%`);
}

// 命令行工具
if (process.argv[1] === fileURLToPath(import.meta.url)) {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(`
🔍 Scale比较工具

用法:
  node compare-scales.mjs <命令> [参数...]

命令:
  component <componentName>  - 比较单个组件的scale效果
  all                       - 比较所有组件的scale效果

示例:
  # 比较单个组件
  node compare-scales.mjs component ContextMenuDocuments
  
  # 比较所有组件
  node compare-scales.mjs all
    `);
    process.exit(0);
  }

  const command = args[0];

  switch (command) {
    case 'component':
      if (args.length < 2) {
        console.error('❌ 参数不足: component <componentName>');
        process.exit(1);
      }
      compareComponentScales(args[1]);
      break;

    case 'all':
      compareAllComponents();
      break;

    default:
      console.error(`❌ 未知命令: ${command}`);
      process.exit(1);
  }
} 