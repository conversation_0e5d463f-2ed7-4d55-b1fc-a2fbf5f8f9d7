// 添加全局错误处理器
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  process.exit(1);
});

// 添加未处理的 Promise 拒绝处理器
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的 Promise 拒绝:', reason);
  process.exit(1);
});

// 添加退出处理器
process.on('exit', (code) => {
  console.log(`进程退出，退出码: ${code}`);
});

import fs from 'fs';
import path from 'path';
import puppeteer from 'puppeteer';
import { compareImages } from './compare-pixelmatch.mjs';
import { fileURLToPath } from 'url';
import chalk from 'chalk';
import { COMPONENT_CONFIGS } from '../components.config.js';

function log(...args) {
  fs.appendFileSync('/tmp/benchmark.log', args.join(' ') + '\n');
}

// 简单测试
async function simpleTest(componentName = 'ZoomMenu') {

  const testDir = path.join(process.cwd(), `src/benchmark/components/${componentName}`);
  const resultsDir = path.join(process.cwd(), `src/benchmark/results/${componentName}`);

  if (!fs.existsSync(resultsDir)) {
    fs.mkdirSync(resultsDir, { recursive: true });
  }

  const actualPath = path.join(resultsDir, 'actual.png');
  const expectedPath = path.join(testDir, 'expected.png');
  const diffPath = path.join(resultsDir, 'diff.png');
  const treePath = path.join(resultsDir, 'tree.json');

  if (!fs.existsSync(expectedPath)) {
    throw new Error(`找不到设计图: ${expectedPath}`);
  }

  // 获取组件配置
  const componentConfig = COMPONENT_CONFIGS[componentName] || COMPONENT_CONFIGS.default;
  const containerSelector = componentConfig.selector || `#benchmark-container-for-screenshot`;
  const componentSelector = `#${componentName}-test`; // The actual component selector
  const viewport = componentConfig.viewport || { width: 1152, height: 772 };
  const screenshotOptions = componentConfig.screenshotOptions || { omitBackground: true };



  // --- Puppeteer logic starts ---
  let browser;
  try {
    const executablePath = process.env.PUPPETEER_EXECUTABLE_PATH || '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome';
    if (!fs.existsSync(executablePath)) {
      throw new Error(`找不到 Chrome: ${executablePath}`);
    }
    browser = await puppeteer.launch({ executablePath, headless: 'new' });
    const page = await browser.newPage();
    
    // 使用配置中的viewport
    await page.setViewport({ 
      width: viewport.width, 
      height: viewport.height, 
      deviceScaleFactor: screenshotOptions.deviceScaleFactor || 1 
    });
    
    const url = `http://localhost:81/benchmark/${componentName}`;
    await page.goto(url, { waitUntil: 'networkidle2' });
    
    // 等待Vue应用加载
    await page.waitForFunction(() => {
      return document.querySelector('#app') && document.querySelector('#app').innerHTML !== '';
    }, { timeout: 3000 });
    
    // 等待容器选择器
    try {
      await page.waitForSelector(containerSelector, { timeout: 10000 });
    } catch (error) {
      console.error(`等待容器选择器 ${containerSelector} 超时`);
      throw error;
    }
    
    // Find the component element within the container
    const element = await page.$(componentSelector);
    if (!element) {
      console.error(`找不到组件元素: ${componentSelector}`);
      throw new Error(`找不到元素: ${componentSelector}`);
    }
    
    // 等待组件完全渲染
    await page.waitForTimeout(100);

    // Inject a script to get the entire DOM structure with geometry
    const actualTree = await page.evaluate((selector) => {
      const getGeometry = (element, parentRect, parentStyle) => {
        const rect = element.getBoundingClientRect();
        const style = window.getComputedStyle(element);

        const parentPadding = {
          left: parseFloat(parentStyle.paddingLeft),
          top: parseFloat(parentStyle.paddingTop)
        };

        // Normalize coordinates to be relative to the parent's CONTENT BOX
        const relativeX = rect.left - (parentRect.left + parentPadding.left);
        const relativeY = rect.top - (parentRect.top + parentPadding.top);

        return {
          x: relativeX,
          y: relativeY,
          width: rect.width,
          height: rect.height,
          margin: {
            top: parseFloat(style.marginTop),
            left: parseFloat(style.marginLeft),
            right: parseFloat(style.marginRight),
            bottom: parseFloat(style.marginBottom),
          },
          padding: {
            top: parseFloat(style.paddingTop),
            left: parseFloat(style.paddingLeft),
            right: parseFloat(style.paddingRight),
            bottom: parseFloat(style.paddingBottom),
          },
        };
      };

      const buildTree = (element, parentRect, parentStyle) => {
        const children = [];
        const currentRect = element.getBoundingClientRect();
        const currentStyle = window.getComputedStyle(element);

        for (const child of element.children) {
          children.push(buildTree(child, currentRect, currentStyle));
        }
        return {
          id: element.id,
          tagName: element.tagName,
          className: element.className,
          geometry: getGeometry(element, parentRect, parentStyle),
          children: children.length > 0 ? children : undefined,
        };
      };

      const rootElement = document.querySelector(selector);
      if (!rootElement) return null;
      
      const rootRect = rootElement.getBoundingClientRect();
      // For the root element, its conceptual parent is a "viewport" at (0,0) with no padding.
      // We subtract its own top-left to make its own position (0,0).
      const conceptualParentRect = { left: rootRect.left, top: rootRect.top };
      const conceptualParentStyle = { paddingLeft: '0px', paddingTop: '0px' };

      return buildTree(rootElement, conceptualParentRect, conceptualParentStyle);
    }, componentSelector);

    // Save the actual DOM structure with geometry
    if (actualTree) {
      const actualGeometryPath = path.join(resultsDir, 'actual.json');
      fs.writeFileSync(actualGeometryPath, JSON.stringify(actualTree, null, 2));
    }

        // 使用 element.screenshot 并配置正确的 deviceScaleFactor
    const scaleFactor = screenshotOptions.deviceScaleFactor || 1;
    
    // 使用 page.screenshot 配合动态 clip 以支持 deviceScaleFactor
    const boundingBox = await element.boundingBox();
    if (!boundingBox) {
      throw new Error('无法获取元素位置信息');
    }
    
    const actualClip = {
      x: boundingBox.x,
      y: boundingBox.y,
      width: boundingBox.width,
      height: boundingBox.height
    };
    
    const finalScreenshotOptions = { 
      path: actualPath,
      omitBackground: true,
      deviceScaleFactor: scaleFactor,
      clip: actualClip
    };
    
    console.log(`生成${scaleFactor}倍图: x=${actualClip.x}, y=${actualClip.y}, w=${actualClip.width}, h=${actualClip.height}`);
    await page.screenshot(finalScreenshotOptions);

  } finally {
    if (browser) {
      await browser.close();
    }
  }
  // --- Puppeteer logic ends ---

  // 对比
  const result = await compareImages(expectedPath, actualPath, diffPath);

  console.log('对比结果:');
  console.log(`- 匹配度: ${result.matchPercentage.toFixed(2)}%`);

  // 保存测试结果到JSON文件
  const resultJsonPath = path.join(resultsDir, 'result.json');
  const resultData = { ...result, componentName, timestamp: new Date().toISOString() };
  fs.writeFileSync(resultJsonPath, JSON.stringify(resultData, null, 2));

  return result;
}

// 命令行调用
const __filename = fileURLToPath(import.meta.url);
if (process.argv[1] === path.resolve(__filename)) {
  try {
    const componentName = process.argv.find(arg => arg.startsWith('--component='))?.split('=')[1] || 'ZoomMenu';
    await simpleTest(componentName);
    process.exit(0);
  } catch (error) {
    console.error('测试过程中发生错误:', error);
    process.exit(1);
  }
} 