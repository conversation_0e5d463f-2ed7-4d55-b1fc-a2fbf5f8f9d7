/**
 * Figma设计稿过滤层分析工具
 * 用于识别设计稿中不符合Web最佳实践的结构，并提供转换建议
 */

import fs from 'fs';
import path from 'path';
import FILTER_RULES_CONFIG from './filter-rules-config.mjs';

export class DesignFilterAnalyzer {
  constructor(customRules = {}) {
    // 合并默认规则和自定义规则
    this.rulesConfig = this.mergeRules(FILTER_RULES_CONFIG, customRules);
    this.enabledRules = this.getEnabledRules();
    this.context = {}; // 全局上下文，用于跨节点的状态跟踪
  }

  /**
   * 合并规则配置
   */
  mergeRules(defaultRules, customRules) {
    // 使用深拷贝，但保持函数引用
    const merged = {};
    
    // 复制默认规则
    Object.entries(defaultRules).forEach(([category, rules]) => {
      merged[category] = {};
      Object.entries(rules).forEach(([ruleName, ruleConfig]) => {
        merged[category][ruleName] = { ...ruleConfig };
      });
    });
    
    // 递归合并自定义规则，只覆盖配置属性，保持check函数
    Object.keys(customRules).forEach(category => {
      if (merged[category]) {
        Object.keys(customRules[category]).forEach(ruleName => {
          if (merged[category][ruleName]) {
            // 只合并配置属性，保持check函数
            const originalCheck = merged[category][ruleName].check;
            Object.assign(merged[category][ruleName], customRules[category][ruleName]);
            // 确保check函数不被覆盖
            if (originalCheck && typeof originalCheck === 'function') {
              merged[category][ruleName].check = originalCheck;
            }
          } else {
            merged[category][ruleName] = { ...customRules[category][ruleName] };
          }
        });
      } else {
        merged[category] = {};
        Object.entries(customRules[category]).forEach(([ruleName, ruleConfig]) => {
          merged[category][ruleName] = { ...ruleConfig };
        });
      }
    });
    
    return merged;
  }

  /**
   * 获取启用的规则
   */
  getEnabledRules() {
    const enabledRules = [];
    
    Object.entries(this.rulesConfig).forEach(([category, rules]) => {
      Object.entries(rules).forEach(([ruleName, ruleConfig]) => {
        if (ruleConfig.enabled) {
          enabledRules.push({
            category,
            ruleName,
            name: ruleConfig.name,
            description: ruleConfig.description,
            severity: ruleConfig.severity,
            enabled: ruleConfig.enabled,
            check: ruleConfig.check,
            // 保持所有原始属性
            ...ruleConfig
          });
        }
      });
    });
    
    return enabledRules;
  }

  /**
   * 启用/禁用特定规则
   */
  toggleRule(category, ruleName, enabled) {
    if (this.rulesConfig[category] && this.rulesConfig[category][ruleName]) {
      this.rulesConfig[category][ruleName].enabled = enabled;
      this.enabledRules = this.getEnabledRules();
    }
  }

  /**
   * 设置规则配置
   */
  setRuleConfig(category, ruleName, config) {
    if (this.rulesConfig[category] && this.rulesConfig[category][ruleName]) {
      Object.assign(this.rulesConfig[category][ruleName], config);
    }
  }

  /**
   * 分析Figma节点数据，识别设计问题
   */
  analyzeDesign(figmaData, options = {}) {
    const issues = [];
    const suggestions = [];
    const transforms = [];
    
    // 重置上下文
    this.context = {};
    
    this.traverseNodes(figmaData.nodes, (node, depth) => {
      // 应用所有启用的规则
      this.enabledRules.forEach(rule => {
        try {
          const result = rule.check(node, depth, rule, this.context);
          
          if (result && result.hasIssue) {
            issues.push({
              nodeId: node.id,
              nodeName: node.name,
              nodeType: node.type,
              rule: rule.name,
              category: rule.category,
              description: rule.description,
              issue: result.issue,
              severity: result.severity || rule.severity,
              depth: depth
            });
          }
          
          if (result && result.suggestion) {
            suggestions.push({
              nodeId: node.id,
              nodeName: node.name,
              nodeType: node.type,
              rule: rule.name,
              category: rule.category,
              suggestion: result.suggestion,
              depth: depth
            });
          }
          
          if (result && result.transform) {
            transforms.push({
              nodeId: node.id,
              nodeName: node.name,
              rule: rule.name,
              category: rule.category,
              transform: result.transform,
              depth: depth
            });
          }
        } catch (error) {
          console.warn(`规则 ${rule.name} 执行失败:`, error.message);
        }
      });
    });

    const analysis = {
      issues,
      suggestions,
      transforms,
      summary: this.generateSummary(issues, suggestions, transforms),
      rulesApplied: this.enabledRules.length,
      metadata: {
        analyzedAt: new Date().toISOString(),
        totalNodes: this.countNodes(figmaData.nodes),
        figmaFile: options.filename || 'unknown'
      }
    };

    return analysis;
  }

  /**
   * 计算节点总数
   */
  countNodes(nodes) {
    let count = 0;
    this.traverseNodes(nodes, () => count++);
    return count;
  }



  /**
   * 生成优化建议的Vue组件结构
   */
  generateOptimizedStructure(figmaData, issues, suggestions) {
    const optimizedStructure = {
      template: this.generateOptimizedTemplate(figmaData),
      style: this.generateOptimizedCSS(figmaData),
      improvements: this.listImprovements(issues, suggestions)
    };
    
    return optimizedStructure;
  }

  /**
   * 遍历节点树
   */
  traverseNodes(nodes, callback, depth = 0) {
    if (!Array.isArray(nodes)) return;
    
    nodes.forEach(node => {
      callback(node, depth);
      if (node.children) {
        this.traverseNodes(node.children, callback, depth + 1);
      }
    });
  }

  /**
   * 生成分析摘要
   */
  generateSummary(issues, suggestions, transforms) {
    const severityCount = issues.reduce((acc, issue) => {
      acc[issue.severity] = (acc[issue.severity] || 0) + 1;
      return acc;
    }, {});

    const categoryCount = [...issues, ...suggestions].reduce((acc, item) => {
      acc[item.category] = (acc[item.category] || 0) + 1;
      return acc;
    }, {});

    return {
      totalIssues: issues.length,
      totalSuggestions: suggestions.length,
      totalTransforms: transforms?.length || 0,
      severityBreakdown: severityCount,
      categoryBreakdown: categoryCount,
      mainProblems: this.getMainProblems(issues),
      recommendedActions: this.getRecommendedActions(suggestions),
      transformActions: this.getTransformActions(transforms || []),
      riskLevel: this.calculateRiskLevel(issues)
    };
  }

  /**
   * 计算风险等级
   */
  calculateRiskLevel(issues) {
    const highCount = issues.filter(i => i.severity === 'high').length;
    const mediumCount = issues.filter(i => i.severity === 'medium').length;
    
    if (highCount > 3) return 'critical';
    if (highCount > 0 || mediumCount > 5) return 'high';
    if (mediumCount > 0) return 'medium';
    return 'low';
  }

  /**
   * 获取转换操作摘要
   */
  getTransformActions(transforms) {
    const actionCount = transforms.reduce((acc, transform) => {
      const action = transform.transform.action;
      acc[action] = (acc[action] || 0) + 1;
      return acc;
    }, {});

    return Object.entries(actionCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([action, count]) => ({ action, count }));
  }

  getMainProblems(issues) {
    const problemCount = issues.reduce((acc, issue) => {
      const key = `${issue.category}_${issue.rule}`;
      acc[key] = {
        category: issue.category,
        rule: issue.rule,
        description: issue.description,
        count: (acc[key]?.count || 0) + 1,
        maxSeverity: this.getMaxSeverity(acc[key]?.maxSeverity, issue.severity)
      };
      return acc;
    }, {});

    return Object.values(problemCount)
      .sort((a, b) => {
        const severityWeight = { critical: 4, high: 3, medium: 2, low: 1 };
        const aWeight = severityWeight[a.maxSeverity] * a.count;
        const bWeight = severityWeight[b.maxSeverity] * b.count;
        return bWeight - aWeight;
      })
      .slice(0, 5);
  }

  getMaxSeverity(current, newSeverity) {
    const severityOrder = ['low', 'medium', 'high', 'critical'];
    const currentIndex = severityOrder.indexOf(current) || 0;
    const newIndex = severityOrder.indexOf(newSeverity) || 0;
    return severityOrder[Math.max(currentIndex, newIndex)];
  }

  getRecommendedActions(suggestions) {
    return suggestions
      .slice(0, 5)
      .map(s => ({
        category: s.category,
        rule: s.rule,
        nodeName: s.nodeName,
        suggestion: s.suggestion
      }));
  }
}

// 使用示例
export async function analyzeDesignFile(figmaFilePath) {
  const analyzer = new DesignFilterAnalyzer();
  
  try {
    const figmaData = JSON.parse(fs.readFileSync(figmaFilePath, 'utf8'));
    const analysis = analyzer.analyzeDesign(figmaData);
    
    console.log('🔍 设计稿分析结果:');
    console.log('📊 问题统计:', analysis.summary);
    console.log('⚠️  发现的问题:', analysis.issues);
    console.log('💡 优化建议:', analysis.suggestions);
    
    return analysis;
  } catch (error) {
    console.error('分析失败:', error);
    return null;
  }
}

// 导出主要功能已在class声明时完成 