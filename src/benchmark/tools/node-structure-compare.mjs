#!/usr/bin/env node

/**
 * Node结构对比工具
 * 用于比较设计稿Node结构和实际DOM渲染结构
 */

import puppeteer from 'puppeteer';
import fs from 'fs';
import path from 'path';

/**
 * 在浏览器中获取DOM Node结构
 */
async function getDOMNodeStructure(page, selector) {
  return await page.evaluate((sel) => {
    // 将Node类相关代码注入到浏览器环境
    function createNodeFromDOM(element, id) {
      const computedStyle = window.getComputedStyle(element);
      const rect = element.getBoundingClientRect();
      
      // 获取样式 (只保留有意义的样式)
      const style = {};
      
      if (computedStyle.backgroundColor !== 'rgba(0, 0, 0, 0)') {
        style.backgroundColor = computedStyle.backgroundColor;
      }
      if (computedStyle.color !== 'rgba(0, 0, 0, 0)') {
        style.color = computedStyle.color;
      }
      if (computedStyle.fontSize !== '16px') {
        style.fontSize = computedStyle.fontSize;
      }
      if (computedStyle.fontWeight !== '400') {
        style.fontWeight = computedStyle.fontWeight;
      }
      if (computedStyle.fontFamily) {
        style.fontFamily = computedStyle.fontFamily;
      }
      if (computedStyle.lineHeight !== 'normal') {
        style.lineHeight = computedStyle.lineHeight;
      }
      if (computedStyle.textAlign !== 'start') {
        style.textAlign = computedStyle.textAlign;
      }
      if (computedStyle.borderRadius !== '0px') {
        style.borderRadius = computedStyle.borderRadius;
      }
      if (computedStyle.border !== '0px none rgba(0, 0, 0, 0)') {
        style.border = computedStyle.border;
      }
      if (computedStyle.boxShadow !== 'none') {
        style.boxShadow = computedStyle.boxShadow;
      }
      if (computedStyle.opacity !== '1') {
        style.opacity = parseFloat(computedStyle.opacity);
      }
      if (computedStyle.display !== 'block') {
        style.display = computedStyle.display;
      }
      if (computedStyle.position !== 'static') {
        style.position = computedStyle.position;
      }
      if (computedStyle.top !== 'auto') {
        style.top = computedStyle.top;
      }
      if (computedStyle.left !== 'auto') {
        style.left = computedStyle.left;
      }
      if (computedStyle.right !== 'auto') {
        style.right = computedStyle.right;
      }
      if (computedStyle.bottom !== 'auto') {
        style.bottom = computedStyle.bottom;
      }
      if (computedStyle.margin !== '0px') {
        style.margin = computedStyle.margin;
      }
      if (computedStyle.padding !== '0px') {
        style.padding = computedStyle.padding;
      }
      if (computedStyle.zIndex !== 'auto') {
        style.zIndex = parseInt(computedStyle.zIndex);
      }
      if (computedStyle.cursor !== 'auto') {
        style.cursor = computedStyle.cursor;
      }
      if (computedStyle.overflow !== 'visible') {
        style.overflow = computedStyle.overflow;
      }
      if (computedStyle.textDecoration !== 'none') {
        style.textDecoration = computedStyle.textDecoration;
      }
      if (computedStyle.transform !== 'none') {
        style.transform = computedStyle.transform;
      }
      if (computedStyle.transition !== 'all 0s ease 0s') {
        style.transition = computedStyle.transition;
      }

      const node = {
        id: id || element.id || `dom-${Math.random().toString(36).substr(2, 9)}`,
        name: element.tagName.toLowerCase() + (element.className ? `.${element.className.split(' ').join('.')}` : ''),
        type: element.tagName.toLowerCase(),
        layout: {
          width: Math.round(rect.width * 100) / 100,
          height: Math.round(rect.height * 100) / 100,
          x: Math.round(rect.left * 100) / 100,
          y: Math.round(rect.top * 100) / 100
        },
        style,
        content: element.textContent?.trim() || undefined,
        children: []
      };

      // 递归创建子节点
      Array.from(element.children).forEach((child, index) => {
        const childNode = createNodeFromDOM(child, `${node.id}-child-${index}`);
        node.children.push(childNode);
      });

      return node;
    }

    const element = document.querySelector(sel);
    if (!element) return null;
    
    return createNodeFromDOM(element, 'root');
  }, selector);
}

/**
 * 比较两个Node结构
 */
function compareNodes(expected, actual) {
  const differences = [];

  // 比较布局
  const layoutDiff = compareLayouts(expected.layout, actual.layout);
  if (layoutDiff.length > 0) {
    differences.push({
      type: 'layout',
      property: 'layout',
      expected: expected.layout,
      actual: actual.layout,
      details: layoutDiff
    });
  }

  // 比较样式
  const styleDiff = compareStyles(expected.style || {}, actual.style || {});
  if (styleDiff.length > 0) {
    differences.push({
      type: 'style',
      property: 'style',
      expected: expected.style,
      actual: actual.style,
      details: styleDiff
    });
  }

  // 比较内容
  if (expected.content !== actual.content) {
    differences.push({
      type: 'content',
      property: 'content',
      expected: expected.content,
      actual: actual.content
    });
  }

  // 比较子节点数量
  const expectedChildren = expected.children || [];
  const actualChildren = actual.children || [];
  
  if (expectedChildren.length !== actualChildren.length) {
    differences.push({
      type: 'structure',
      property: 'childrenCount',
      expected: expectedChildren.length,
      actual: actualChildren.length
    });
  }

  // 递归比较子节点
  const childComparisons = [];
  const maxChildren = Math.max(expectedChildren.length, actualChildren.length);
  
  for (let i = 0; i < maxChildren; i++) {
    const expectedChild = expectedChildren[i];
    const actualChild = actualChildren[i];
    
    if (expectedChild && actualChild) {
      const childComparison = compareNodes(expectedChild, actualChild);
      if (childComparison.differences.length > 0 || childComparison.children.some(c => c.differences.length > 0)) {
        childComparisons.push(childComparison);
      }
    } else if (expectedChild) {
      differences.push({
        type: 'structure',
        property: 'missingChild',
        expected: expectedChild.name,
        actual: null
      });
    } else if (actualChild) {
      differences.push({
        type: 'structure',
        property: 'extraChild',
        expected: null,
        actual: actualChild.name
      });
    }
  }

  return {
    nodeId: expected.id,
    nodeName: expected.name,
    differences,
    children: childComparisons,
    summary: {
      totalDifferences: differences.length + childComparisons.reduce((sum, child) => sum + child.summary.totalDifferences, 0),
      layoutDifferences: differences.filter(d => d.type === 'layout').length + childComparisons.reduce((sum, child) => sum + child.summary.layoutDifferences, 0),
      styleDifferences: differences.filter(d => d.type === 'style').length + childComparisons.reduce((sum, child) => sum + child.summary.styleDifferences, 0),
      structureDifferences: differences.filter(d => d.type === 'structure').length + childComparisons.reduce((sum, child) => sum + child.summary.structureDifferences, 0),
      contentDifferences: differences.filter(d => d.type === 'content').length + childComparisons.reduce((sum, child) => sum + child.summary.contentDifferences, 0)
    }
  };
}

/**
 * 比较布局差异
 */
function compareLayouts(expected, actual) {
  const differences = [];
  
  if (Math.abs(expected.width - actual.width) > 0.5) {
    differences.push(`width: expected ${expected.width}px, actual ${actual.width}px`);
  }
  if (Math.abs(expected.height - actual.height) > 0.5) {
    differences.push(`height: expected ${expected.height}px, actual ${actual.height}px`);
  }
  if (Math.abs(expected.x - actual.x) > 0.5) {
    differences.push(`x: expected ${expected.x}px, actual ${actual.x}px`);
  }
  if (Math.abs(expected.y - actual.y) > 0.5) {
    differences.push(`y: expected ${expected.y}px, actual ${actual.y}px`);
  }
  
  return differences;
}

/**
 * 比较样式差异
 */
function compareStyles(expected, actual) {
  const differences = [];
  const allKeys = new Set([...Object.keys(expected), ...Object.keys(actual)]);
  
  for (const key of allKeys) {
    const expectedValue = expected[key];
    const actualValue = actual[key];
    
    if (expectedValue !== actualValue) {
      differences.push(`${key}: expected "${expectedValue}", actual "${actualValue}"`);
    }
  }
  
  return differences;
}

/**
 * 生成对比报告
 */
function generateReport(comparison) {
  const lines = [];
  
  lines.push('📊 Node结构对比报告');
  lines.push('='.repeat(50));
  lines.push('');
  
  // 总体统计
  lines.push('📈 总体统计:');
  lines.push(`   总差异数: ${comparison.summary.totalDifferences}`);
  lines.push(`   布局差异: ${comparison.summary.layoutDifferences}`);
  lines.push(`   样式差异: ${comparison.summary.styleDifferences}`);
  lines.push(`   结构差异: ${comparison.summary.structureDifferences}`);
  lines.push(`   内容差异: ${comparison.summary.contentDifferences}`);
  lines.push('');
  
  // 详细差异
  addNodeDifferences(lines, comparison, 0);
  
  return lines.join('\n');
}

/**
 * 添加节点差异信息
 */
function addNodeDifferences(lines, comparison, depth) {
  const indent = '  '.repeat(depth);
  
  if (comparison.differences.length > 0) {
    lines.push(`${indent}🔍 ${comparison.nodeName} [${comparison.nodeId}]:`);
    
    comparison.differences.forEach(diff => {
      lines.push(`${indent}  ❌ ${diff.type} - ${diff.property}:`);
      if (diff.details && diff.details.length > 0) {
        diff.details.forEach(detail => {
          lines.push(`${indent}     ${detail}`);
        });
      } else {
        lines.push(`${indent}     期望: ${JSON.stringify(diff.expected)}`);
        lines.push(`${indent}     实际: ${JSON.stringify(diff.actual)}`);
      }
    });
    lines.push('');
  }
  
  // 递归处理子节点
  comparison.children.forEach(child => {
    addNodeDifferences(lines, child, depth + 1);
  });
}

/**
 * 获取差异统计
 */
function getStatistics(comparison) {
  const totalNodes = countNodes(comparison);
  const nodesWithDifferences = countNodesWithDifferences(comparison);
  const totalDifferences = comparison.summary.totalDifferences;
  
  return {
    accuracy: totalNodes > 0 ? ((totalNodes - nodesWithDifferences) / totalNodes) * 100 : 100,
    totalNodes,
    nodesWithDifferences,
    averageDifferencesPerNode: totalNodes > 0 ? totalDifferences / totalNodes : 0
  };
}

/**
 * 统计节点总数
 */
function countNodes(comparison) {
  return 1 + comparison.children.reduce((sum, child) => sum + countNodes(child), 0);
}

/**
 * 统计有差异的节点数
 */
function countNodesWithDifferences(comparison) {
  const hasOwnDifferences = comparison.differences.length > 0 ? 1 : 0;
  const childrenWithDifferences = comparison.children.reduce((sum, child) => sum + countNodesWithDifferences(child), 0);
  return hasOwnDifferences + childrenWithDifferences;
}

/**
 * 主函数
 */
async function compareNodeStructures(options = {}) {
  const {
    componentName,
    expectedNodeFile,
    url = 'http://localhost:81',
    selector = `#${componentName}-test`,
    outputDir = './results'
  } = options;

  if (!componentName) {
    console.error('❌ 请提供组件名称 --component=ComponentName');
    process.exit(1);
  }

  try {
    // 读取预期Node结构
    let expectedNode;
    if (expectedNodeFile && fs.existsSync(expectedNodeFile)) {
      const content = fs.readFileSync(expectedNodeFile, 'utf8');
      expectedNode = JSON.parse(content);
    } else {
      console.log('⚠️  未找到预期Node结构文件，将只获取实际DOM结构');
    }

    // 启动浏览器
    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    await page.setViewport({ width: 1200, height: 800 });

    // 访问页面
    const targetUrl = `${url}/benchmark/${componentName}`;
    console.log(`🌐 访问页面: ${targetUrl}`);
    await page.goto(targetUrl, { waitUntil: 'networkidle2' });

    // 等待组件加载
    await page.waitForSelector(selector, { timeout: 10000 });

    // 获取实际DOM结构
    console.log(`🔍 获取DOM结构: ${selector}`);
    const actualNode = await getDOMNodeStructure(page, selector);

    await browser.close();

    if (!actualNode) {
      console.error(`❌ 未找到选择器对应的元素: ${selector}`);
      process.exit(1);
    }

    // 确保输出目录存在
    const componentOutputDir = path.join(outputDir, componentName);
    fs.mkdirSync(componentOutputDir, { recursive: true });

    // 保存实际DOM结构
    const actualNodeFile = path.join(componentOutputDir, 'actual-node-structure.json');
    fs.writeFileSync(actualNodeFile, JSON.stringify(actualNode, null, 2));
    console.log(`💾 实际DOM结构已保存: ${actualNodeFile}`);

    if (expectedNode) {
      // 进行结构对比
      console.log('🔄 开始Node结构对比...');
      const comparison = compareNodes(expectedNode, actualNode);
      
      // 生成报告
      const report = generateReport(comparison);
      const reportFile = path.join(componentOutputDir, 'node-comparison-report.txt');
      fs.writeFileSync(reportFile, report);
      
      // 保存JSON格式的对比结果
      const jsonReportFile = path.join(componentOutputDir, 'node-comparison.json');
      fs.writeFileSync(jsonReportFile, JSON.stringify(comparison, null, 2));
      
      // 获取统计信息
      const stats = getStatistics(comparison);
      
      console.log('\n📊 Node结构对比结果:');
      console.log(`   结构准确度: ${stats.accuracy.toFixed(2)}%`);
      console.log(`   总节点数: ${stats.totalNodes}`);
      console.log(`   有差异节点数: ${stats.nodesWithDifferences}`);
      console.log(`   平均每节点差异数: ${stats.averageDifferencesPerNode.toFixed(2)}`);
      console.log(`   总差异数: ${comparison.summary.totalDifferences}`);
      console.log('');
      console.log(`📝 详细报告: ${reportFile}`);
      console.log(`📄 JSON报告: ${jsonReportFile}`);
      
      // 如果有差异，显示概要
      if (comparison.summary.totalDifferences > 0) {
        console.log('\n🚨 主要差异类型:');
        if (comparison.summary.layoutDifferences > 0) {
          console.log(`   - 布局差异: ${comparison.summary.layoutDifferences}个`);
        }
        if (comparison.summary.styleDifferences > 0) {
          console.log(`   - 样式差异: ${comparison.summary.styleDifferences}个`);
        }
        if (comparison.summary.structureDifferences > 0) {
          console.log(`   - 结构差异: ${comparison.summary.structureDifferences}个`);
        }
        if (comparison.summary.contentDifferences > 0) {
          console.log(`   - 内容差异: ${comparison.summary.contentDifferences}个`);
        }
      } else {
        console.log('✅ Node结构完全匹配！');
      }
    } else {
      console.log('✅ 实际DOM结构获取完成');
    }

  } catch (error) {
    console.error('❌ 执行过程中出现错误:', error.message);
    process.exit(1);
  }
}

// 命令行调用
if (process.argv[1] === new URL(import.meta.url).pathname) {
  const args = process.argv.slice(2);
  const options = {};
  
  args.forEach(arg => {
    if (arg.startsWith('--component=')) {
      options.componentName = arg.split('=')[1];
    } else if (arg.startsWith('--expected=')) {
      options.expectedNodeFile = arg.split('=')[1];
    } else if (arg.startsWith('--url=')) {
      options.url = arg.split('=')[1];
    } else if (arg.startsWith('--selector=')) {
      options.selector = arg.split('=')[1];
    } else if (arg.startsWith('--output=')) {
      options.outputDir = arg.split('=')[1];
    }
  });
  
  compareNodeStructures(options);
}

export { compareNodeStructures, getDOMNodeStructure }; 