#!/usr/bin/env node

/**
 * 综合对比工具
 * 同时运行像素级对比和Node结构对比，提供全面的还原质量分析
 */

import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import puppeteer from 'puppeteer';

const execAsync = promisify(exec);
const GEOMETRY_TOLERANCE = 1.5;

/**
 * 运行像素级对比
 */
async function runPixelComparison(componentName) {
  console.log('🎨 开始像素级对比...');
  try {
    const { stdout, stderr } = await execAsync(`PUPPETEER_EXECUTABLE_PATH="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome" node figma-benchmark.mjs --component=${componentName}`);
    console.log(stdout);
    if (stderr) console.error(stderr);
    return true;
  } catch (error) {
    console.error('❌ 像素级对比失败:', error.message);
    return false;
  }
}

/**
 * 运行Node结构对比
 */
async function runNodeComparison(componentName) {
  console.log('🔗 开始Node结构对比...');
  try {
    const expectedNodeFile = path.resolve('..', 'components', componentName, 'expected-node-structure.json');
    const command = `PUPPETEER_EXECUTABLE_PATH="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome" node node-structure-compare.mjs --component=${componentName} --expected=${expectedNodeFile}`;
    
    const { stdout, stderr } = await execAsync(command);
    console.log(stdout);
    if (stderr) console.error(stderr);
    return true;
  } catch (error) {
    console.error('❌ Node结构对比失败:', error.message);
    return false;
  }
}

/**
 * 读取对比结果
 */
function readComparisonResults(componentName) {
  const resultsDir = path.resolve('..', 'results', componentName);
  const results = {};
  
  try {
    // 读取像素对比结果
    const pixelReportFile = path.join(resultsDir, 'result.json');
    if (fs.existsSync(pixelReportFile)) {
      results.pixel = JSON.parse(fs.readFileSync(pixelReportFile, 'utf8'));
    }
    
    // 读取Node结构对比结果
    const nodeReportFile = path.resolve('..', 'components', componentName, 'results', componentName, 'node-comparison.json');
    if (fs.existsSync(nodeReportFile)) {
      results.node = JSON.parse(fs.readFileSync(nodeReportFile, 'utf8'));
    }
  } catch (error) {
    console.error('⚠️  读取对比结果时出错:', error.message);
  }
  
  return results;
}

/**
 * 生成综合报告
 */
function generateComprehensiveReport(componentName, results) {
  const lines = [];
  
  lines.push('📊 综合还原质量报告');
  lines.push('='.repeat(60));
  lines.push(`🎯 组件: ${componentName}`);
  lines.push(`⏰ 生成时间: ${new Date().toLocaleString('zh-CN')}`);
  lines.push('');
  
  // 像素级对比结果
  if (results.pixel) {
    lines.push('🎨 像素级对比结果:');
    lines.push(`   匹配度: ${results.pixel.matchPercentage?.toFixed(2) || 'N/A'}%`);
    lines.push(`   差异像素: ${results.pixel.diffPixels || 'N/A'}个`);
    lines.push(`   总像素: ${results.pixel.totalPixels || 'N/A'}个`);
    
    // 根据匹配度给出评级
    const similarity = results.pixel.matchPercentage || 0;
    if (similarity >= 98) {
      lines.push(`   评级: A+ (像素级还原) ⭐⭐⭐⭐⭐`);
    } else if (similarity >= 95) {
      lines.push(`   评级: A (高质量还原) ⭐⭐⭐⭐`);
    } else if (similarity >= 90) {
      lines.push(`   评级: B+ (需要优化) ⭐⭐⭐`);
    } else if (similarity >= 80) {
      lines.push(`   评级: B (需要大幅修改) ⭐⭐`);
    } else {
      lines.push(`   评级: C (必须重新实现) ⭐`);
    }
    lines.push('');
  } else {
    lines.push('🎨 像素级对比结果: 未找到数据');
    lines.push('');
  }
  
  // Node结构对比结果
  if (results.node) {
    const nodeStats = calculateNodeStatistics(results.node);
    lines.push('🔗 Node结构对比结果:');
    lines.push(`   结构准确度: ${nodeStats.accuracy.toFixed(2)}%`);
    lines.push(`   总节点数: ${nodeStats.totalNodes}`);
    lines.push(`   有差异节点数: ${nodeStats.nodesWithDifferences}`);
    lines.push(`   总差异数: ${results.node.summary.totalDifferences}`);
    lines.push('');
    
    lines.push('   差异类型分布:');
    lines.push(`     - 布局差异: ${results.node.summary.layoutDifferences}个`);
    lines.push(`     - 样式差异: ${results.node.summary.styleDifferences}个`);
    lines.push(`     - 结构差异: ${results.node.summary.structureDifferences}个`);
    lines.push(`     - 内容差异: ${results.node.summary.contentDifferences}个`);
    lines.push('');
  } else {
    lines.push('🔗 Node结构对比结果: 未找到数据');
    lines.push('');
  }
  
  // 综合分析和建议
  lines.push('💡 综合分析和建议:');
  
  if (results.pixel && results.node) {
    const pixelScore = results.pixel.matchPercentage || 0;
    const nodeScore = calculateNodeStatistics(results.node).accuracy;
    
    if (pixelScore >= 95 && nodeScore >= 80) {
      lines.push('   ✅ 整体还原质量优秀，仅需微调细节');
    } else if (pixelScore >= 90 && nodeScore >= 60) {
      lines.push('   ⚠️  还原质量良好，需要优化主要差异点');
    } else if (pixelScore >= 80 || nodeScore >= 40) {
      lines.push('   🔧 还原质量一般，需要重点关注结构和样式');
    } else {
      lines.push('   🚨 还原质量较差，建议重新实现核心部分');
    }
    
    // 具体建议
    lines.push('');
    lines.push('🎯 优先修复建议:');
    
    if (results.node.summary.structureDifferences > 0) {
      lines.push('   1. 修复DOM结构差异 - 检查元素嵌套和类名');
    }
    
    if (results.node.summary.layoutDifferences > 0) {
      lines.push('   2. 调整布局定位 - 检查宽高、定位、间距');
    }
    
    if (results.node.summary.styleDifferences > 0) {
      lines.push('   3. 优化样式属性 - 检查颜色、字体、边框等');
    }
    
    if (results.node.summary.contentDifferences > 0) {
      lines.push('   4. 修正文本内容 - 检查文字格式和内容');
    }
    
    if (pixelScore < 95) {
      lines.push('   5. 像素级细节优化 - 查看差异图重点修正红色区域');
    }
  } else {
    lines.push('   ⚠️  对比数据不完整，请重新运行完整测试');
  }
  
  lines.push('');
  lines.push('📁 详细报告文件:');
  lines.push(`   - 像素对比报告: results/${componentName}/comparison-report.json`);
  lines.push(`   - 差异图: results/${componentName}/diff.png`);
  lines.push(`   - Node结构报告: results/${componentName}/node-comparison-report.txt`);
  lines.push(`   - 实际DOM结构: results/${componentName}/actual-node-structure.json`);
  
  return lines.join('\n');
}

/**
 * 计算Node统计信息
 */
function calculateNodeStatistics(nodeComparison) {
  const totalNodes = countNodes(nodeComparison);
  const nodesWithDifferences = countNodesWithDifferences(nodeComparison);
  
  return {
    accuracy: totalNodes > 0 ? ((totalNodes - nodesWithDifferences) / totalNodes) * 100 : 100,
    totalNodes,
    nodesWithDifferences
  };
}

/**
 * 统计节点总数
 */
function countNodes(comparison) {
  return 1 + comparison.children.reduce((sum, child) => sum + countNodes(child), 0);
}

/**
 * 统计有差异的节点数
 */
function countNodesWithDifferences(comparison) {
  const hasOwnDifferences = comparison.differences.length > 0 ? 1 : 0;
  const childrenWithDifferences = comparison.children.reduce((sum, child) => sum + countNodesWithDifferences(child), 0);
  return hasOwnDifferences + childrenWithDifferences;
}

// --- Part 1: Expected Geometry Generation (from figma-data.json) ---

function generateExpectedGeometry(figmaNode, parentContentBox = { x: 0, y: 0 }) {
    const layout = {
        width: figmaNode.absoluteBoundingBox.width || 0,
        height: figmaNode.absoluteBoundingBox.height || 0,
        x: figmaNode.absoluteBoundingBox.x - (parentContentBox.x || 0),
        y: figmaNode.absoluteBoundingBox.y - (parentContentBox.y || 0),
    };

    const padding = {
        top: figmaNode.paddingTop || 0,
        left: figmaNode.paddingLeft || 0,
        right: figmaNode.paddingRight || 0,
        bottom: figmaNode.paddingBottom || 0
    };
    
    const nodeGeometry = {
        name: figmaNode.name,
        type: figmaNode.type,
        geometry: { x: layout.x, y: layout.y, width: layout.width, height: layout.height, padding },
        children: []
    };

    const selfContentBox = {
        x: figmaNode.absoluteBoundingBox.x,
        y: figmaNode.absoluteBoundingBox.y
    };

    if (figmaNode.children) {
        nodeGeometry.children = figmaNode.children.map(child => generateExpectedGeometry(child, selfContentBox));
    }

    return nodeGeometry;
}


// --- Part 2: Actual Geometry Generation (from Puppeteer) ---

async function generateActualGeometry(componentName) {
    let browser;
    try {
        browser = await puppeteer.launch({ headless: true });
        const page = await browser.newPage();
        await page.goto(`http://localhost:81/benchmark/${componentName}`, { waitUntil: 'networkidle0' });
        const testElementId = `#${componentName}-test`;
        await page.waitForSelector(testElementId);
        
        const actualTree = await page.evaluate((selector) => {
            function buildTree(element, parentRect, parentStyle) {
                const rect = element.getBoundingClientRect();
                const style = window.getComputedStyle(element);
                const parentPadding = {
                    left: parseFloat(parentStyle.paddingLeft),
                    top: parseFloat(parentStyle.paddingTop)
                };

                const relativeX = rect.left - (parentRect.left + parentPadding.left);
                const relativeY = rect.top - (parentRect.top + parentPadding.top);

                const children = [];
                for (const child of element.children) {
                    children.push(buildTree(child, rect, style));
                }
                
                return {
                    name: element.className || element.tagName,
                    type: element.tagName,
                    geometry: {
                        x: relativeX, y: relativeY, width: rect.width, height: rect.height,
                        padding: { top: parseFloat(style.paddingTop), left: parseFloat(style.paddingLeft), right: parseFloat(style.paddingRight), bottom: parseFloat(style.paddingBottom) }
                    },
                    children: children.length > 0 ? children : undefined,
                };
            }

            const rootEl = document.querySelector(selector);
            const rootRect = rootEl.getBoundingClientRect();
            const conceptualParentRect = { left: rootRect.left, top: rootRect.top };
            const conceptualParentStyle = { paddingLeft: '0px', paddingTop: '0px' };
            return buildTree(rootEl, conceptualParentRect, conceptualParentStyle);
        }, testElementId);

        return actualTree;
    } finally {
        if (browser) await browser.close();
    }
}

// --- Part 3: Comparison Logic ---

function compareNodes(expected, actual, path) {
    const diffs = [];
    if (!expected || !actual) return diffs;

    const propsToCompare = ['x', 'y', 'width', 'height'];
    for (const key of propsToCompare) {
        const diff = Math.abs(actual.geometry[key] - expected.geometry[key]);
        if (diff > GEOMETRY_TOLERANCE) {
            diffs.push({ path, property: `geometry.${key}`, expected: expected.geometry[key], actual: actual.geometry[key], diff });
        }
    }
    // Simplified padding check
    const paddingDiff = Math.abs(actual.geometry.padding.left - expected.geometry.padding.left);
    if(paddingDiff > GEOMETRY_TOLERANCE) {
         diffs.push({ path, property: `geometry.padding.left`, expected: expected.geometry.padding.left, actual: actual.geometry.padding.left, diff: paddingDiff });
    }


    const len = Math.min(expected.children?.length || 0, actual.children?.length || 0);
    for(let i=0; i<len; i++){
        diffs.push(...compareNodes(expected.children[i], actual.children[i], `${path}.${actual.children[i].name}[${i}]`));
    }
    return diffs;
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  let componentName = '';
  
  args.forEach(arg => {
    if (arg.startsWith('--component=')) {
      componentName = arg.split('=')[1];
    }
  });
  
  if (!componentName) {
    console.error('❌ 请提供组件名称 --component=ComponentName');
    process.exit(1);
  }
  
  console.log(`🚀 开始综合对比测试: ${componentName}`);
  console.log('='.repeat(50));
  console.log('');
  
  // 运行像素级对比
  const pixelSuccess = await runPixelComparison(componentName);
  
  console.log('');
  
  // 运行Node结构对比
  const nodeSuccess = await runNodeComparison(componentName);
  
  console.log('');
  console.log('📋 生成综合报告...');
  
  // 读取对比结果
  const results = readComparisonResults(componentName);
  
  // 生成综合报告
  const report = generateComprehensiveReport(componentName, results);
  
  // 保存综合报告
  const reportDir = path.resolve('..', 'results', componentName);
  fs.mkdirSync(reportDir, { recursive: true });
  
  const reportFile = path.join(reportDir, 'comprehensive-report.txt');
  fs.writeFileSync(reportFile, report);
  
  // 输出报告
  console.log(report);
  console.log('');
  console.log(`📄 综合报告已保存: ${reportFile}`);
  
  // 总结
  if (pixelSuccess && nodeSuccess) {
    console.log('✅ 综合对比测试完成！');
  } else {
    console.log('⚠️  部分测试失败，请检查详细日志');
  }
}

// 命令行调用
if (process.argv[1] === new URL(import.meta.url).pathname) {
  main().catch(error => {
    console.error('❌ 执行过程中出现错误:', error.message);
    process.exit(1);
  });
}

export { runPixelComparison, runNodeComparison, generateComprehensiveReport }; 