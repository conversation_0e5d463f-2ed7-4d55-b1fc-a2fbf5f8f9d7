#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { PNG } from 'pngjs';

/**
 * 获取PNG图片尺寸
 */
function getPngDimensions(filePath) {
  const buffer = fs.readFileSync(filePath);
  const width = buffer.readUInt32BE(16);
  const height = buffer.readUInt32BE(20);
  return { width, height, size: buffer.length };
}

/**
 * 调整图片尺寸以匹配目标尺寸
 */
function resizeImage(sourcePath, targetPath, targetWidth, targetHeight) {
  const sourceBuffer = fs.readFileSync(sourcePath);
  const sourcePng = PNG.sync.read(sourceBuffer);
  
  // 创建目标尺寸的PNG
  const targetPng = new PNG({
    width: targetWidth,
    height: targetHeight,
    filterType: 4
  });
  
  // 计算缩放比例
  const scaleX = targetWidth / sourcePng.width;
  const scaleY = targetHeight / sourcePng.height;
  
  console.log(`🔄 调整图片尺寸:`);
  console.log(`   源尺寸: ${sourcePng.width} × ${sourcePng.height}`);
  console.log(`   目标尺寸: ${targetWidth} × ${targetHeight}`);
  console.log(`   缩放比例: X=${scaleX.toFixed(4)}, Y=${scaleY.toFixed(4)}`);
  
  // 简单的最近邻插值
  for (let y = 0; y < targetHeight; y++) {
    for (let x = 0; x < targetWidth; x++) {
      const sourceX = Math.floor(x / scaleX);
      const sourceY = Math.floor(y / scaleY);
      
      if (sourceX < sourcePng.width && sourceY < sourcePng.height) {
        const sourceIdx = (sourcePng.width * sourceY + sourceX) << 2;
        const targetIdx = (targetWidth * y + x) << 2;
        
        targetPng.data[targetIdx] = sourcePng.data[sourceIdx];     // R
        targetPng.data[targetIdx + 1] = sourcePng.data[sourceIdx + 1]; // G
        targetPng.data[targetIdx + 2] = sourcePng.data[sourceIdx + 2]; // B
        targetPng.data[targetIdx + 3] = sourcePng.data[sourceIdx + 3]; // A
      }
    }
  }
  
  // 保存调整后的图片
  const buffer = PNG.sync.write(targetPng);
  fs.writeFileSync(targetPath, buffer);
  
  return {
    originalSize: sourceBuffer.length,
    newSize: buffer.length,
    compression: ((sourceBuffer.length - buffer.length) / sourceBuffer.length * 100).toFixed(1)
  };
}

/**
 * 同步两个图片的尺寸
 */
function syncDimensions(expectedPath, actualPath, strategy = 'resize-expected') {
  console.log('=== 图片尺寸同步工具 ===\n');
  
  // 检查文件是否存在
  if (!fs.existsSync(expectedPath)) {
    throw new Error(`Expected图片不存在: ${expectedPath}`);
  }
  if (!fs.existsSync(actualPath)) {
    throw new Error(`Actual图片不存在: ${actualPath}`);
  }
  
  // 获取当前尺寸
  const expected = getPngDimensions(expectedPath);
  const actual = getPngDimensions(actualPath);
  
  console.log('📐 当前尺寸:');
  console.log(`   Expected: ${expected.width} × ${expected.height} (${(expected.size/1024).toFixed(1)}KB)`);
  console.log(`   Actual: ${actual.width} × ${actual.height} (${(actual.size/1024).toFixed(1)}KB)`);
  
  // 检查是否需要同步
  if (expected.width === actual.width && expected.height === actual.height) {
    console.log('✅ 图片尺寸已经一致，无需同步');
    return;
  }
  
  console.log(`\n🔧 执行同步策略: ${strategy}`);
  
  const backupDir = path.dirname(expectedPath);
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  
  if (strategy === 'resize-expected') {
    // 策略1: 调整expected图片尺寸以匹配actual
    const backupPath = path.join(backupDir, `expected_backup_${timestamp}.png`);
    fs.copyFileSync(expectedPath, backupPath);
    console.log(`💾 备份原始expected图片到: ${path.basename(backupPath)}`);
    
    const result = resizeImage(expectedPath, expectedPath, actual.width, actual.height);
    console.log(`✅ Expected图片已调整为 ${actual.width} × ${actual.height}`);
    console.log(`   文件大小: ${(expected.size/1024).toFixed(1)}KB → ${(result.newSize/1024).toFixed(1)}KB`);
    
  } else if (strategy === 'resize-actual') {
    // 策略2: 调整actual图片尺寸以匹配expected
    const backupPath = path.join(path.dirname(actualPath), `actual_backup_${timestamp}.png`);
    fs.copyFileSync(actualPath, backupPath);
    console.log(`💾 备份原始actual图片到: ${path.basename(backupPath)}`);
    
    const result = resizeImage(actualPath, actualPath, expected.width, expected.height);
    console.log(`✅ Actual图片已调整为 ${expected.width} × ${expected.height}`);
    console.log(`   文件大小: ${(actual.size/1024).toFixed(1)}KB → ${(result.newSize/1024).toFixed(1)}KB`);
    
  } else {
    throw new Error(`未知的同步策略: ${strategy}`);
  }
  
  console.log('\n🎯 同步完成！现在可以重新运行像素对比测试。');
}

// 命令行调用
if (process.argv[1] === new URL(import.meta.url).pathname) {
  const componentName = process.argv[2];
  const strategy = process.argv[3] || 'resize-expected';
  
  if (!componentName) {
    console.log('用法: node sync-dimensions.mjs <组件名> [策略]');
    console.log('策略选项:');
    console.log('  resize-expected (默认) - 调整expected图片尺寸匹配actual');
    console.log('  resize-actual - 调整actual图片尺寸匹配expected');
    console.log('');
    console.log('示例: node sync-dimensions.mjs ContextMenuDocuments resize-expected');
    process.exit(1);
  }
  
  try {
    const expectedPath = path.resolve(`../components/${componentName}/expected.png`);
    const actualPath = path.resolve(`../results/${componentName}/actual.png`);
    
    syncDimensions(expectedPath, actualPath, strategy);
  } catch (error) {
    console.error('❌ 错误:', error.message);
    process.exit(1);
  }
}

export { syncDimensions, getPngDimensions, resizeImage }; 