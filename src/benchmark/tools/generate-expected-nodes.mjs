#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 导入组件的Node结构定义
async function loadComponentNodes(componentName) {
  try {
    const modulePath = path.resolve(__dirname, `../components/${componentName}/DocumentSortMenuNodes.ts`)
    
    // 这里我们直接创建简化的Node结构
    const expectedNodes = {
      id: "DocumentSortMenu-test",
      name: "div.document-sort-menu", 
      type: "div",
      layout: {
        width: 244,
        height: 319,
        x: 0,
        y: 0
      },
      style: {
        backgroundColor: "#FFFFFF",
        borderRadius: "12px",
        boxShadow: "0px 5px 30px 0px rgba(48, 61, 60, 0.15), 0px 2px 8px 0px rgba(48, 61, 60, 0.1)",
        padding: "4px 0px",
        display: "flex",
        flexDirection: "column"
      },
      content: undefined,
      children: [
        // 菜单项1 - 选中状态
        {
          id: "menu-item-1",
          name: "div.menu-item.menu-item--selected",
          type: "div",
          layout: { width: 244, height: 32, x: 0, y: 4 },
          style: {
            display: "flex",
            flexDirection: "row", 
            alignItems: "center",
            padding: "6px 16px 6px 12px"
          },
          content: "修改时间新→旧",
          children: [
            {
              id: "menu-item-1-icon",
              name: "img.check-icon",
              type: "img",
              layout: { width: 16, height: 16, x: 12, y: 12 },
              style: { marginRight: "4px", opacity: 1 },
              children: []
            },
            {
              id: "menu-item-1-text", 
              name: "span.menu-text",
              type: "span",
              layout: { width: 196, height: 20, x: 32, y: 10 },
              style: {
                fontFamily: '"PingFang SC", -apple-system, BlinkMacSystemFont, sans-serif',
                fontWeight: "400",
                fontSize: "14px", 
                lineHeight: "20px",
                color: "#212121",
                textAlign: "left",
                whiteSpace: "nowrap",
                flex: "1"
              },
              content: "修改时间新→旧",
              children: []
            }
          ]
        },
        // 菜单项2 - 默认状态
        {
          id: "menu-item-2",
          name: "div.menu-item",
          type: "div", 
          layout: { width: 244, height: 32, x: 0, y: 36 },
          style: {
            display: "flex",
            flexDirection: "row",
            alignItems: "center", 
            padding: "6px 16px 6px 12px"
          },
          content: "修改时间旧→新",
          children: [
            {
              id: "menu-item-2-icon",
              name: "img.check-icon.check-icon--hidden",
              type: "img",
              layout: { width: 16, height: 16, x: 12, y: 44 },
              style: { marginRight: "4px", opacity: 0 },
              children: []
            },
            {
              id: "menu-item-2-text",
              name: "span.menu-text", 
              type: "span",
              layout: { width: 196, height: 20, x: 32, y: 42 },
              style: {
                fontFamily: '"PingFang SC", -apple-system, BlinkMacSystemFont, sans-serif',
                fontWeight: "400",
                fontSize: "14px",
                lineHeight: "20px", 
                color: "#212121",
                textAlign: "left",
                whiteSpace: "nowrap",
                flex: "1"
              },
              content: "修改时间旧→新",
              children: []
            }
          ]
        },
        // 分割线1
        {
          id: "divider-1",
          name: "div.divider",
          type: "div",
          layout: { width: 244, height: 1, x: 0, y: 68 },
          style: {
            backgroundColor: "#F1F1F1",
            margin: "4px 0"
          },
          children: []
        },
        // 继续其他菜单项...
        {
          id: "menu-item-3",
          name: "div.menu-item",
          type: "div",
          layout: { width: 244, height: 32, x: 0, y: 73 },
          style: {
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            padding: "6px 16px 6px 12px"
          },
          content: "创建时间新→旧",
          children: [
            {
              id: "menu-item-3-icon",
              name: "img.check-icon.check-icon--hidden",
              type: "img", 
              layout: { width: 16, height: 16, x: 12, y: 81 },
              style: { marginRight: "4px", opacity: 0 },
              children: []
            },
            {
              id: "menu-item-3-text",
              name: "span.menu-text",
              type: "span",
              layout: { width: 196, height: 20, x: 32, y: 79 },
              style: {
                fontFamily: '"PingFang SC", -apple-system, BlinkMacSystemFont, sans-serif',
                fontWeight: "400",
                fontSize: "14px",
                lineHeight: "20px",
                color: "#212121", 
                textAlign: "left",
                whiteSpace: "nowrap",
                flex: "1"
              },
              content: "创建时间新→旧",
              children: []
            }
          ]
        }
        // 为了简化，这里只展示几个关键节点
      ]
    }
    
    return expectedNodes
  } catch (error) {
    console.error(`❌ 加载组件Node结构失败: ${error.message}`)
    return null
  }
}

async function generateExpectedNodes(componentName) {
  try {
    console.log(`🔄 正在生成 ${componentName} 的预期Node结构...`)
    
    const expectedNodes = await loadComponentNodes(componentName)
    if (!expectedNodes) {
      throw new Error('无法生成预期Node结构')
    }
    
    // 确保输出目录存在
    const outputDir = path.resolve(__dirname, `../components/${componentName}`)
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
    }
    
    // 保存预期Node结构
    const outputPath = path.join(outputDir, 'expected-node-structure.json')
    fs.writeFileSync(outputPath, JSON.stringify(expectedNodes, null, 2), 'utf8')
    
    console.log(`✅ 预期Node结构已保存: ${outputPath}`)
    
    // 输出统计信息
    function countNodes(node) {
      return 1 + (node.children ? node.children.reduce((sum, child) => sum + countNodes(child), 0) : 0)
    }
    
    const totalNodes = countNodes(expectedNodes)
    console.log(`📊 预期Node结构统计:`)
    console.log(`   总节点数: ${totalNodes}`)
    console.log(`   根节点: ${expectedNodes.name}`)
    console.log(`   尺寸: ${expectedNodes.layout.width}×${expectedNodes.layout.height}`)
    
    return expectedNodes
  } catch (error) {
    console.error(`❌ 生成预期Node结构失败: ${error.message}`)
    process.exit(1)
  }
}

// 解析命令行参数
const args = process.argv.slice(2)
let componentName = 'DocumentSortMenu'

for (let i = 0; i < args.length; i++) {
  if (args[i] === '--component' && i + 1 < args.length) {
    componentName = args[i + 1]
    break
  }
}

// 主执行函数
async function main() {
  console.log(`🚀 开始生成 ${componentName} 的预期Node结构`)
  console.log('=' .repeat(50))
  
  await generateExpectedNodes(componentName)
  
  console.log('\n✅ Node结构生成完成！')
}

// 检测是否为直接运行
if (process.argv[1] === fileURLToPath(import.meta.url)) {
  main().catch(error => {
    console.error('❌ 脚本执行失败:', error)
    process.exit(1)
  })
}

export { generateExpectedNodes } 