/**
 * Figma设计稿过滤规则配置
 * 将所有过滤规则标准化和模块化
 */

export const FILTER_RULES_CONFIG = {
  // ==================== 结构层级规则 ====================
  structure: {
    // 冗余容器检测
    redundant_containers: {
      name: 'redundant_containers',
      description: '冗余容器检测',
      category: 'structure',
      severity: 'low',
      enabled: true,
      check: (node) => {
        if (node.type === 'FRAME' && node.children?.length === 1) {
          const child = node.children[0];
          const hasUniqueStyle = !!(
            node.fills?.length > 0 ||
            node.strokes?.length > 0 ||
            node.borderRadius ||
            node.opacity !== undefined ||
            node.layout?.padding ||
            node.layout?.margin
          );
          
          if (!hasUniqueStyle) {
            return {
              hasIssue: true,
              issue: '单子元素容器，无特殊样式，可以扁平化',
              suggestion: `将 ${child.name} 提升到父级，移除 ${node.name} 容器`,
              transform: {
                action: 'flatten',
                removeNode: node.id,
                promoteChild: child.id
              }
            };
          }
        }
        return { hasIssue: false };
      }
    },

    // 过深嵌套检测
    deep_nesting: {
      name: 'deep_nesting',
      description: '过深嵌套检测',
      category: 'structure',
      severity: 'high',
      enabled: true,
      maxDepth: 5,
      check: (node, depth = 0, config) => {
        const maxDepth = config.maxDepth || 5;
        if (depth > maxDepth) {
          return {
            hasIssue: true,
            issue: `嵌套层级过深（${depth}层），影响性能和可维护性`,
            suggestion: '考虑重构布局结构，减少嵌套层级',
            transform: {
              action: 'restructure',
              currentDepth: depth,
              recommendedDepth: maxDepth
            }
          };
        }
        return { hasIssue: false };
      }
    },

    // 空容器检测
    empty_containers: {
      name: 'empty_containers',
      description: '空容器检测',
      category: 'structure',
      severity: 'medium',
      enabled: true,
      check: (node) => {
        if ((node.type === 'FRAME' || node.type === 'GROUP') && 
            (!node.children || node.children.length === 0)) {
          return {
            hasIssue: true,
            issue: '空容器，没有子元素',
            suggestion: '移除空容器或添加必要的内容',
            transform: {
              action: 'remove',
              nodeId: node.id
            }
          };
        }
        return { hasIssue: false };
      }
    }
  },

  // ==================== 命名规范规则 ====================
  naming: {
    // 技术性命名检测
    technical_names: {
      name: 'technical_names',
      description: '技术性命名检测',
      category: 'naming',
      severity: 'medium',
      enabled: true,
      patterns: [
        /^(Vector|Rectangle|Ellipse|Group)\s+\d+$/,
        /^(Frame|Component)\s+\d+$/,
        /^(Path|Shape|Line)\s+\d+$/,
        /^Union\s+\d+$/,
        /^Subtract\s+\d+$/
      ],
      check: (node, depth, config) => {
        const isTechnical = config.patterns.some(pattern => pattern.test(node.name));
        
        if (isTechnical) {
          const suggestedName = suggestSemanticName(node);
          return {
            hasIssue: true,
            issue: '使用了技术性命名，不够语义化',
            suggestion: `建议重命名为: ${suggestedName}`,
            transform: {
              action: 'rename',
              oldName: node.name,
              newName: suggestedName
            }
          };
        }
        return { hasIssue: false };
      }
    },

    // 中文命名规范化
    chinese_naming: {
      name: 'chinese_naming',
      description: '中文命名规范化',
      category: 'naming',
      severity: 'low',
      enabled: true,
      check: (node) => {
        const hasChinese = /[\u4e00-\u9fff]/.test(node.name);
        const hasSpecialChars = /[^\w\u4e00-\u9fff\-_\/]/.test(node.name);
        
        if (hasChinese && hasSpecialChars) {
          const cleanName = node.name.replace(/[^\w\u4e00-\u9fff\-_\/]/g, '_');
          return {
            hasIssue: true,
            issue: '中文命名包含特殊字符，可能影响代码生成',
            suggestion: `建议规范化为: ${cleanName}`,
            transform: {
              action: 'normalize',
              oldName: node.name,
              newName: cleanName
            }
          };
        }
        return { hasIssue: false };
      }
    },

    // 重复命名检测
    duplicate_names: {
      name: 'duplicate_names',
      description: '重复命名检测',
      category: 'naming',
      severity: 'medium',
      enabled: true,
      check: (node, depth, config, context) => {
        if (!context.nameCounter) {
          context.nameCounter = {};
        }
        
        const name = node.name;
        context.nameCounter[name] = (context.nameCounter[name] || 0) + 1;
        
        if (context.nameCounter[name] > 1) {
          return {
            hasIssue: true,
            issue: `命名重复，已存在${context.nameCounter[name]}个同名元素`,
            suggestion: `建议添加唯一标识: ${name}_${context.nameCounter[name]}`,
            transform: {
              action: 'uniquify',
              oldName: name,
              newName: `${name}_${context.nameCounter[name]}`
            }
          };
        }
        return { hasIssue: false };
      }
    }
  },

  // ==================== 内容逻辑规则 ====================
  content: {
    // 图标与内容区分
    icon_vs_content: {
      name: 'icon_vs_content',
      description: '图标与内容区分',
      category: 'content',
      severity: 'high',
      enabled: true,
      thresholds: {
        smallIcon: 32,
        largeIcon: 80,
        contentSize: 120
      },
      check: (node, depth, config) => {
        if (node.type === 'INSTANCE' || node.type === 'COMPONENT') {
          const dimensions = node.layout?.dimensions;
          if (dimensions) {
            const { width, height } = dimensions;
            const isSquare = Math.abs(width - height) < 5;
            const size = Math.max(width, height);
            
            // 小图标检测
            if (size <= config.thresholds.smallIcon && isSquare) {
              return {
                hasIssue: false,
                suggestion: '检测为小图标，建议使用SvgIcon组件',
                transform: {
                  action: 'componentize',
                  componentType: 'SvgIcon',
                  size: 'small'
                }
              };
            }
            
            // 大图标误用检测
            if (size >= config.thresholds.largeIcon && isSquare) {
              return {
                hasIssue: true,
                issue: '大尺寸方形元素可能被误用作图标，实际应为内容展示',
                suggestion: '考虑拆分为背景内容 + 角标图标的组合',
                transform: {
                  action: 'split',
                  splitInto: ['background_content', 'badge_icon'],
                  originalSize: { width, height }
                }
              };
            }
            
            // 内容区域检测
            if (size >= config.thresholds.contentSize) {
              return {
                hasIssue: false,
                suggestion: '检测为内容区域，建议使用适当的内容展示方式',
                transform: {
                  action: 'contentize',
                  contentType: 'preview',
                  size: 'large'
                }
              };
            }
          }
        }
        return { hasIssue: false };
      }
    },

    // 隐藏元素处理
    hidden_elements: {
      name: 'hidden_elements',
      description: '隐藏元素处理',
      category: 'content',
      severity: 'medium',
      enabled: true,
      check: (node) => {
        const isHidden = node.opacity === 0 || node.visible === false;
        
        if (isHidden) {
          // 分析隐藏原因
          const hiddenReason = analyzeHiddenReason(node);
          
          return {
            hasIssue: true,
            issue: `元素被隐藏 (${hiddenReason.reason})，需要判断处理方式`,
            suggestion: hiddenReason.suggestion,
            transform: {
              action: hiddenReason.action,
              reason: hiddenReason.reason,
              conditional: hiddenReason.conditional
            }
          };
        }
        return { hasIssue: false };
      }
    },

    // 文本内容分析
    text_content_analysis: {
      name: 'text_content_analysis',
      description: '文本内容分析',
      category: 'content',
      severity: 'low',
      enabled: true,
      check: (node) => {
        if (node.type === 'TEXT' && node.text) {
          const analysis = analyzeTextContent(node.text);
          
          if (analysis.type !== 'generic') {
            return {
              hasIssue: false,
              suggestion: `检测为${analysis.type}文本，建议使用对应的语义化标签`,
              transform: {
                action: 'semanticize',
                textType: analysis.type,
                suggestedTag: analysis.suggestedTag,
                suggestedClass: analysis.suggestedClass
              }
            };
          }
        }
        return { hasIssue: false };
      }
    }
  },

  // ==================== 样式优化规则 ====================
  style: {
    // 颜色值规范化
    color_normalization: {
      name: 'color_normalization',
      description: '颜色值规范化',
      category: 'style',
      severity: 'low',
      enabled: true,
      check: (node) => {
        const colors = extractColors(node);
        const issues = [];
        
        colors.forEach(color => {
          if (color.alpha < 0.01) {
            issues.push({
              hasIssue: true,
              issue: '颜色透明度过低，可能是无意设置',
              suggestion: '检查是否应该完全透明或调整透明度',
              transform: {
                action: 'adjustOpacity',
                color: color.hex,
                currentAlpha: color.alpha
              }
            });
          }
        });
        
        return issues.length > 0 ? issues[0] : { hasIssue: false };
      }
    },

    // 尺寸规范化
    size_normalization: {
      name: 'size_normalization',
      description: '尺寸规范化',
      category: 'style',
      severity: 'low',
      enabled: true,
      check: (node) => {
        const dimensions = node.layout?.dimensions;
        if (dimensions) {
          const { width, height } = dimensions;
          
          // 检测奇怪的小数尺寸
          const hasWeirdDecimals = (
            (width % 1 !== 0 && width % 0.5 !== 0) ||
            (height % 1 !== 0 && height % 0.5 !== 0)
          );
          
          if (hasWeirdDecimals) {
            return {
              hasIssue: false,
              suggestion: '尺寸包含复杂小数，考虑规范化为整数或0.5倍数',
              transform: {
                action: 'normalizeSize',
                originalSize: { width, height },
                normalizedSize: {
                  width: Math.round(width * 2) / 2,
                  height: Math.round(height * 2) / 2
                }
              }
            };
          }
        }
        return { hasIssue: false };
      }
    }
  },

  // ==================== 交互优化规则 ====================
  interaction: {
    // 交互状态推断
    interaction_state_inference: {
      name: 'interaction_state_inference',
      description: '交互状态推断',
      category: 'interaction',
      severity: 'low',
      enabled: true,
      check: (node) => {
        const interactionHints = analyzeInteractionHints(node);
        
        if (interactionHints.length > 0) {
          return {
            hasIssue: false,
            suggestion: `检测到可能的交互状态: ${interactionHints.join(', ')}`,
            transform: {
              action: 'addInteraction',
              interactions: interactionHints,
              suggestedEvents: interactionHints.map(hint => `on${hint}`)
            }
          };
        }
        return { hasIssue: false };
      }
    },

    // 可访问性检查
    accessibility_check: {
      name: 'accessibility_check',
      description: '可访问性检查',
      category: 'interaction',
      severity: 'medium',
      enabled: true,
      check: (node) => {
        const a11yIssues = checkAccessibility(node);
        
        if (a11yIssues.length > 0) {
          return {
            hasIssue: true,
            issue: `可访问性问题: ${a11yIssues.join(', ')}`,
            suggestion: '添加必要的可访问性属性',
            transform: {
              action: 'improveA11y',
              issues: a11yIssues,
              suggestions: a11yIssues.map(issue => getA11ySuggestion(issue))
            }
          };
        }
        return { hasIssue: false };
      }
    }
  }
};

// ==================== 辅助函数 ====================

function suggestSemanticName(node) {
  // 基于节点特征推荐语义化名称
  const nameMap = {
    'Vector': 'icon',
    'Rectangle': 'background',
    'Frame': 'container',
    'Group': 'group',
    'Ellipse': 'circle',
    'Line': 'divider'
  };
  
  for (const [tech, semantic] of Object.entries(nameMap)) {
    if (node.name.includes(tech)) {
      return semantic;
    }
  }
  
  // 基于上下文推荐
  if (node.children?.some(child => child.type === 'TEXT')) return 'text_container';
  if (node.children?.some(child => child.type === 'IMAGE-SVG')) return 'icon_container';
  if (node.type === 'TEXT') return analyzeTextContent(node.text || '').suggestedClass;
  
  return 'element';
}

function analyzeHiddenReason(node) {
  // 分析元素被隐藏的原因
  if (node.name.includes('同步') || node.name.includes('sync')) {
    return {
      reason: '状态指示器',
      suggestion: '建议使用条件渲染 v-show="isSync"',
      action: 'conditionalRender',
      conditional: 'isSync'
    };
  }
  
  if (node.name.includes('hover') || node.name.includes('悬浮')) {
    return {
      reason: '悬浮状态',
      suggestion: '建议使用CSS :hover伪类',
      action: 'cssHover',
      conditional: ':hover'
    };
  }
  
  return {
    reason: '未知',
    suggestion: '考虑移除或在特定条件下显示',
    action: 'review',
    conditional: null
  };
}

function analyzeTextContent(text) {
  // 分析文本内容类型
  const patterns = {
    title: /^.{1,20}$|标题|title/i,
    timestamp: /\d{4}[-\/]\d{2}[-\/]\d{2}|\d{2}:\d{2}/,
    count: /^\d+$|页|个|条/,
    tag: /标签|tag|类型/i,
    button: /确定|取消|提交|保存|删除|编辑/,
    placeholder: /请输入|输入|搜索/
  };
  
  for (const [type, pattern] of Object.entries(patterns)) {
    if (pattern.test(text)) {
      return {
        type,
        suggestedTag: getSuggestedTag(type),
        suggestedClass: `text-${type}`
      };
    }
  }
  
  return {
    type: 'generic',
    suggestedTag: 'span',
    suggestedClass: 'text'
  };
}

function getSuggestedTag(textType) {
  const tagMap = {
    title: 'h3',
    timestamp: 'time',
    count: 'span',
    tag: 'span',
    button: 'button',
    placeholder: 'input'
  };
  
  return tagMap[textType] || 'span';
}

function extractColors(node) {
  // 提取节点中的所有颜色
  const colors = [];
  
  if (node.fills) {
    node.fills.forEach(fill => {
      if (fill.color) {
        colors.push({
          hex: fill.color,
          alpha: fill.opacity || 1,
          type: 'fill'
        });
      }
    });
  }
  
  if (node.strokes) {
    node.strokes.forEach(stroke => {
      if (stroke.color) {
        colors.push({
          hex: stroke.color,
          alpha: stroke.opacity || 1,
          type: 'stroke'
        });
      }
    });
  }
  
  return colors;
}

function analyzeInteractionHints(node) {
  // 分析可能的交互提示
  const hints = [];
  
  if (node.name.includes('button') || node.name.includes('按钮')) {
    hints.push('Click');
  }
  
  if (node.name.includes('input') || node.name.includes('输入')) {
    hints.push('Input', 'Focus', 'Blur');
  }
  
  if (node.name.includes('hover') || node.name.includes('悬浮')) {
    hints.push('Hover');
  }
  
  // 基于样式推断
  if (node.strokes?.length > 0 && node.fills?.length > 0) {
    hints.push('Focus');
  }
  
  return [...new Set(hints)];
}

function checkAccessibility(node) {
  // 检查可访问性问题
  const issues = [];
  
  if (node.type === 'TEXT' && !node.text) {
    issues.push('empty_text');
  }
  
  if ((node.type === 'INSTANCE' || node.type === 'COMPONENT') && 
      node.name.includes('icon') && !node.alt) {
    issues.push('missing_alt');
  }
  
  if (node.type === 'FRAME' && node.children?.length > 0 && !node.name) {
    issues.push('unlabeled_container');
  }
  
  return issues;
}

function getA11ySuggestion(issue) {
  const suggestions = {
    'empty_text': '添加有意义的文本内容',
    'missing_alt': '添加alt属性描述图标用途',
    'unlabeled_container': '添加aria-label或role属性'
  };
  
  return suggestions[issue] || '改善可访问性';
}

export default FILTER_RULES_CONFIG; 