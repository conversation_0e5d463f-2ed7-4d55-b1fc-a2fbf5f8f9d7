#!/usr/bin/env node

/**
 * Figma设计稿过滤分析命令行工具
 * 用于测试和分析不规范的设计文件
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { DesignFilterAnalyzer } from './design-filter-analyzer.mjs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 命令行参数解析
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {
    input: null,
    output: null,
    rules: null,
    format: 'console',
    categories: [],
    severity: [],
    help: false
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    switch (arg) {
      case '-i':
      case '--input':
        options.input = args[++i];
        break;
      case '-o':
      case '--output':
        options.output = args[++i];
        break;
      case '-r':
      case '--rules':
        options.rules = args[++i];
        break;
      case '-f':
      case '--format':
        options.format = args[++i];
        break;
      case '-c':
      case '--categories':
        options.categories = args[++i].split(',');
        break;
      case '-s':
      case '--severity':
        options.severity = args[++i].split(',');
        break;
      case '-h':
      case '--help':
        options.help = true;
        break;
      default:
        if (!options.input && !arg.startsWith('-')) {
          options.input = arg;
        }
    }
  }

  return options;
}

// 显示帮助信息
function showHelp() {
  console.log(`
🔍 Figma设计稿过滤分析工具

用法:
  node design-filter-cli.mjs [选项] <输入文件>

选项:
  -i, --input <file>      输入的Figma数据文件 (JSON格式)
  -o, --output <file>     输出分析结果到文件
  -r, --rules <file>      自定义规则配置文件
  -f, --format <type>     输出格式: console, json, html, markdown
  -c, --categories <list> 只分析指定类别: structure,naming,content,style,interaction
  -s, --severity <list>   只显示指定严重程度: low,medium,high,critical
  -h, --help             显示此帮助信息

示例:
  # 分析单个文件
  node design-filter-cli.mjs design.json

  # 分析并输出详细报告
  node design-filter-cli.mjs -i design.json -o report.html -f html

  # 只分析结构和命名问题
  node design-filter-cli.mjs design.json -c structure,naming

  # 只显示高严重程度问题
  node design-filter-cli.mjs design.json -s high,critical

支持的文件格式:
  输入: JSON (Figma API数据)
  输出: console, json, html, markdown
`);
}

// 加载Figma数据
async function loadFigmaData(inputPath) {
  try {
    if (!fs.existsSync(inputPath)) {
      throw new Error(`文件不存在: ${inputPath}`);
    }
    
    const content = fs.readFileSync(inputPath, 'utf8');
    const data = JSON.parse(content);
    
    // 验证数据格式
    if (!data.nodes && !data.document) {
      throw new Error('无效的Figma数据格式，缺少nodes或document字段');
    }
    
    return data;
  } catch (error) {
    console.error(`❌ 加载文件失败: ${error.message}`);
    process.exit(1);
  }
}

// 加载自定义规则
function loadCustomRules(rulesPath) {
  if (!rulesPath) return {};
  
  try {
    if (!fs.existsSync(rulesPath)) {
      console.warn(`⚠️  规则文件不存在: ${rulesPath}，使用默认规则`);
      return {};
    }
    
    const content = fs.readFileSync(rulesPath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`❌ 加载规则文件失败: ${error.message}`);
    return {};
  }
}

// 过滤分析结果
function filterResults(analysis, options) {
  const { categories, severity } = options;
  
  if (categories.length > 0) {
    analysis.issues = analysis.issues.filter(issue => 
      categories.includes(issue.category)
    );
    analysis.suggestions = analysis.suggestions.filter(suggestion => 
      categories.includes(suggestion.category)
    );
    analysis.transforms = analysis.transforms.filter(transform => 
      categories.includes(transform.category)
    );
  }
  
  if (severity.length > 0) {
    analysis.issues = analysis.issues.filter(issue => 
      severity.includes(issue.severity)
    );
  }
  
  return analysis;
}

// 控制台输出格式
function outputConsole(analysis, options) {
  const { summary, issues, suggestions, transforms, metadata } = analysis;
  
  console.log('\n🔍 Figma设计稿分析报告');
  console.log('=' .repeat(60));
  
  // 基本信息
  console.log(`\n📄 文件信息:`);
  console.log(`   文件: ${metadata.figmaFile}`);
  console.log(`   分析时间: ${new Date(metadata.analyzedAt).toLocaleString()}`);
  console.log(`   节点总数: ${metadata.totalNodes}`);
  console.log(`   应用规则: ${metadata.rulesApplied || 0} 个`);
  
  // 摘要信息
  console.log(`\n📊 问题统计:`);
  console.log(`   问题总数: ${summary.totalIssues}`);
  console.log(`   建议总数: ${summary.totalSuggestions}`);
  console.log(`   转换操作: ${summary.totalTransforms}`);
  console.log(`   风险等级: ${getRiskEmoji(summary.riskLevel)} ${summary.riskLevel.toUpperCase()}`);
  
  // 严重程度分布
  if (Object.keys(summary.severityBreakdown).length > 0) {
    console.log(`\n⚠️  严重程度分布:`);
    Object.entries(summary.severityBreakdown).forEach(([severity, count]) => {
      console.log(`   ${getSeverityEmoji(severity)} ${severity}: ${count}个`);
    });
  }
  
  // 类别分布
  if (Object.keys(summary.categoryBreakdown).length > 0) {
    console.log(`\n📦 问题类别分布:`);
    Object.entries(summary.categoryBreakdown).forEach(([category, count]) => {
      console.log(`   ${getCategoryEmoji(category)} ${category}: ${count}个`);
    });
  }
  
  // 主要问题
  if (summary.mainProblems.length > 0) {
    console.log(`\n🚨 主要问题 (前5个):`);
    summary.mainProblems.forEach((problem, index) => {
      console.log(`   ${index + 1}. [${problem.maxSeverity.toUpperCase()}] ${problem.description}`);
      console.log(`      类别: ${problem.category} | 出现次数: ${problem.count}`);
    });
  }
  
  // 详细问题列表
  if (issues.length > 0 && issues.length <= 10) {
    console.log(`\n❌ 发现的问题:`);
    issues.forEach((issue, index) => {
      console.log(`   ${index + 1}. [${issue.severity.toUpperCase()}] ${issue.nodeName} (${issue.nodeType})`);
      console.log(`      问题: ${issue.issue}`);
      console.log(`      类别: ${issue.category} | 规则: ${issue.rule}`);
      console.log('');
    });
  } else if (issues.length > 10) {
    console.log(`\n❌ 发现 ${issues.length} 个问题 (仅显示前5个):`);
    issues.slice(0, 5).forEach((issue, index) => {
      console.log(`   ${index + 1}. [${issue.severity.toUpperCase()}] ${issue.nodeName}`);
      console.log(`      ${issue.issue}`);
    });
    console.log(`   ... 还有 ${issues.length - 5} 个问题 (使用 -f json 查看完整列表)`);
  }
  
  // 推荐操作
  if (summary.recommendedActions.length > 0) {
    console.log(`\n💡 推荐操作:`);
    summary.recommendedActions.forEach((action, index) => {
      console.log(`   ${index + 1}. ${action.nodeName}: ${action.suggestion}`);
    });
  }
  
  // 转换操作摘要
  if (summary.transformActions.length > 0) {
    console.log(`\n🔄 可自动转换的操作:`);
    summary.transformActions.forEach(action => {
      console.log(`   ${getTransformEmoji(action.action)} ${action.action}: ${action.count}个`);
    });
  }
  
  console.log(`\n✅ 分析完成！`);
  
  if (summary.riskLevel === 'critical' || summary.riskLevel === 'high') {
    console.log(`\n⚠️  建议: 发现${summary.riskLevel === 'critical' ? '严重' : '较多'}问题，建议优化设计稿结构后再进行还原。`);
  } else {
    console.log(`\n👍 建议: 问题较少，可以进行还原，注意处理发现的问题。`);
  }
}

// JSON输出格式
function outputJSON(analysis, outputPath) {
  const jsonContent = JSON.stringify(analysis, null, 2);
  
  if (outputPath) {
    fs.writeFileSync(outputPath, jsonContent);
    console.log(`📄 分析结果已保存到: ${outputPath}`);
  } else {
    console.log(jsonContent);
  }
}

// HTML输出格式
function outputHTML(analysis, outputPath) {
  const htmlContent = generateHTMLReport(analysis);
  
  if (outputPath) {
    fs.writeFileSync(outputPath, htmlContent);
    console.log(`📄 HTML报告已保存到: ${outputPath}`);
  } else {
    console.log(htmlContent);
  }
}

// 生成HTML报告
function generateHTMLReport(analysis) {
  const { summary, issues, suggestions, transforms, metadata } = analysis;
  
  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Figma设计稿分析报告</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif; margin: 40px; line-height: 1.6; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .risk-critical { border-left: 4px solid #dc3545; }
        .risk-high { border-left: 4px solid #fd7e14; }
        .risk-medium { border-left: 4px solid #ffc107; }
        .risk-low { border-left: 4px solid #28a745; }
        .issue { margin-bottom: 15px; padding: 15px; border-radius: 6px; background: #f8f9fa; }
        .severity-high { border-left: 4px solid #dc3545; }
        .severity-medium { border-left: 4px solid #ffc107; }
        .severity-low { border-left: 4px solid #17a2b8; }
        .node-name { font-weight: bold; color: #495057; }
        .category { display: inline-block; background: #e9ecef; padding: 2px 8px; border-radius: 12px; font-size: 12px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Figma设计稿分析报告</h1>
        <p><strong>文件:</strong> ${metadata.figmaFile}</p>
        <p><strong>分析时间:</strong> ${new Date(metadata.analyzedAt).toLocaleString()}</p>
        <p><strong>节点总数:</strong> ${metadata.totalNodes}</p>
    </div>

    <div class="summary">
        <div class="card risk-${summary.riskLevel}">
            <h3>📊 总体情况</h3>
            <p><strong>风险等级:</strong> ${summary.riskLevel.toUpperCase()}</p>
            <p><strong>问题数:</strong> ${summary.totalIssues}</p>
            <p><strong>建议数:</strong> ${summary.totalSuggestions}</p>
        </div>
        
        <div class="card">
            <h3>⚠️ 严重程度分布</h3>
            ${Object.entries(summary.severityBreakdown).map(([severity, count]) => 
              `<p><strong>${severity}:</strong> ${count}个</p>`
            ).join('')}
        </div>
        
        <div class="card">
            <h3>📦 类别分布</h3>
            ${Object.entries(summary.categoryBreakdown).map(([category, count]) => 
              `<p><strong>${category}:</strong> ${count}个</p>`
            ).join('')}
        </div>
    </div>

    <div class="card">
        <h2>🚨 主要问题</h2>
        ${summary.mainProblems.map(problem => 
          `<div class="issue severity-${problem.maxSeverity}">
             <div class="node-name">${problem.description}</div>
             <span class="category">${problem.category}</span>
             <span class="category">出现 ${problem.count} 次</span>
           </div>`
        ).join('')}
    </div>

    <div class="card">
        <h2>❌ 详细问题列表</h2>
        ${issues.map(issue => 
          `<div class="issue severity-${issue.severity}">
             <div class="node-name">${issue.nodeName} (${issue.nodeType})</div>
             <p>${issue.issue}</p>
             <span class="category">${issue.category}</span>
             <span class="category">${issue.rule}</span>
             <span class="category">${issue.severity}</span>
           </div>`
        ).join('')}
    </div>

    <div class="card">
        <h2>💡 优化建议</h2>
        ${suggestions.map(suggestion => 
          `<div class="issue">
             <div class="node-name">${suggestion.nodeName}</div>
             <p>${suggestion.suggestion}</p>
             <span class="category">${suggestion.category}</span>
           </div>`
        ).join('')}
    </div>
</body>
</html>`;
}

// 辅助函数：获取风险等级emoji
function getRiskEmoji(risk) {
  const emojis = {
    critical: '🔥',
    high: '⚠️',
    medium: '⚡',
    low: '✅'
  };
  return emojis[risk] || '❓';
}

// 辅助函数：获取严重程度emoji
function getSeverityEmoji(severity) {
  const emojis = {
    critical: '🔥',
    high: '❌',
    medium: '⚠️',
    low: '💡'
  };
  return emojis[severity] || '❓';
}

// 辅助函数：获取类别emoji
function getCategoryEmoji(category) {
  const emojis = {
    structure: '🏗️',
    naming: '🏷️',
    content: '📝',
    style: '🎨',
    interaction: '👆'
  };
  return emojis[category] || '📦';
}

// 辅助函数：获取转换操作emoji
function getTransformEmoji(action) {
  const emojis = {
    flatten: '📦',
    rename: '🏷️',
    split: '✂️',
    componentize: '🧩',
    conditionalRender: '🔄',
    remove: '🗑️'
  };
  return emojis[action] || '🔧';
}

// 主函数
async function main() {
  const options = parseArgs();
  
  if (options.help) {
    showHelp();
    return;
  }
  
  if (!options.input) {
    console.error('❌ 错误: 请指定输入文件');
    console.log('使用 --help 查看使用说明');
    process.exit(1);
  }
  
  console.log('🚀 开始分析设计稿...');
  
  // 加载数据和规则
  const figmaData = await loadFigmaData(options.input);
  const customRules = loadCustomRules(options.rules);
  
  // 创建分析器并分析
  const analyzer = new DesignFilterAnalyzer(customRules);
  const analysis = analyzer.analyzeDesign(figmaData, { 
    filename: path.basename(options.input) 
  });
  
  // 过滤结果
  const filteredAnalysis = filterResults(analysis, options);
  
  // 输出结果
  switch (options.format) {
    case 'json':
      outputJSON(filteredAnalysis, options.output);
      break;
    case 'html':
      outputHTML(filteredAnalysis, options.output);
      break;
    case 'markdown':
      // TODO: 实现markdown格式
      console.log('Markdown格式暂未实现，使用console格式');
      outputConsole(filteredAnalysis, options);
      break;
    default:
      outputConsole(filteredAnalysis, options);
  }
}

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 运行主函数
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('❌ 执行失败:', error.message);
    process.exit(1);
  });
}

export { main }; 