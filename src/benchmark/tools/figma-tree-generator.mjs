#!/usr/bin/env node

/**
 * Figma 节点树形结构生成器
 * 将 Figma JSON 数据转换为包含位置信息的结构化树形数据
 */

import fs from 'fs';
import path from 'path';

/**
 * 解析 Figma 节点的位置和尺寸信息
 */
function parseLayoutInfo(layout, globalVars) {
  if (!layout) return null;
  
  const layoutData = globalVars?.styles?.[layout];
  if (!layoutData) return null;

  return {
    mode: layoutData.mode || 'none',
    dimensions: layoutData.dimensions || null,
    position: layoutData.locationRelativeToParent || null,
    sizing: layoutData.sizing || null,
    padding: layoutData.padding || null,
    gap: layoutData.gap || null,
    alignItems: layoutData.alignItems || null,
    justifyContent: layoutData.justifyContent || null,
    alignSelf: layoutData.alignSelf || null
  };
}

/**
 * 解析填充信息
 */
function parseFillInfo(fills, globalVars) {
  if (!fills) return null;
  
  const fillData = globalVars?.styles?.[fills];
  if (!fillData) return null;

  return fillData.map(fill => {
    if (typeof fill === 'string') {
      return { type: 'color', value: fill };
    } else if (fill.type === 'IMAGE') {
      return { 
        type: 'image', 
        imageRef: fill.imageRef,
        scaleMode: fill.scaleMode 
      };
    } else if (fill.type === 'GRADIENT_LINEAR' || fill.type === 'GRADIENT_RADIAL') {
      return { 
        type: 'gradient', 
        gradientType: fill.type,
        gradientStops: fill.gradientStops 
      };
    }
    return fill;
  });
}

/**
 * 解析描边信息
 */
function parseStrokeInfo(strokes, globalVars) {
  if (!strokes) return null;
  
  const strokeData = globalVars?.styles?.[strokes];
  if (!strokeData) return null;

  return {
    colors: strokeData.colors || [],
    strokeWeight: strokeData.strokeWeight || 0
  };
}

/**
 * 解析文本样式信息
 */
function parseTextStyle(textStyle, globalVars) {
  if (!textStyle) return null;
  
  const textStyleData = globalVars?.styles?.[textStyle];
  if (!textStyleData) return null;

  return {
    fontFamily: textStyleData.fontFamily,
    fontWeight: textStyleData.fontWeight,
    fontSize: textStyleData.fontSize,
    lineHeight: textStyleData.lineHeight,
    textAlign: textStyleData.textAlignHorizontal,
    textAlignVertical: textStyleData.textAlignVertical
  };
}

/**
 * 递归构建节点树
 */
function buildNodeTree(node, globalVars, depth = 0) {
  const nodeInfo = {
    id: node.id,
    name: node.name,
    type: node.type,
    depth: depth,
    
    // 布局信息
    layout: parseLayoutInfo(node.layout, globalVars),
    
    // 视觉样式
    fills: parseFillInfo(node.fills, globalVars),
    strokes: parseStrokeInfo(node.strokes, globalVars),
    borderRadius: node.borderRadius || null,
    opacity: node.opacity || null,
    
    // 文本信息
    text: node.text || null,
    textStyle: parseTextStyle(node.textStyle, globalVars),
    
    // 组件信息
    componentId: node.componentId || null,
    componentProperties: node.componentProperties || null,
    
    // 子节点
    children: []
  };

  // 递归处理子节点
  if (node.children && node.children.length > 0) {
    nodeInfo.children = node.children.map(child => 
      buildNodeTree(child, globalVars, depth + 1)
    );
  }

  return nodeInfo;
}

/**
 * 生成节点统计信息
 */
function generateStatistics(nodeTree) {
  const stats = {
    totalNodes: 0,
    nodeTypes: {},
    maxDepth: 0,
    componentNodes: 0,
    textNodes: 0,
    imageNodes: 0
  };

  function traverse(node) {
    stats.totalNodes++;
    stats.maxDepth = Math.max(stats.maxDepth, node.depth);
    
    // 统计节点类型
    stats.nodeTypes[node.type] = (stats.nodeTypes[node.type] || 0) + 1;
    
    // 统计特殊节点
    if (node.componentId) stats.componentNodes++;
    if (node.text) stats.textNodes++;
    if (node.type === 'RECTANGLE' && node.fills?.some(f => f.type === 'image')) {
      stats.imageNodes++;
    }

    node.children.forEach(traverse);
  }

  traverse(nodeTree);
  return stats;
}

/**
 * 生成可读的位置摘要
 */
function generatePositionSummary(nodeTree) {
  const positions = [];

  function traverse(node, parentPath = '') {
    const path = parentPath ? `${parentPath} > ${node.name}` : node.name;
    
    if (node.layout?.position) {
      positions.push({
        path: path,
        type: node.type,
        position: node.layout.position,
        dimensions: node.layout.dimensions
      });
    }

    node.children.forEach(child => traverse(child, path));
  }

  traverse(nodeTree);
  return positions;
}

/**
 * 主函数
 */
function generateFigmaTree(figmaData, outputPath = null) {
  if (!figmaData.nodes || figmaData.nodes.length === 0) {
    throw new Error('Invalid Figma data: no nodes found');
  }

  // 构建树形结构
  const rootNode = figmaData.nodes[0]; // 通常第一个节点是根节点
  const nodeTree = buildNodeTree(rootNode, figmaData.globalVars);

  // 生成统计信息
  const statistics = generateStatistics(nodeTree);
  
  // 生成位置摘要
  const positionSummary = generatePositionSummary(nodeTree);

  const result = {
    metadata: {
      rootNodeId: rootNode.id,
      rootNodeName: rootNode.name,
      generatedAt: new Date().toISOString(),
      statistics: statistics
    },
    nodeTree: nodeTree,
    positionSummary: positionSummary
  };

  // 如果指定了输出路径，保存到文件
  if (outputPath) {
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    fs.writeFileSync(outputPath, JSON.stringify(result, null, 2), 'utf-8');
    console.log(`✅ 树形结构已保存到: ${outputPath}`);
  }

  return result;
}

/**
 * 命令行使用
 */
if (process.argv[1] === new URL(import.meta.url).pathname) {
  const args = process.argv.slice(2);
  
  if (args.length < 1) {
    console.log(`
用法: node figma-tree-generator.mjs <figma-data-file> [output-file]

参数:
  figma-data-file    Figma JSON 数据文件路径
  output-file        输出文件路径 (可选)

示例:
  node figma-tree-generator.mjs figma-data.json
  node figma-tree-generator.mjs figma-data.json tree-output.json
    `);
    process.exit(1);
  }

  try {
    const inputFile = args[0];
    const outputFile = args[1];

    if (!fs.existsSync(inputFile)) {
      throw new Error(`输入文件不存在: ${inputFile}`);
    }

    const figmaData = JSON.parse(fs.readFileSync(inputFile, 'utf-8'));
    const result = generateFigmaTree(figmaData, outputFile);

    console.log('\n📊 节点统计信息:');
    console.log(`总节点数: ${result.metadata.statistics.totalNodes}`);
    console.log(`最大深度: ${result.metadata.statistics.maxDepth}`);
    console.log(`组件节点: ${result.metadata.statistics.componentNodes}`);
    console.log(`文本节点: ${result.metadata.statistics.textNodes}`);
    console.log(`图片节点: ${result.metadata.statistics.imageNodes}`);
    
    console.log('\n📍 位置信息摘要:');
    result.positionSummary.slice(0, 10).forEach(pos => {
      console.log(`${pos.path}: (${pos.position.x}, ${pos.position.y})`);
    });
    
    if (result.positionSummary.length > 10) {
      console.log(`... 还有 ${result.positionSummary.length - 10} 个节点`);
    }

  } catch (error) {
    console.error('❌ 错误:', error.message);
    process.exit(1);
  }
}

export { generateFigmaTree, buildNodeTree, parseLayoutInfo }; 