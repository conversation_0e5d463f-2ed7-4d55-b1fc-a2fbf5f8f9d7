<template>
  <div class="image-canvas-container" style="display: flex; justify-self: center; align-items: center">
    <!-- 让 totalRotateDegree 的改变和 scaleRatio 同步，使得切换图片不要闪动... -->
    <svg 
      id="imageSvg"
      ref="svg"
      :style="{ transform: `scale(${scaleRatio}) rotate(${showBorder ? totalRotateDegree : 0}deg)`, transition: `${enableTransition ? 'all 0.2s' : 'none'}`}"
      :width="SVGWrapper.width +  svgPadding * 2"
      :height="SVGWrapper.height + svgPadding * 2"
      @transitionend="enableTransition = false"
      @pointermove="onMouseMove"
      @pointerup="onMouseUp"
      @pointerleave="onMouseUp"
    >
      <!-- 图片 -->
      <image 
        :transform="svgTranslate"
        :href="showBorder ? imageUrl : enhancedUrl"
        x="0"
        y="0"
        :width="SVGWrapper.width"
        :height="SVGWrapper.height"
      />
      <!-- 绘制多边形 -->
      <polygon 
        :transform="svgTranslate"
        v-show="showBorder"
        :points="rectPoints.map(point => `${point.x},${point.y}`).join(' ')"
        fill="transparent"
        stroke="#19BCAA"
        :stroke-width="lineWidth"
      />
      <!-- 拖动的把柄 -->
      <g 
        :transform="svgTranslate"
        v-show="showBorder && showTools"
        v-for="(point, index) in rectPoints"
        :key="index"
      >
        <rect 
          :style="index === 0 || index === 2 ? { cursor: 'ns-resize' } : { cursor: 'ew-resize' }"
          :x="rectMiddlePoints[index].x"
          :y="rectMiddlePoints[index].y"
          :transform="rectMiddlePoints[index].transform"
          :width="rectMiddlePoints[index].width"
          :stroke-width="lineWidth"
          :height="rectMiddlePoints[index].height"
          :rx="cornerRadius"
          :ry="cornerRadius"
          fill="#ffffff"
          stroke="#19BCAA"
          @pointerdown="onMouseDown(index, 'line')"
        />
        <circle 
          :cx="point.x"
          :cy="point.y"
          :r="centerRadius"
          fill="#ffffff"
          stroke="#19BCAA"
          :stroke-width="lineWidth"
          @pointerdown="onMouseDown(index, 'point')"
        />
      </g>
    </svg>
  </div>
</template>

<script lang="ts">
import { defineComponent, type PropType } from 'vue'
import type { imageItem } from '../shootDialog/component'

interface Point {
  x: number
  y: number
}
interface ImageData {
  origWidth: number
  origHeight: number
  modWidth: number
  modHeight: number
}

export default defineComponent({
  props: {
    selectedImage: {
      type: Object as PropType<imageItem>,
      required: true
    },
    showBorder: {
      type: Boolean,
      default: () => false
    },
    imageUrl: {
      type: String,
      default: () => ''
    },
    enhancedUrl: {
      type: String,
      default: () => ''
    },
    // 外部传进来的值为 interaction_position
    dataStr: {
      default: () => '',
      type: String
    }
  },
  watch: {
    enhancedUrl() {
      console.log('enhancedUrl改变导致watch触发')
      this.init()
    },
    showBorder() {
      console.log('showBorder改变导致watch触发')
      this.init()
    },
    // 用于框选全图、自动切边、切换图片。重新解析 interaction_position
    dataStr() {
      console.log('dataStr改变导致watch触发')
      this.parseImageData()
      this.init()
    },

    //深度监听 selectedImage
    selectedImage: {
      deep: true,
      handler(newselectedImage: imageItem, oldselectedImage: imageItem) {
        console.log('selectedImage改变导致watch触发')

        //旋转按钮点击时， newselectedImage.url 和  oldselectedImage.url 相同，但是 他们的 rotateDegree 不同
        if (newselectedImage.rotateDegree !== oldselectedImage.rotateDegree && newselectedImage.url === oldselectedImage.url) {
          this.enableTransition = true  // 开启图片旋转的过渡动画

          // 判断旋转的方向
          if (newselectedImage.rotateDegree - oldselectedImage.rotateDegree === 90 || newselectedImage.rotateDegree - oldselectedImage.rotateDegree === -270) {
            this.totalRotateDegree += 90
          } else if (newselectedImage.rotateDegree - oldselectedImage.rotateDegree === -90 || newselectedImage.rotateDegree - oldselectedImage.rotateDegree === 270) {
            this.totalRotateDegree -= 90
          } 

          this.init()
        } 
      }
    }
  },
  mounted() {
    this.parseImageData()
    this.init()
  },
  data() {
    return {
      totalRotateDegree: 0, // 旋转的总角度
      enableTransition: false,  // 是否开启 图片渲染的过渡动画
      
      showTools: true,
      // 可视区域
      maxHeight: 552, //原473，图片的最大高度
      // 可视区域
      maxWidth: 864, //原800，图片的最大宽度
      // 图片缩放比例
      scaleRatio: 1,
      // 图片宽高 和 切边后的宽高
      imageData: {} as ImageData,
      // 四个顶点坐标
      rectPoints: [] as Point[],
      // 选中的边
      selectedLineIndex: -1,
      // 选中的点
      selectedVertexIndex: -1,
      SVGWrapper: {
        width: 0,
        height: 0
      }
    }
  },
  computed: {
    // 多边形的中间点的数组，上右下左顺序。用于绘制拖动时中间的把柄
    rectMiddlePoints() {
      return this.rectPoints.map((ele, i) => {
        const { x, y } = ele
        const nextIndex = i + 1 === this.rectPoints.length ? 0 : i + 1
        const nextPoint = this.rectPoints[nextIndex]
        const middlePoint = {
          x: (x + nextPoint.x) / 2,
          y: (y + nextPoint.y) / 2
        }
        const startPoint = {
          x: i % 2 === 0 ? middlePoint.x - 2 * this.rectWidth : middlePoint.x - this.rectHeight / 2,
          y: i % 2 === 0 ? middlePoint.y - this.rectWidth / 2 : middlePoint.y - this.rectWidth / 2
        }
        const dx = nextPoint.x - x
        const dy = nextPoint.y - y
        const angle = Math.atan2(dy, dx) * (180 / Math.PI)
        return {
          ...startPoint,
          transform: `rotate(${angle} ${middlePoint.x} ${middlePoint.y})`,
          width: this.rectHeight,
          height: this.rectWidth
        }
      })
    },
    centerRadius() {
      return 5 / this.scaleRatio
    },
    lineWidth() {
      return 2 / this.scaleRatio
    },
    cornerRadius() {
      return 4 / this.scaleRatio
    },
    rectWidth() {
      return 6 / this.scaleRatio
    },
    rectHeight() {
      return 28 / this.scaleRatio
    },
    // 我需要svg有内部孔隙来保证可以正常显示裁切框
    svgPadding() {
      return 20 / this.scaleRatio
    },
    svgTranslate() {
      return `translate(${this.svgPadding}, ${this.svgPadding})`
    }
  },
  methods: {
    init() {
      this.drawImage()
    },
    /**
     * 解析 interaction_position，获取图片的宽高和切边后的宽高、四个顶点坐标
     */
    parseImageData() {
      const { dataStr } = this
      const values = dataStr.split(',').map(Number)
      this.imageData = {
        origWidth: values[0],
        origHeight: values[1],
        modWidth: values[2],
        modHeight: values[3]
      }

      this.rectPoints = [
        { x: values[4], y: values[5] },
        { x: values[6], y: values[7] },
        { x: values[8], y: values[9] },
        { x: values[10], y: values[11] }
      ]

      this.totalRotateDegree = this.selectedImage.rotateDegree  //重置旋转的总角度
    },

    // 计算宽高、缩放比例
    drawImage() {
      const img = new Image()
      img.src = this.showBorder ? this.imageUrl : this.enhancedUrl

      img.onload = () => {
        const [curWidth, curHeight] = [img.width, img.height]
        this.SVGWrapper = { width: curWidth, height: curHeight }

        // 检查图像是否旋转
        const isRotated = this.selectedImage.rotateDegree === 90 || this.selectedImage.rotateDegree === 270

        if (isRotated) {
          // 如果图片旋转
          if (this.showBorder) {
            this.scaleRatio = this.calculateScaleRatio(curHeight, curWidth)  // 1.图片旋转、显示切边框（切边模式）
          } else {
            this.scaleRatio = this.calculateScaleRatio(curWidth, curHeight)  // 2.图片旋转、不显示切边框（滤镜模式）
          }
        } else {
          // 如果图片没有旋转。因为这里的代码容易绕晕，建议不用优化
          if (this.showBorder) {
            this.scaleRatio = this.calculateScaleRatio(curWidth, curHeight)  // 3.图片不旋转、显示切边框（切边模式）
          } else {
            this.scaleRatio = this.calculateScaleRatio(curWidth, curHeight)  // 4.图片不旋转、不显示切边框（滤镜模式）
          }
        }
      }
    },
    
    //计算缩放比例
    calculateScaleRatio(width: number, height: number) {
      const exceedsMaxWidth = width > this.maxWidth
      const exceedsMaxHeight = height > this.maxHeight

      if (exceedsMaxWidth || exceedsMaxHeight) {
        return Math.min(this.maxHeight / height, this.maxWidth / width)
      }

      return 1 // 如果没有超过最大尺寸，保持原始尺寸（缩放比例为1）
    },
    
    collectDataStr() {
      const { origWidth, origHeight, modWidth, modHeight } = this.imageData
      // 将原始宽高、切边后的宽高、四个顶点坐标拼接成字符串
      return `${origWidth},${origHeight},${modWidth},${modHeight},${this.rectPoints.map(point => `${Math.floor(point.x)},${Math.floor(point.y)}`).join(',')}`
    },
    onMouseDown(index: number, type: string) {
      this.selectedVertexIndex = type === 'point' ? index : -1
      this.selectedLineIndex = type === 'line' ? index : -1
    },

    onMouseUp() {
      this.showTools = true
      this.selectedLineIndex = -1
      this.selectedVertexIndex = -1
      const data = this.collectDataStr()
      this.$emit('end', data)
    },
    // Updated onMouseMove method
    onMouseMove(e: any) {
      if (!this.showBorder || (this.selectedVertexIndex === -1 && this.selectedLineIndex === -1)) return //没有选中边或者点

      const { offsetX, offsetY } = e
      const width = this.imageData.origWidth
      const height = this.imageData.origHeight
      // selectedVertexIndex point index
      if (this.selectedVertexIndex > -1) {
        this.showTools = false
        const x = this.rectPoints[this.selectedVertexIndex].x
        const y = this.rectPoints[this.selectedVertexIndex].y
        // Moving a vertex, adjust adjacent edges
        const dx = offsetX - x
        const dy = offsetY - y
        // 碰撞检测
        // todo
        // 为了顶点可以移动到边缘 取消半径 用移动的中心
        this.rectPoints[this.selectedVertexIndex].x = this.handleCollision(x, dx, width, 0)
        this.rectPoints[this.selectedVertexIndex].y = this.handleCollision(y, dy, height, 0)
      } else if (this.selectedLineIndex > -1) {
        this.showTools = false
        const prevIndex = this.selectedLineIndex % 4
        const nextIndex = (this.selectedLineIndex + 1) % 4
        const middlePoint = {
          x: (this.rectPoints[prevIndex].x + this.rectPoints[nextIndex].x) / 2,
          y: (this.rectPoints[prevIndex].y + this.rectPoints[nextIndex].y) / 2
        }
        const dx = offsetX - middlePoint.x
        const dy = offsetY - middlePoint.y
        // 碰撞检测
        this.rectPoints[prevIndex].x = this.handleCollision(this.rectPoints[prevIndex].x, dx, width, 0)
        this.rectPoints[nextIndex].x = this.handleCollision(this.rectPoints[nextIndex].x, dx, width, 0)
        this.rectPoints[prevIndex].y = this.handleCollision(this.rectPoints[prevIndex].y, dy, height, 0)
        this.rectPoints[nextIndex].y = this.handleCollision(this.rectPoints[nextIndex].y, dy, height, 0)
      }
      // this.init()
    },
    // 碰撞检测
    // x dx width offset
    handleCollision(x: number, dx: number, width: number, offset: number = 0) {
      const originX = x - this.svgPadding
      const origWidth = width
      if (originX + dx + offset >= origWidth) {
        return origWidth - offset
      } else if (originX + dx - offset <= 0) {
        return offset
      } else {
        return originX + dx
      }
    }
  }
})
</script>

<style lang="scss" scoped>
.image-canvas-container {
  circle {
    z-index: 2023;
    cursor: move;
  }
  rect {
    z-index: 2023;
  }
  /* 为hover在circle元素上的样式 */
  circle:hover {
    stroke: #ffffff;
    fill: #19bcaa;
  }

  /* 为hover在rect元素上的样式 */
  rect:hover {
    stroke: #ffffff;
    fill: #19bcaa;
  }
}
</style>
