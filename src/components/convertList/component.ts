import { mapWritableState } from 'pinia'
import { defineComponent } from 'vue'

/* 接口 */
import { deleteShare, modifyShareData } from '@/api/oapi'
import mainMorePopup from '@/components/mainMorePopup/MainMorePopup.vue'
import modal from '@/components/modal/ModalComponent.vue'
import config from '@/config'
import bus from '@/config/bus'
// import napi from '@/services/napi'
import { compareVersion, encryptEid, format, getBrowser, hasElectron } from '@/config/utils'
import { useDirStore } from '@/stores/dir-store'
import { useUserStore } from '@/stores/user-store'

export interface DataListOptions {
  type: string
  selected?: boolean
  src?: string
  sid: string
  title?: string
  modify_time: string
}

const sleep = (time: number) => {
  return new Promise<any>((resolve: any) => {
    setTimeout(() => {
      resolve()
    }, time)
  })
}
const txtUrl = new URL('./images/ic_pc_download_txt.png', import.meta.url).href
const docUrl = new URL('./images/ic_pc_download_doc.png', import.meta.url).href
const xlsUrl = new URL('./images/ic_pc_download_xls.png', import.meta.url).href

// 有没有创建下载,convertDownload 调用一次就可以了
// let beginDownload = false
export default defineComponent({
  components: {
    mainMorePopup,
    modal
  },
  props: {
    showConvertDownload: {
      type: Boolean,
      default: false
    },
    displayModeParent: {
      type: String,
      default: 'list'
    }
  },
  data() {
    return {
      dataList: [] as DataListOptions[],
      isSelected: false,
      isSelectedAll: false,
      mouseShapeDown: false,
      mouseShapeX: 0,
      editTargetId: '',
      mouseShapeY: 0,
      /* 鼠标拖拽事件 */
      beginMouseMoveSelect: false, // 当前目标未选中时，鼠标拖拽为选中
      beginMouseDown: false, // 鼠标点下状态
      beginMouseMoveDrag: false, // 当前目标已选中时，鼠标拖拽为移动文档
      mouseMoveStartDirId: '', // 鼠标拖拽事件，从文件夹开始
      mouseMoveStartDocId: '', // 鼠标拖拽事件，从文档开始
      mouseMoveLastDirId: '', // 鼠标拖拽事件，上一个响应的文件夹id
      mouseMoveLastDocId: '', // 鼠标拖拽事件，上一个响应的文档id
      // mainMorePopup组件
      showMorePopup: false, // 是否展示更多菜单
      morePopupLeft: 0, // 更多菜单左间距
      morePopupTop: 0, // 更多菜单上间距
      showRemoveConfirm: false,
      titleEdit: '',
      showDownBatch: false,
      isPremium: useUserStore().userType === config.userTypePremium || useUserStore().userType === config.userTypePrestige,
      isBusiness: useUserStore().userType === config.userTypeBusiness || (useUserStore().userType !== config.userTypeNormal && useUserStore().userType !== config.userTypePureness)
    }
  },
  directives: {
    focus: {
      mounted: function (el) {
        if (getBrowser() === config.browserSafari) {
          document.querySelector('.title_edit') && (document.querySelector('.title_edit') as HTMLInputElement).select()
          return
        }
        el.focus()
        el.select()
      }
    }
  },
  watch: {
    dataList: {
      immediate: true,
      handler(newValue) {
        newValue.find((item: DataListOptions) => item.selected) ? (this.isSelected = true) : (this.isSelected = false)
      },
      deep: true
    }
  },
  computed: {
    ...mapWritableState(useUserStore, ['token', 'userType']),
    checkSelected() {
      const selectList = this.dataList.filter(item => item.selected)
      // ;(this.$parent as any).selectNum(selectList)
      return selectList
    }
  },
  created() {
    const self = this
    // this.displayModeParent = (this.$parent as any).displayMode
    // console.log('this.$store.state.dir', this.$store.state.dir.dirMap.file_convert_list)
    if (useDirStore().dirMap.file_convert_list && useDirStore().dirMap.file_convert_list.fileList?.length !== 0) {
      const share_info = JSON.parse(JSON.stringify(useDirStore().dirMap.file_convert_list.fileList))
      // console.log('share_info', share_info)
      share_info.map((item: DataListOptions) => {
        let src = ''
        if (item.type === '6') {
          src = txtUrl
        } else if (item.type === '0' || item.type === '2' || item.type === '4') {
          src = docUrl
        } else if (item.type === '1' || item.type === '3') {
          src = xlsUrl
        }
        return Object.assign(item, { selected: false, src: src })
      })
      self.dataList = share_info
      // console.log('res11', self.dataList)
      // oapi.queryFileConvertList(this.token).then(res => {
      // })
    }
  },
  mounted() {
    const self = this
    document.addEventListener('mouseup', self.mouseUp, false)
    document.addEventListener('mousemove', self.mouseMove, false)
    document.addEventListener('click', self.clearAll, false)
    bus.on('downloadConvertList', () => {
      self.startDownload(true)
    })
  },
  beforeUnmount() {
    bus.off('downloadConvertList')
    document.removeEventListener('click', this.clearAll)
    document.removeEventListener('mouseup', this.mouseUp)
    document.removeEventListener('mousemove', this.mouseMove)
  },
  methods: {
    // 重命名
    convertRename() {
      this.editTargetId = this.checkSelected[0].sid || ''
      this.titleEdit = this.checkSelected[0].title || ''
    },
    /**
     * 阻止默认事件
     */
    preventDefault(event: any) {
      event.preventDefault()
      event.returnValue = false
    },
    saveTitle() {
      const self = this
      if (!self.titleEdit.trim()) {
        bus.emit('toast', { msg: self.$t('web_3_not_empty') })
        return false
      }
      if (!self.checkDocName(self.titleEdit)) {
        bus.emit('toast', { msg: self.$t('web_3_folder_repeat') })
        return false
      }
      modifyShareData(this.token, this.editTargetId, this.titleEdit, this.checkSelected[0].type)
        .then(res => {
          if (res.ret === 0) {
            self.dataList.map(item => {
              if (item.sid === self.editTargetId) {
                item.title = self.titleEdit
              }
            })
            bus.emit('toast', {
              msg: self.$t('cs_save_share_ok'),
              type: 'success'
            })
            self.cancelRename()
          } else {
            bus.emit('toast', { msg: self.$t('web_3_renamefailed') })
          }
        })
        .catch(err => {
          bus.emit('toast', { msg: self.$t('web_3_renamefailed') })
          throw err
        })
    },
    cancelRename() {
      this.editTargetId = ''
      this.titleEdit = ''
    },
    async startDownload(hasDownload = false) {
      for (let i = 0; i < this.checkSelected.length; i++) {
        const item = this.checkSelected[i]
        let url = ''
        if (item.type === '6') {
          url = `${config.macro.V3_MAIN_DOMAIN}/word/download`
        } else if (item.type === '0') {
          url = `${config.macro.V3_MAIN_DOMAIN}/word/download`
        } else if (item.type === '1') {
          url = `${config.macro.V3_MAIN_DOMAIN}/excel/download`
        } else if (item.type === '2') {
          url = `${config.api.OAPI}/download_word`
        } else if (item.type === '4') {
          url = `${config.api.OAPI}/download_ppt`
        } else if (item.type === '3') {
          url = `${config.api.OAPI}/download_excel`
        }

        if (hasElectron() && window.desktopVersion && compareVersion(window.desktopVersion, '1.0.2') > 0 && !hasDownload) {
          window.ipcRenderer.send('open-savePath', 'convertList', item.title)
        } else {
          const downItem = `${url}?type_value=${item.type}&title=${item.type === '6' || item.type === '0' ? item.title + '.docx' : item.title}&sid=${item.sid}&token=${this.token}&platform=web&encrypt_id=${encryptEid(useUserStore().userId)}`
          const iframe = document.createElement('iframe') as HTMLIFrameElement
          iframe.style.display = 'none' // 防止影响页面
          iframe.style.height = '0' // 防止影响页面
          iframe.src = downItem
          document.body.appendChild(iframe) // 这一行必须，iframe挂在到dom树上才会发请求
          // 5分钟之后删除
          setTimeout(
            () => {
              iframe.remove()
            },
            5 * 60 * 1000
          )
        }
        if (hasElectron() && window.desktopVersion && compareVersion(window.desktopVersion, '1.0.2') > 0) {
          await sleep(2000)
        }
      }
    },
    // 下载
    convertDownload(hasDownload = false) {
      if (hasElectron() && window.desktopVersion && compareVersion(window.desktopVersion, '1.0.2') > 0 && !hasDownload) {
        window.ipcRenderer.send('open-savePath', 'convertList', this.checkSelected[0].title)
      } else {
        this.startDownload()
      }
    },
    /**
     * 检查文件名是否可用
     * @param docName
     * @returns {boolean}, true 标签名可用. false 标签名重复
     */
    checkDocName(docName: string) {
      return this.dataList.findIndex(item => item.title === docName) === -1
    },
    // 删除
    async convertDelete() {
      this.showRemoveConfirm = true
    },
    // 确认删除
    async removeConfirm() {
      const self = this
      // token, deviceId, sid, type = '', type_value
      const total = this.checkSelected.length
      this.showRemoveConfirm = false
      while (this.checkSelected.length) {
        const i = 0
        const selectItem = this.checkSelected[i]
        bus.emit('toast', {
          msg: self.$t('web_3_deleting'),
          type: 'progress',
          desc: '',
          total: total,
          current: i
        })
        const res = await deleteShare(this.token, '', selectItem.sid || '', '', selectItem.type)
        if (res.error_code === 0) {
          this.dataList.splice(
            this.dataList.findIndex(item => item.sid === selectItem.sid),
            1
          )
        }
      }
      bus.emit('toast', {
        msg: self.$t('cs_518b_delete_success'),
        type: 'success'
      })
    },
    /**
     * 最外框捕获阶段点击事件，关闭其他弹窗
     */
    clearAll() {
      const self = this
      self.showMorePopup = false
    },
    /**
     * 文件右键菜单（由于右键默认选中，直接处理已选中列表）
     */
    more(event: any) {
      const self = this
      self.clearAll()
      self.showMorePopup = true
      self.morePopupLeft = event.pageX
      self.morePopupTop = event.pageY
    },
    checkSelectDoc(event: Event, id: string) {
      this.dataList.map(item => {
        if (item.sid === id) {
          item.selected = !item.selected
        }
      })
      event.preventDefault()
      event.stopPropagation()
    },
    dateFormat(t: number) {
      return format(t * 1000, 'yyyy-MM-dd hh:mm:ss')
    },
    mouseDown(event: MouseEvent) {
      const self = this
      const dom = document.getElementById('shape') as HTMLElement
      self.mouseShapeDown = true
      dom.style.left = event.clientX + 'px'
      dom.style.top = event.clientY + 'px'
      self.mouseShapeX = event.clientX
      self.mouseShapeY = event.clientY
    },
    mouseUp() {
      const self = this
      const dom = document.getElementById('shape') as HTMLElement
      self.mouseShapeDown = false
      dom.style.left = '0px'
      dom.style.top = '0px'
      dom.style.width = '0px'
      dom.style.height = '0px'
    },
    mouseMove(event: MouseEvent, item?: DataListOptions) {
      const self = this
      if (self.mouseShapeDown && Math.abs(self.mouseShapeY - event.clientY) > 3) {
        if (item) {
          item.selected = true
        }
        const dom = document.getElementById('shape') as HTMLElement
        dom.style.width = Math.abs(event.clientX - self.mouseShapeX) + 'px'
        dom.style.height = Math.abs(event.clientY - self.mouseShapeY) + 'px'
        if (event.clientX - self.mouseShapeX < 0) {
          dom.style.left = event.clientX + 'px'
        }
        if (event.clientY - self.mouseShapeY < 0) {
          dom.style.top = event.clientY + 'px'
        }
      }
    },
    /**
     * 选中当前文件
     * 1. 当前文件已选中时，无事发生
     * 2. 当前文件未选中时，反选其他所有文件，单选当前文件
     */
    selectCurrentDoc(sid: string, event: Event) {
      this.dataList.map(item => {
        item.selected = item.sid === sid
      })
      event.preventDefault()
      event.stopPropagation()
    },
    // 检查权限状态
    checkSelectAll() {
      const self = this
      if (self.isSelected) {
        self.clearSelect()
      } else {
        self.selectAll()
      }
    },
    /**
     * 清除选中
     * 1. 清除全选状态
     * 2. 清除文件夹和文档选中状态
     */
    clearSelect() {
      this.isSelected = false
      this.isSelectedAll = false
      this.dataList.map(item => {
        item.selected = false
      })
    },
    /**
     * 选中全部
     * 1. 设置全选状态
     * 2. 设置文件夹和文档选中状态
     */
    selectAll() {
      this.isSelected = true
      this.isSelectedAll = true
      this.dataList.map(item => {
        item.selected = true
      })
    },
    spOptions(event: Event, sid: string) {
      this.more(event)
      this.selectCurrentDoc(sid, event)
    },
    spOptionsAnother(event: Event, sid: string) {
      this.selectCurrentDoc(sid, event)
      this.convertDownload()
    }
    /**
     * 鼠标拖拽选中事件
     * @param docId 当前响应事件文件id
     */
    // mouseEnterDoc(docId) {
    //   const self = this
    //   let startIndex // 待处理文件的初始下标
    //   let endIndex // 当前响应文件的初始下标
    //   let lastIndex // 上次响应文件的下标
    //   if (!self.mouseMoveStartDirId && !self.mouseMoveStartDocId) {
    //     let index = self.selectedDocArray.indexOf(docId)
    //     if (index === -1) {
    //       self.selectedDocArray.push(docId)
    //       self.docMap[docId].selected = true
    //     }
    //     self.mouseMoveStartDocId = docId
    //     return
    //   }
    //   if (self.mouseMoveStartDocId) {
    //     // mouseMoveStartDocId有值，说明是从文件开始拖拽选中
    //     startIndex = self.docArray.indexOf(self.mouseMoveStartDocId)
    //     endIndex = self.docArray.indexOf(docId)
    //   } else {
    //     // mouseMoveStartDirId有值，说明是从文件夹开始拖拽选中
    //     // 由于文档在文件夹之后，因此
    //     // 文档是从 第一个 到 docId 对应的文档
    //     // 文件夹是从 mouseMoveStartDirId有值 对应的文件夹 到 最后一个
    //     startIndex = 0
    //     endIndex = self.docArray.indexOf(docId)

    //     // 检查应被选中文件夹
    //     let dirStartIndex = self.dirArray.length - 1
    //     let index = self.selectedDocArray.indexOf(self.dirArray[dirStartIndex])
    //     if (index === -1) {
    //       let dirEndIndex = self.dirArray.indexOf(self.mouseMoveStartDirId)
    //       for (let i = dirStartIndex; i >= dirEndIndex; i--) {
    //         // 选中
    //         let dirId = self.dirArray[i]
    //         let index = self.selectedDirArray.indexOf(dirId)
    //         if (index === -1) {
    //           self.selectedDirArray.push(dirId)
    //           self.dirMap[dirId].selected = true
    //         } else {
    //           // 若已选中，则说明上一个循环中已将该文档之后的文档全部选中了
    //           break
    //         }
    //       }
    //     }
    //   }

    //   if (self.mouseMoveLastDocId) {
    //     // mouseMoveLastDocId 有值，则说明上一个响应事件的是文件
    //     // 否则，上一个响应时间的是文件夹，则不用判断，文件依旧按照选中的流程处理
    //     lastIndex = self.docArray.indexOf(self.mouseMoveLastDocId)
    //     if ((lastIndex > startIndex && startIndex >= endIndex) || (lastIndex < startIndex && startIndex <= endIndex)) {
    //       // 该条件分支说明，上次响应的文件和当前响应的文件在初始文件的两边，因此需要反选 [lastIndex, startIndex) 区间的文件
    //       for (let j = lastIndex; lastIndex < startIndex ? j < startIndex : j > startIndex; lastIndex < startIndex ? j++ : j--) {
    //         // 反选
    //         let currentDocId = self.docArray[j]

    //         let index = self.selectedDocArray.indexOf(currentDocId)
    //         if (index !== -1) {
    //           self.selectedDocArray.splice(index, 1)
    //           self.docMap[currentDocId].selected = false
    //         }
    //       }
    //     } else if ((lastIndex > endIndex && endIndex >= startIndex) || (lastIndex < endIndex && endIndex <= startIndex)) {
    //       // 该条件分支说明，上次响应的文件距离本次响应的文件距离初始文件更远，因此需要反选 [lastIndex, endIndex) 区间的文件
    //       for (let j = lastIndex; lastIndex < endIndex ? j < endIndex : j > endIndex; lastIndex < endIndex ? j++ : j--) {
    //         // 反选
    //         let currentDocId = self.docArray[j]

    //         let index = self.selectedDocArray.indexOf(currentDocId)
    //         if (index !== -1) {
    //           self.selectedDocArray.splice(index, 1)
    //           self.docMap[currentDocId].selected = false
    //         }
    //       }
    //     }
    //   } else if (self.mouseMoveLastDirId) {
    //     if (self.mouseMoveStartDocId) {
    //       // 该条件分支说明，初始选中和当前目标都是文件，上一次响应为文件夹
    //       // 该分支下，应该反选上一次响应的文件夹到最后一个文件夹，第一个文件到当前文件
    //       let lastDirIndex = self.dirArray.indexOf(self.mouseMoveLastDirId)
    //       for (let j = lastDirIndex; j <= self.dirArray.length - 1; j++) {
    //         // 反选文件夹
    //         let currentDirId = self.dirArray[j]

    //         let index = self.selectedDirArray.indexOf(currentDirId)
    //         if (index !== -1) {
    //           self.selectedDirArray.splice(index, 1)
    //           self.dirMap[currentDirId].selected = false
    //         }
    //       }

    //       for (let j = 0; j <= endIndex - 1; j++) {
    //         // 反选文件
    //         let currentDocId = self.docArray[j]

    //         let index = self.selectedDocArray.indexOf(currentDocId)
    //         if (index !== -1) {
    //           self.selectedDocArray.splice(index, 1)
    //           self.docMap[currentDocId].selected = false
    //         }
    //       }
    //     }
    //   }

    //   self.mouseMoveLastDirId = ''
    //   self.mouseMoveLastDocId = docId

    //   for (let i = endIndex; endIndex > startIndex ? i >= startIndex : i <= startIndex; endIndex > startIndex ? i-- : i++) {
    //     // 选中
    //     let currentDocId = self.docArray[i]

    //     let index = self.selectedDocArray.indexOf(currentDocId)
    //     if (index === -1) {
    //       self.selectedDocArray.push(currentDocId)
    //       self.docMap[currentDocId].selected = true
    //     } else {
    //       // 若已选中，则说明上一个循环中已将该文件夹之后的文件夹全部选中了
    //       break
    //     }
    //   }
    // }
  }
})
