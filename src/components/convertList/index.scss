@import "@/styles/_variables";

.main {
  display: flex;
  width: 100%;
  height: 100%;
  font-size: 14px;
  overflow: hidden;
  user-select: none;
  -webkit-user-drag: none;
  user-drag: none;
  position: relative;
}

.loading {
  position: fixed;
  top: calc(50% - 15px);
  left: calc(50% - 40px);
  background: transparent;
}

.main_right {
  display: flex;
  flex: 1;
  flex-direction: column;
  background: $white;
  overflow: hidden;
}

#shape {
  position: fixed;
  background-color: rgb(139, 191, 249);
  border: 1px solid rgb(19, 98, 180);
  opacity: 0.5;
  z-index: 999;
  pointer-events: none
}

.main_top_wrap {
  display: flex;
  flex: none;
  padding: 19px 50px 17px 40px;
  height: 36px;
  border: 0;
  border-bottom: 1px solid $gray_middle;

  .main_top_btns {
    display: flex;
    flex: 1;

    div {
      position: relative;
      flex: none;
    }

    .btn_list {
      div+div {
        margin-left: 20px;
      }
    }

    .btn_upload {
      position: absolute;
      display: block;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      opacity: 0;
      cursor: pointer;
    }

    .btn_group {
      flex: none;
      display: flex;
      height: 34px;
      border: 1px solid $gray_deep;
      border-radius: 2px;
      cursor: pointer;

      .btn {
        flex: none;
        min-width: 50px;
        padding: 0 16px;
        font-size: 0;
        border: 0;

        &+.btn {
          border-left: 1px solid $gray_deep;
        }
      }

      span {
        font-size: 14px;
        color: $font_gray_middle;
        line-height: 34px;
        vertical-align: middle;
      }

      [class^="icon-"],
      [class*=" icon-"] {
        font-size: 20px;
      }
    }
  }
}

.files_top_wrap {
  position: relative;
  flex: 0;
  margin-top: 6px;
  padding: 0 50px 0 40px;
  height: 38px;

  .route_wrap {
    float: left;
    padding-right: 20px;
    height: 38px;
    background: url(./images/ic_down_list.png) no-repeat right center;
    background-size: 20px 20px;
    cursor: pointer;

    div {
      display: inline-block;
    }

    span {
      display: inline-block;
      height: 38px;
      font-size: 16px;
      color: $font_gray_deep;
      line-height: 38px;
      font-weight: bold;
      vertical-align: middle;
    }

    &.dir {
      background: none;
      cursor: auto;
    }

    .breadcrumb {
      cursor: pointer;
    }

    .dir_name {
      display: inline-block;
      max-width: 120px;
    }

    .tag_show {
      max-width: 400px;
      height: 38px;
      line-height: 38px;
      cursor: pointer;
      vertical-align: middle;
    }

    .separator {
      width: 30px;
      text-align: center;
    }
  }

  .tag_wrap {
    position: absolute;
    top: 38px;
    left: 40px;
    width: 240px;
    max-height: 400px;
    background: $white;
    border: 1px solid $gray_shallow;
    border-radius: 2px;
    z-index: 11;
    overflow-y: auto;

    .tag_item {
      display: block;
      padding: 0 20px;
      height: 40px;
      color: $font_gray_middle;
      line-height: 40px;
      cursor: pointer;

      &:hover {
        background: $white_middle;
      }
    }

    .tag_setting {
      position: absolute;
      top: 5px;
      right: 5px;
      width: 30px;
      height: 30px;
      font-size: 0;
      cursor: pointer;
      text-align: center;
      border-radius: 2px;

      &:hover {
        background-color: $white_hover;
      }

      [class^="icon-"],
      [class*=" icon-"] {
        font-size: 20px;
        line-height: 30px;
      }
    }
  }

  .display_mode_wrap {
    display: flex;
    float: right;

    [class^="icon-"],
    [class*=" icon-"] {
      font-size: 20px;
      line-height: 38px;
    }

    .list_mode {
      width: 38px;
      height: 38px;
      color: $font_gray_shallow;
      text-align: center;
      border-radius: 2px;
      position: relative;

      &:hover {
        background-color: $white_hover;
      }
    }

    .grid_mode {
      margin-left: 4px;
      width: 38px;
      height: 38px;
      color: $font_gray_shallow;
      text-align: center;
      border-radius: 2px;
      position: relative;

      &:hover {
        background-color: $white_hover;
      }
    }

    &.list .list_mode {
      color: #19BCAA;
    }

    &.grid .grid_mode {
      color: #19BCAA;
    }
  }
}

.file_empty {
  padding-top: 320px;
  background: url(./images/ic_default_blank_doc.png) no-repeat top center;
  background-size: 320px 320px;

  .file_empty_tip {
    font-size: 16px;
    color: $font_gray_middle;
    line-height: 24px;
    text-align: center;
  }
}

.display_wrap {
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  background: #FFFFFF;
}

.sort_wrap {
  position: relative;
  margin-left: 40px;
  padding-right: 10px;
  height: 30px;
  font-size: 14px;
  color: $font_gray_deep;
  line-height: 30px;
  border: 0;
  border-bottom: 1px solid $gray_shallow;
  padding: 12px 0;

  &:hover {
    .checkbox {
      display: block !important;
    }
  }

  &.grid {
    cursor: pointer;
  }

  .checkbox_wrap {
    position: absolute;
    top: 12px;
    left: -40px;
    width: 40px;
    height: 30px;

    &.selected {
      .checkbox {
        background: $green_brand;
        border-color: $green_brand;
      }
    }

    .icon-half-check {
      display: inline-block;
      width: 16px;
      height: 16px;
      background: url(./images/check.png) no-repeat center center;
      background-size: contain;
    }
  }

  .checkbox {
    display: none;
    margin: 6px 11px;
    width: 18px;
    height: 18px;
    background: $white;
    font-size: 0;
    color: $white;
    border: 1px solid $font_gray_shallow;
    border-radius: 2px;
    box-sizing: border-box;
    cursor: pointer;

    [class^="icon-"],
    [class*=" icon-"] {
      font-size: 16px;
    }
  }
}

.sort_icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  color: #19BCAA;
  background: url(./images/ic_sort_arrow.png) no-repeat center 5px;
  background-size: 20px 20px;
  opacity: 0;

  &.asc {
    background: url(./images/ic_sort_arrow.png) no-repeat center -5px;
    background-size: 20px 20px;
    transform: rotate(-180deg);
  }
}

.active {
  .sort_icon {
    opacity: 1;
  }
}

.column_name {
  position: relative;
  float: left;
  width: 56%;
  box-sizing: border-box;
  overflow: hidden;
  word-break: keep-all;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.column_sort {
  cursor: pointer;
}

.column_prop {
  display: flex;
  float: left;
  margin-right: 2%;
  width: 16%;
  overflow: hidden;
}

.column_time {
  float: left;
  width: 25%;
  overflow: hidden;
  word-break: keep-all;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.files_main_wrap {
  overflow-y: auto;
}

.files_row {
  position: relative;
  height: 60px;

  .files_row_inner {
    margin-left: 40px;
    padding-right: 10px;
    height: 60px;
  }

  &+.files_row .files_row_inner {
    border: 0;
    border-top: 1px solid $gray_shallow;
  }

  .files_first_page {
    position: absolute;
    top: 10px;
    left: 40px;
    width: 40px;
    height: 40px;
    z-index: 1;

    .first_page {
      width: 40px;
      height: 40px;
    }
  }

  .column_name {
    padding-left: 70px;
    background: url(./images/ic_default_loading_thumbnail.png) no-repeat left center;
    background-size: 40px 40px;
    font-size: 14px;
    color: $font_gray_deep;
    line-height: 60px;

    &>span:hover {
      color: $green_brand;
      cursor: pointer;
    }
  }

  .column_name_none_img{
    background: none;
  }

  .column_prop {
    font-size: 0;
    line-height: 60px;
  }

  .column_time {
    color: $font_gray_shallow;
    line-height: 60px;
  }

  .prop {
    display: inline-block;
    flex: none;

    &+.prop {
      padding-left: 5px;
    }

    span {
      font-size: 14px;
      color: $font_gray_shallow;
      vertical-align: middle;
    }

    [class^="icon-"],
    [class*=" icon-"] {
      font-size: 18px;
    }
  }

  .checkbox_wrap {
    position: absolute;
    top: 0;
    left: 0;
    width: 40px;
    height: 60px;
  }

  .checkbox {
    display: none;
    margin: 21px 11px;
    width: 18px;
    height: 18px;
    background: $white;
    font-size: 0;
    color: $white;
    border: 1px solid $font_gray_shallow;
    border-radius: 2px;
    box-sizing: border-box;
    cursor: pointer;

    [class^="icon-"],
    [class*=" icon-"] {
      font-size: 16px;
    }
  }

  .doc_opts {
    position: absolute;
    opacity: 0;
    top: 0;
    right: 0;
    bottom: 0;
    padding-right: 24px;
    width: 90px;
    height: 60px;
    background: $green_deep_0_1;

    .opt {
      float: left;
      width: 30px;
      height: 60px;
      font-size: 20px;
      color: $green_brand;
      line-height: 60px;
      text-align: center;
      cursor: pointer;
    }
  }

  &:hover,
  &.selected {
    background: $green_deep_0_1;

    .checkbox {
      display: block;
    }
  }

  &:hover {
    .doc_opts {
      opacity: 1;
    }
  }

  &.selected {
    .checkbox {
      background: $green_brand;
      border-color: $green_brand;
    }

    .doc_opts {
      display: none;
    }
  }
}

.team_row {
  .column_name {
    background: url(./images/ic_folder_team.png) no-repeat left center;
    background-size: 42px 42px;
  }
}

.dir_row {
  .column_name {
    background: url(./images/ic_folder_list.png) no-repeat left center;
    background-size: 42px 42px;
  }

  .doc_opts {
    width: 30px;
  }
}

.grid_list {
  margin: 10px 0 10px 40px;
  overflow: hidden;

  &+.grid_list {
    margin-top: 40px;
  }

  .dir_grid {
    float: left;
    padding-top: 80px;
    width: 140px;
    height: 50px;
    background: url(./images/ic_folder_list.png) no-repeat center top;
    background-size: 84px 84px;
    cursor: pointer;
  }

  .doc_grid {
    float: left;
    margin-top: 20px;
    width: 140px;
    height: 196px;
    cursor: pointer;
  }

  .grid_image1 {
    margin: 10px 20px;
    width: 100px;
    height: 124px;
    object-fit: cover;
    // border: 1px solid #D9DEE3;
  }

  .grid_title {
    margin: 0 20px;
    max-height: 40px;
    font-size: 14px;
    color: $font_gray_deep;
    line-height: 20px;
    text-align: center;
    overflow: hidden;
  }

  .checkbox_wrap {
    position: absolute;
    top: 0;
    left: 0;
    width: 26px;
    height: 26px;
  }

  .checkbox {
    display: none;
    margin: 5px;
    width: 16px;
    height: 16px;
    background: $white;
    font-size: 0;
    color: $white;
    border: 1px solid $font_gray_shallow;
    border-radius: 2px;
    box-sizing: border-box;

    [class^="icon-"],
    [class*=" icon-"] {
      font-size: 15px;
    }
  }

  .grid_item {
    position: relative;

    &:hover,
    &.selected {
      background-color: #F7F8F9;

      .checkbox {
        display: block;
      }
    }

    &.selected {
      .checkbox {
        background: $green_brand;
        border-color: $green_brand;
      }
    }
  }
}

.checkbox_active {
  .checkbox {
    display: block;
  }
}

.title_edit_wrap {
  display: flex;
  padding-top: 18px;
  max-width: 200px;
  height: 60px;
  box-sizing: border-box;

  .title_edit {
    flex: 1;
    display: block;
    padding: 0 5px;
    height: 24px;
    border-radius: 2px;
    border: 1px solid $gray_middle;
    box-sizing: border-box;
  }

  .btn {
    flex: none;
    margin-left: 10px;
    width: 24px;
    height: 24px;
    font-size: 0;
    line-height: 24px;
    border-radius: 2px;
    border: 1px solid rgba(239, 239, 239, 1);
    box-sizing: border-box;
    text-align: center;
    cursor: pointer;
    
    [class^="icon-"],
    [class*=" icon-"] {
      font-size: 18px;
      vertical-align: middle;
      line-height: 24px;
    }

    &.title_save {
      color: $green_brand;
    }

    &.title_unsave {
      color: $font_gray_shallow;
    }
  }
}

.grid_list {
  .title_edit_wrap {
    display: block;
    margin: 0 auto;
    padding: 0;
    width: 100px;

    .title_edit {
      width: 100%;
    }

    .btn {
      display: inline-block;
    }

    .btn:first-of-type {
      margin-left: 21px;
    }
  }
}

.sort_popup {
  position: absolute;
  top: 35px;
  left: 0;
  width: 180px;
  background: $white;
  border: 1px solid $gray_shallow;
  box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.20);
  z-index: 1;

  .sort_title {
    padding-left: 20px;
    height: 40px;
    font-size: 12px;
    color: $font_gray_shallow;
    line-height: 40px;
    border-bottom: 1px solid $gray_shallow;
  }

  .sort_type {
    padding: 0 20px;
    height: 40px;
    font-size: 12px;
    color: $font_gray_deep;
    line-height: 40px;

    &.selected {
      color: $green_brand;
    }
  }
}

.mask_download {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9;
  background: rgba(0, 0, 0, 0.6);

  .download_wrap {
    position: fixed;
    top: 50%;
    left: 50%;
    margin: -240px 0 0 -220px;
    width: 440px;
    height: 484px;
    background: #FFFFFF;
    box-shadow: 0 3px 8px 0 rgba(0, 0, 0, 0.14);
    border-radius: 2px;
    animation: downShow 0.5s linear;

    .type {
      margin: 44px auto 0;
      width: 100px;
      height: 100px;
      background: no-repeat center center;
      background-size: 100px 100px;
    }

    .name {
      margin-top: 24px;
      height: 28px;
      font-size: 20px;
      color: $font_gray_middle;
      line-height: 25px;
      text-align: center;
    }

    .btns {
      margin: 70px auto 0;
      width: 340px;
    }

    .btn_green {
      margin-top: 30px;
      padding: 0;
      width: 340px;
      height: 44px;
      font-size: 16px;
      color: $white;
      line-height: 44px;
      text-align: center;
      border-radius: 4px;
      cursor: pointer;

      &:nth-child(3) {
        background: $white;
        color: $green_brand;
        border-width: 2px;
      }
    }

    .open_btn {
      display: none;
    }

    .print_btn {
      display: none;
    }

    .get_more {
      display: block;
      margin-top: 23px;
      font-size: 16px;
      color: $green_brand;
      line-height: 22px;
      text-align: center;
    }

    .esc {
      position: absolute;
      top: 20px;
      right: 20px;
      width: 22px;
      height: 22px;
      cursor: pointer;
      color: $font_gray_middle;

      [class^="icon-"],
      [class*=" icon-"] {
        font-size: 22px;
      }
    }
  }

  .txt {
    .type {
      background-image: url(./images/ic_pc_download_txt.png);
    }
  }

  .word {
    .type {
      background-image: url(./images/ic_pc_download_doc.png);
    }
  }

  .excel {
    .type {
      background-image: url(./images/ic_pc_download_xls.png);
    }
  }

  .ppt {
    .type {
      background-image: url(./images/ic_pc_download_ppt.png);
    }
  }

  .pdf {
    .type {
      background-image: url(./images/ic_pc_download_pdf.png);
    }

    .open_btn {
      display: block;
    }

    .print_btn {
      display: block;
    }
  }
}

// @-webkit-keyframes show {
//   0% {
//     display: block;
//     opacity: 0;
//   }

//   100% {
//     opacity: 1;
//   }
// }

// @-o-keyframes show {
//   0% {
//     display: block;
//     opacity: 0;
//   }

//   100% {
//     opacity: 1;
//   }
// }

@keyframes downShow {
  0% {
    // top: 968px;
    top: 150%;
  }

  100% {
    top: 50%;
  }
}