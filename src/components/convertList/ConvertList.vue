<template>
  <div class="main" @mouseup.left="mouseUp" @mousemove.left="mouseMove" @mousedown.left="mouseDown">
    <!-- 列表模式 -->
    <div class="display_wrap" :class="{ checkbox_active: checkSelected.length }" v-if="displayModeParent === 'list'">
      <div class="sort_wrap">
        <div class="checkbox_wrap" :class="isSelected ? 'selected' : ''" @click="checkSelectAll()">
          <div class="checkbox">
            <span :class="isSelectedAll ? 'icon-ic_rename_confirm' : isSelected ? 'icon-half-check' : ''"></span>
          </div>
        </div>
        <div class="column_name" v-show="checkSelected.length">{{ $t('web_3_select_count', [checkSelected.length]) }}</div>
        <div class="column_name column_sort" v-show="checkSelected.length === 0">{{ $t('web_3_doclist_name') }}</div>
        <div class="column_time column_sort" v-show="checkSelected.length === 0">{{ $t('web_3_doclist_modifytime') }}</div>
      </div>
      <div class="files_main_wrap">
        <!-- 文档列表 -->
        <div class="files_row" v-for="item in dataList" :key="item.sid" :class="{ selected: item.selected }" @mousemove.left="mouseMove($event, item)" @click="selectCurrentDoc(item.sid, $event)" @contextmenu.prevent="spOptions($event, item.sid)">
          <div class="files_row_inner">
            <div class="checkbox_wrap" @click="checkSelectDoc($event, item.sid)">
              <div class="checkbox">
                <span class="icon-ic_rename_confirm"></span>
              </div>
            </div>
            <div class="files_first_page">
              <img class="first_page" :src="item.src" />
            </div>
            <div class="column_name c2" v-if="item.sid === editTargetId">
              <div class="title_edit_wrap">
                <input class="title_edit c11" type="text" v-model="titleEdit" v-focus @keyup.enter="saveTitle()" @keyup.esc="cancelRename()" />
                <div class="btn title_save" @click.stop="saveTitle()">
                  <span class="icon-ic_tag_rename_confirm"></span>
                </div>
                <div class="btn title_unsave" @click.stop="cancelRename()">
                  <span class="icon-ic_tag_rename_cancel"></span>
                </div>
              </div>
            </div>
            <div class="column_name" :class="item.src ? 'column_name_none_img' : ''" :title="item.title" v-else>
              <span>{{ item.title || '' }}</span>
              <div class="doc_opts" @click.stop="">
                <div class="opt" @click="spOptionsAnother($event, item.sid)">
                  <span class="icon-ic_maintop_download"></span>
                </div>
                <div class="opt" @click="spOptions($event, item.sid)">
                  <span class="icon-ic_hover_more"></span>
                </div>
              </div>
            </div>
            <div class="column_time">{{ dateFormat(Number(item.modify_time)) }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 列表模式 end -->
    <!-- 栅格模式 -->
    <div class="display_wrap" v-if="displayModeParent === 'grid'">
      <div class="sort_wrap grid">
        <div class="checkbox_wrap" :class="isSelected ? 'selected' : ''" @click.stop="checkSelectAll()">
          <div class="checkbox">
            <span :class="isSelectedAll ? 'icon-ic_rename_confirm' : isSelected ? 'icon-half-check' : ''"></span>
          </div>
        </div>
        <div class="column_name">
          <span>{{ $t('web_3_sort_time1') }}</span>
        </div>
      </div>
      <div class="files_main_wrap">
        <div class="grid_list">
          <div class="grid_item doc_grid" v-for="item in dataList" :key="item.sid" :class="{ selected: item.selected }" @mousemove.left="mouseMove($event, item)" @click="selectCurrentDoc(item.sid, $event)" @contextmenu.prevent="spOptions($event, item.sid)">
            <div class="checkbox_wrap" @click="checkSelectDoc($event, item.sid)">
              <div class="checkbox">
                <span class="icon-ic_rename_confirm"></span>
              </div>
            </div>
            <img class="grid_image1" :src="item.src" @dragstart="preventDefault($event)" />
            <div class="grid_title" v-show="item.sid !== editTargetId">
              {{ item.title }}
            </div>
            <div class="title_edit_wrap" v-if="item.sid === editTargetId" @click.stop="">
              <input class="title_edit" type="text" v-model="titleEdit" @click.stop="" @mousedown.stop="" @mouseup.stop="" @mousemove.stop="" @keyup.enter="saveTitle()" @keyup.esc="cancelRename()" />
              <div class="btn title_save" @click.stop="saveTitle()">
                <span class="icon-ic_tag_rename_confirm"></span>
              </div>
              <div class="btn title_unsave" @click.stop="cancelRename()">
                <span class="icon-ic_tag_rename_cancel"></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 栅格模式 end -->
    <!-- more popup -->
    <mainMorePopup :show="showMorePopup" :selectedDirArray="[]" :selectedDocArray="checkSelected" :top="morePopupTop" :left="morePopupLeft" :showConvertDownload="showConvertDownload" @convertRename="convertRename" @convertDownload="convertDownload" @convertDelete="convertDelete" @close="clearAll" />

    <!-- 确认删除 -->
    <modal :modalShow="showRemoveConfirm" :title="$t('web_3_delete_confirm')" :desc="$t('web_421_delete_tip2', [checkSelected.length])" :confirmText="$t('web_3_delete')" @confirm="removeConfirm" @cancel="showRemoveConfirm = false" />

    <!-- modal 批量下载 -->
    <modal :modalShow="showDownBatch" :title="$t('web_422_batch_download_title')" :desc="$t('web_422_batch_download_content')" :confirmText="$t('web_3_premium_buy')" @confirm="$router.push({ name: 'premium' })" @cancel="showDownBatch = false" />
  </div>
</template>
<script lang="ts" src="./component.ts"></script>
<style lang="scss" scoped src="./index.scss"></style>
