import { defineComponent } from 'vue'
import { setWebVersion } from '@/api/csapi'
import config from '@/config'
import { useUserStore } from '@/stores/user-store'

export default defineComponent({
  components: {},
  data() {
    return {}
  },
  computed: {
    v3Host() {
      return config.macro.V3_MAIN_DOMAIN
    }
  },
  created() {},
  methods: {
    returnToV3() {
      setWebVersion({ token: useUserStore().token, version: 'v3' }).then(() => {})
    }
  }
})
