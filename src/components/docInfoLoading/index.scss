@import "../../styles/_variables";

.doc_info_loading {
  position: fixed;
  top: 50%;
  left: 50%;
  margin: -200px 0 0 -220px;
  width: 440px;
  height: 405px;
  background: $white;
  text-align: center;
}
.logo {
  display: block;
  margin: 56px auto 0;
  height: 122px;
  &.cn {
    display: none;
  }
}
.title {
  margin-top: 24px;
  height: 25px;
  font-size: 18px;
  font-weight: 600;
  color: $font_gray_middle;
  line-height: 25px;
}
.desc {
  margin: 6px auto 24px;
  width: 335px;
  font-size: 14px;
  color: $font_gray_shallow;
  line-height: 20px;
}
.lottie_wrap {
  position: relative;
  margin: 0 auto;
  width: 60px;
  height: 60px;
}
.loading_lottie {
  position: absolute;
  top: -180px;
  left: -180px;
  width: 420px;
  height: 420px;
}
.lang-zh-cn {
  .cn {
    display: block;
  }
  .en {
    display: none;
  }
}