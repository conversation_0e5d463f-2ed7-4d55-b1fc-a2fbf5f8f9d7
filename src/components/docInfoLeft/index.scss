@import "../../styles/_variables";

.pages_left_wrap {
  display: none;
  flex-direction: column;
  align-items: center;
  width: 0;
  height: 100%;
  background: $black_0_8;
  overflow-x: hidden;
  overflow-y: scroll;
  &.click {
    animation: unfold_zero 0.25s;
    -o-animation: unfold_zero 0.25s;
    -webkit-animation: unfold_zero 0.25s;
  }
  &.unfold {
    width: 235px;
    &.click {
      animation: unfold_left 0.25s;
      -o-animation: unfold_left 0.25s;
      -webkit-animation: unfold_left 0.25s;
    }
  }
}
.thumb_item {
  margin-top: 35px;
  box-sizing: border-box;
  position: relative;
  &.status {
    .thumb_image {
      width: 130px;
      height: 130px;
      background: $white no-repeat center center;
      background-size: 130px 130px;
      border: 4px solid transparent;
    }
  }
  &.loading {
    .thumb_image {
      background-image: url(./images/ic_loading_small.png);
    }
  }
  &.error {
    .thumb_image {
      background-image: url(./images/ic_loading_fail.png);
    }
  }
  .thumb_image {
    border-radius: 4px;
    border: 4px solid transparent;
    &.active {
      border: 4px solid $green_brand;
    }
  }
  .thumb_mask {
    position: absolute;
    top: 4px;
    left: 4px;
    right: 4px;
    background: rgba(0, 0, 0, 0.5);
  }
  .thumb_scope {
    position: absolute;
    box-sizing: border-box;
    background-repeat: no-repeat;
    border: 2px solid black;
  }
  .thumb_no {
    height: 22px;
    line-height: 22px;
    margin-top: 6px;
    font-size: 16px;
    color: white;
    text-align: center;
  }
}
