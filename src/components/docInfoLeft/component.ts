import { defineComponent, type PropType } from 'vue'

export default defineComponent({
  props: {
    currentMode: {
      type: String,
      default: 'scroll'
    },
    isUnfoldLeft: {
      type: Boolean,
      default: false
    },
    leftWithAnimation: {
      type: Boolean,
      default: false
    },
    pageArray: {
      type: Array as PropType<string[]>,
      default: () => {
        return []
      }
    },
    pageMap: {
      type: Object as PropType<{
        [key: string]: any
      }>,
      default: () => {
        return {}
      }
    },
    currentPageId: {
      type: String,
      default: ''
    },
    pageWidth: {
      type: Number,
      default: 0
    },
    pageHeight: {
      type: Number,
      default: 0
    },
    currentRatio: {
      type: Number,
      default: 0
    },
    scopeTopRatio: {
      type: Number,
      default: 0
    },
    scopeLeftRatio: {
      type: Number,
      default: 0
    },
    scopeWidthRatio: {
      type: Number,
      default: 0
    },
    scopeHeightRatio: {
      type: Number,
      default: 0
    }
  },
  methods: {
    clickPage(pageId: string, index: number) {
      this.$emit('clickPage', pageId, index)
    },
    loadImageFail(pageId: string) {
      console.log(pageId)
    }
  }
})
