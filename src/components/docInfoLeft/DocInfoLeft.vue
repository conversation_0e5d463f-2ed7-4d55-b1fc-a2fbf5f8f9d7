<template>
  <div class="pages_left_wrap" v-if="currentMode !== 'small' && pageArray.length" :class="[isUnfoldLeft ? 'unfold' : '', leftWithAnimation && 'click']">
    <div class="thumb_item" :class="[pageMap[pageId].loading ? 'status loading' : pageMap[pageId].error ? 'status error' : '']" v-for="(pageId, index) in pageArray" :key="pageId" :id="'thumb_' + pageId">
      <img class="thumb_image" :class="[pageId == currentPageId ? 'active' : '']" :style="{ width: `${pageMap[pageId].thumbWidth}px`, height: `${pageMap[pageId].thumbHeight}px` }" crossorigin="anonymous" alt="" :src="pageMap[pageId].src" @click="clickPage(pageId, index)" @error="loadImageFail(pageId)" />
      <template v-if="pageId == currentPageId && ((((pageWidth - 60) * currentRatio) / 100 > pageWidth && currentMode === 'scroll') || (((pageWidth - 60) * currentRatio) / 100 / pageMap[currentPageId].originRatio > pageHeight && currentMode === 'scroll') || ((pageMap[currentPageId].singleWidth * currentRatio) / 100 > pageWidth && currentMode === 'single') || ((pageMap[currentPageId].singleHeight * currentRatio) / 100 > pageHeight && currentMode === 'single')) && !pageMap[pageId].loading && !pageMap[pageId].error">
        <div class="thumb_mask" :style="{ height: `${pageMap[pageId].thumbHeight || 130}px` }"></div>
        <div
          class="thumb_scope"
          :style="{
            width: `${scopeWidthRatio * pageMap[pageId].thumbWidth}px`,
            height: `${scopeHeightRatio * pageMap[pageId].thumbHeight}px`,
            top: `${4 + scopeTopRatio * pageMap[pageId].thumbHeight}px`,
            left: `${4 + scopeLeftRatio * pageMap[pageId].thumbWidth}px`,
            ['background-position']: `${-scopeLeftRatio * pageMap[pageId].thumbWidth - 2}px ${-scopeTopRatio * pageMap[pageId].thumbHeight - 2}px`,
            ['background-image']: `url(${pageMap[pageId].src})`,
            ['background-size']: `${pageMap[pageId].thumbWidth || 130}px ${pageMap[pageId].thumbHeight || 130}px`
          }"
        ></div>
      </template>
      <div class="thumb_no">{{ index + 1 }}</div>
    </div>
  </div>
</template>
<style lang="scss" scoped src="./index.scss"></style>
<script lang="ts" src="./component.ts"></script>
