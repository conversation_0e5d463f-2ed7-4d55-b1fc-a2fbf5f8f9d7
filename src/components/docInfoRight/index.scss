@import "../../styles/_variables";

.page_right_wrap {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 56px;
  height: 100%;
  background: $gray_shallow;
  border: 0;
  border-left: 1px solid $gray_middle;
  position: relative;
  text-align: center;
  .func_wrap {
    margin-top: 40px;
  }

  .func_train {
    display: none;
  }
  &.education {
    width: 124px;
    .func_train {
      position: absolute;
      display: flex;
      align-items: center;
      top: -15px;
      left: 25px;
      width: 112px;
      height: 80px;
      -webkit-transform: scale(0.5);
      -moz-transform: scale(0.5);
      -ms-transform: scale(0.5);
      -o-transform: scale(0.5);
      transform: scale(0.5);
      .func_train_inner {
        flex: 1;
        text-align: left;
        white-space: pre-wrap;
        font-size: 22px;
        color: $font_gray_deep;
      }
    }
  }
  .separator {
    margin: 10px 12px;
    height: 1px;
    background: $gray_deep;
  }
  .selected_line {
    position: absolute;
    width: 4px;
    height: 30px;
    left: 0;
    background: #2B6267;
    &.line_ocr {
      top: 92px;
    }
    &.line_signature {
      top: 142px;
    }
    &.line_watermark {
      top: 192px;
    }
    &.line_edit {
      top: 242px;
    }
  }
  .right_status {
    margin: 9px 3px 12px;
    width: 50px;
    height: 50px;
    background: no-repeat center center;
    background-size: 50px 50px;
    background-image: url(./images/ic_extend_left.png);
    cursor: pointer;
    position: relative;
    &.education_status {
      margin-left: 37px;
    }
    &.unfold {
      background-image: url(./images/ic_extend_right.png);
    }
  }
  .right_func {
    position: relative;
    margin: 0 auto 0 3px;
    width: 50px;
    height: 50px;
    background: no-repeat center center;
    background-size: 50px 50px;
    cursor: pointer;
    z-index: 500;
  }
  .func_ocr {
    background-image: url(./images/ic_ocr.png);
    &.selected {
      background-image: url(./images/ic_ocr_selected.png);
    }
  }
  .func_signature {
    background-image: url(./images/ic_signature.png);
    &.selected {
      background-image: url(./images/ic_signature_selected.png);
    }
  }
  .func_watermark {
    background-image: url(./images/ic_photo_watermark.png);
    &.selected {
      background-image: url(./images/ic_photo_watermark_selected.png);
    }
  }
  .func_edit {
    background-image: url(./images/ic_photo_edit.png);
    &.selected {
      background-image: url(./images/ic_photo_edit_selected.png);
    }
  }
  .func_more {
    background-image: url(./images/ic_photo_more.png);
  }
}
