// 印度地区增加公告-区分山寨产品
import { defineComponent } from 'vue'
import bus from '@/config/bus'
import { setCookie, getCookie } from '@/config/utils'
export default defineComponent({
  data() {
    return {
      showDialog: false
    }
  },
  created() {},
  mounted() {
    const self = this
    bus.on('open-india-tip', () => {
      if (!(getCookie('close-india-tip') && getCookie('close-india-tip') === 'accept')) {
        self.showDialog = true
      }
    })
  },
  methods: {
    closeDialog() {
      setCookie('close-india-tip', 'accept', new Date().getTime() + 60 * 1000 * 60 * 24)
      this.showDialog = false
    }
  }
})
