import FingerprintJS from '@fingerprintjs/fingerprintjs'
//@ts-ignore
import forge from 'node-forge'
import type { MsgsOptions } from '@/stores/message-store'
export const defaultMessageVersion = 1711438308058
const fpPromise = FingerprintJS.load()
export type TSocketParam = {
  wsUrl: string
  token: string
}
class SocketMessage {
  declare token: string
  declare wsUrl: string
  declare ws: WebSocket
  declare bytes: string
  declare handlers: Record<string, Function[]>
  declare markAsReadFunc: Function
  constructor(param: TSocketParam) {
    const config = param
    // websocket对象
    this.token = config.token
    this.wsUrl = config.wsUrl
    // 解密rsa的密钥
    this.bytes = ''
    // 事件数组
    this.handlers = {}
    // 已读消息回调
    // this.markAsReadFunc = null
    this.ws = new WebSocket(this.wsUrl)
  }
  init(fn: Function) {
    const { ws } = this
    ws.onclose = () => this.emit('onclose')
    // socket握手监听
    ws.onmessage = evt => {
      const data = JSON.parse(evt.data)
      // if (!data.body) {
      //   return
      // }
      if (data && data.cmdId === 8 && data.code === 0) {
        this.connect(data)
      }
      // 连接成功，开始认证
      if (data && data.cmdId === 10 && data.code === 0) {
        this.certification()
      }
      // 解密认证，验证是否认证成功
      if (data && data.cmdId === 10001 && data.code === 0) {
        const atobBase64 = window.atob(data.body)
        const res = this.decryptAes(atobBase64, this.bytes)
        const resBody = JSON.parse(res)
        if (resBody.code === 0) {
          // todo
          // return this.ws and call the function
          if (typeof fn === 'function') {
            fn()
          }
          // this.fetchMessage()
        }
        // this.fetchMessage()
      }
      // 收到新消息通知
      if (data && data.cmdId === 4 && data.code === 0) {
        this.emit('onGetNewTipMessage', data)
        // let atobBase64 = window.atob(data.body)
        // let res = this.decryptAes(atobBase64, this.bytes)
        // if (!res) {
        //   return
        // }
        // let resBody = JSON.parse(res)
        // if (resBody.code === 0) {
        // console.log('11来新消息了,认证成功')
        // }
      }
      // 收到批量拉取新消息
      if (data && data.cmdId === 10003 && data.code === 0) {
        const atobBase64 = window.atob(data.body)
        const res = this.decryptAes(atobBase64, this.bytes)
        const resBody = JSON.parse(res)
        if (resBody.code === 0) {
          // socketMessage.emit('getNewMessage')
          this.emit('onGetNewMessage', resBody)
          this.reportMessage(resBody)
        }
      }
      // 收到设置消息已读成功
      if (data && data.cmdId === 10009 && data.code === 0) {
        const atobBase64 = window.atob(data.body)
        const res = this.decryptAes(atobBase64, this.bytes)
        const resBody = JSON.parse(res)
        if (resBody.code === 0) {
          // 执行设置消息已读成功的回调函数
          if (typeof this.markAsReadFunc === 'function') {
            this.markAsReadFunc()
          }
        }
      }
    }
    return this.ws
  }
  // 注销websocket 链接
  disConnect() {
    this.ws.close()
  }
  // 主动发送拉取新消息
  // 默认时间 1711438308058
  fetchMessage(max_update_time = defaultMessageVersion) {
    const self = this
    const base64body = { max_update_time: max_update_time }
    const paramsBody = self.encryptAes(JSON.stringify(base64body), self.bytes)
    const obj = {
      body: paramsBody,
      ts: new Date().getTime(),
      clientVer: 0,
      serverVer: 0,
      code: 0,
      cmdId: 3,
      seqId: 0
    }
    this.ws.send(JSON.stringify(obj))
  }
  // 认证
  async certification() {
    const deviceId = await this.getDeviceId()
    const base64body = {
      token: this.token,
      type: 1,
      platform: 4,
      vendor: 'pc_web',
      device_id: 'WB_' + deviceId,
      appid: 'COM.CS.MAIN.01'
    }
    // 发起认证
    const paramsBody = this.encryptAes(JSON.stringify(base64body), this.bytes)
    const obj = {
      body: paramsBody,
      ts: new Date().getTime(),
      clientVer: 0,
      serverVer: 0,
      code: 0,
      cmdId: 1,
      seqId: 0
    }
    this.ws.send(JSON.stringify(obj))
  }
  // 订阅事件
  on(eventType: string, handler: Function) {
    const self = this
    if (!(eventType in self.handlers)) {
      self.handlers[eventType] = []
    }
    self.handlers[eventType].push(handler)
    return this
  }
  // 触发事件(发布事件)
  emit(eventType: string, ...args: Function[]) {
    if (this.handlers[eventType]) {
      this.handlers[eventType].forEach(listener => {
        listener.apply(this, args)
      })
    }
    return self
  }
  // 删除订阅事件
  off(eventType: string, handler: Function) {
    const currentEvent = this.handlers[eventType]
    let len = 0
    if (currentEvent) {
      len = currentEvent.length
      for (let i = len - 1; i >= 0; i--) {
        if (currentEvent[i] === handler) {
          currentEvent.splice(i, 1)
        }
      }
    }
    return this
  }
  // 标记已读
  markAsRead(data: string, fn: Function) {
    const base64body = {
      type: 0,
      list: data
    }
    // 发起认证
    const paramsBody = this.encryptAes(JSON.stringify(base64body), this.bytes)
    const obj = {
      body: paramsBody,
      ts: new Date().getTime(),
      clientVer: 0,
      serverVer: 0,
      code: 0,
      cmdId: 9,
      seqId: 0
    }
    this.ws.send(JSON.stringify(obj))
    if (typeof fn === 'function') {
      this.markAsReadFunc = fn
    }
  }
  // 更新消息extra字段，方便客户端自定义消息一些处理逻辑标记
  setExtra(msg_id: string, task_id: string, data: string) {
    const base64body = {
      msg_id,
      task_id,
      ext: JSON.stringify(data)
    }
    // 发起认证
    const paramsBody = this.encryptAes(JSON.stringify(base64body), this.bytes)
    const obj = {
      body: paramsBody,
      ts: new Date().getTime(),
      clientVer: 0,
      serverVer: 0,
      code: 0,
      cmdId: 15,
      seqId: 0
    }
    this.ws.send(JSON.stringify(obj))
  }
  // 发送socket连接
  connect(data: { body: string }) {
    // 解密base64
    const atobBase64 = window.atob(data.body)
    const jsonParse = JSON.parse(atobBase64)
    const publicKey = jsonParse.public_key
    // self.resPublicKey = publicKey
    // 发送握手
    const base64body = window.btoa(JSON.stringify({ key_bytes: this.encryptRsa(publicKey) }))
    const obj = {
      body: base64body,
      ts: new Date().getTime(),
      clientVer: 0,
      serverVer: 0,
      code: 0,
      cmdId: 10008,
      seqId: 0
    }
    this.ws.send(JSON.stringify(obj))
  }
  // 上报消息id列表
  reportMessage(data: { msgs: MsgsOptions[] }) {
    const list: Pick<MsgsOptions, 'msg_id' | 'task_id'>[] = []
    const dataObj = { list }
    if (data.msgs && data.msgs.length !== 0) {
      data.msgs.map(item => {
        dataObj.list.push({
          msg_id: item.msg_id,
          task_id: item.task_id
        })
      })
    }
    // 发起认证
    const paramsBody = this.encryptAes(JSON.stringify(dataObj), this.bytes)
    const obj = {
      body: paramsBody,
      ts: new Date().getTime(),
      clientVer: 0,
      serverVer: 0,
      code: 0,
      cmdId: 13,
      seqId: 0
    }
    this.ws.send(JSON.stringify(obj))
  }
  // rsa加密
  encryptRsa(public_key: string) {
    const encoded = forge.util.decode64(public_key)
    const arr = forge.util.createBuffer(encoded)
    const pki = forge.pki
    const asn1 = forge.asn1
    const obj = asn1.fromDer(arr)
    const publicKey = pki.publicKeyFromAsn1(obj)
    const md = forge.md.md5.create()
    md.update(new Date().getTime())
    let bytes = md.digest().toHex()
    bytes = bytes.substring(0, 16)
    this.bytes = bytes
    // encrypt data with a public key using RSAES PKCS#1 v1.5
    let encrypted = publicKey.encrypt(bytes, 'RSAES-PKCS1-V1_5')
    encrypted = forge.util.encode64(encrypted)
    return encrypted
  }
  // aes 加密
  encryptAes(body: string, key: string) {
    const cipher = forge.cipher.createCipher('AES-ECB', key)
    cipher.start()
    cipher.update(forge.util.createBuffer(body))
    cipher.finish()
    return forge.util.encode64(cipher.output.getBytes())
  }
  // aes 解密
  decryptAes(body: string, key: string) {
    const decipher = forge.cipher.createDecipher('AES-ECB', key)
    decipher.start()
    decipher.update(forge.util.createBuffer(body), 'utf8')
    decipher.finish() // check 'result' for true/false
    // outputs decrypted hex
    return forge.util.decodeUtf8(decipher.output.getBytes())
  }
  async getDeviceId() {
    try {
      const fp = await fpPromise
      const result = await fp.get()
      return result.visitorId
    } catch (err) {
      return null
    }
  }
}
export default SocketMessage
