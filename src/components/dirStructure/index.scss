@import '../../styles/variables';

.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  .dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    margin-left: -263px;
    margin-top: -221px;
    width: 527px;
    height: 442px;
    box-sizing: border-box;
    padding: 0 20px 19px 20px;
    background: #ffffff;
    border-radius: 2px;
    .top_wrap {
      width: 100%;
      height: 54px;
      margin-bottom: 9px;
      .title {
        height: 54px;
        text-align: left;
        font-size: 16px;
        font-weight: 500;
        color: #5a5a5a;
        line-height: 54px;
      }
    }
    .structure_wrap {
      width: 486px;
      height: 300px;
      border-radius: 1px;
      border: 1px solid #dcdcdc;
      overflow: scroll;
      cursor: default;
      .root_wrap {
        width: 486px;
        height: 38px;
        &.selected {
          background: rgba($color: #19bcaa, $alpha: 0.1);
        }
        .root_btn {
          margin: 12.5px 10px 0 9px;
          width: 13px;
          height: 13px;
          cursor: pointer;
          opacity: 0;
          float: left;
          &.can_unfold {
            font-size: 10px;
            font-weight: bolder;
            color: #5a5a5a;
            border-radius: 1px;
            border: 1px solid #d7d7d7;
            line-height: 13px;
            text-align: center;
            opacity: 1;
          }
        }
        .root {
          float: left;
          height: 38px;
          line-height: 38px;
          text-align: left;
          font-size: 16px;
          font-weight: 400;
          color: #5a5a5a;
          box-sizing: border-box;
          padding-right: 5px;
          &.disable {
            opacity: 0.6;
          }
        }
      }
    }
    .btn_wrap {
      margin-top: 24px;
      width: 486px;
      height: 36px;
      display: flex;
      align-items: center;
      .create_dir {
        width: 263px;
        display: flex;
        color: #19bcaa;
        align-items: center;
        font-size: 14px;
        cursor: pointer;
        .icon {
          width: 16px;
          height: 16px;
          background: url(./images/ic_new_dir.png) no-repeat center center;
          background-size: 16px 16px;
          margin-right: 4px;
        }
      }
      .btn {
        width: 100px;
        height: 36px;
        box-sizing: border-box;
        border-radius: 2px;
        text-align: center;
        line-height: 36px;
        font-weight: 400;
        font-size: 14px;
        cursor: pointer;
        & + .btn {
          margin-left: 24px;
        }
        &.disable {
          opacity: 0.3;
        }
      }
    }
  }
}

::-webkit-scrollbar {
  width: 0;
  height: 0;
}
