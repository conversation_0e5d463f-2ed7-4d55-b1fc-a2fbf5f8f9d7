<template>
  <div class="mask" v-if="show">
    <div class="dialog">
      <div class="top_wrap">
        <div class="title" v-if="dialogTitle">{{ dialogTitle }}</div>
        <div class="title" v-else-if="isMove">{{ $t('web_3_move_select') }}</div>
        <div class="title" v-else-if="hasUnfold">{{ $t('web_3_tool_choosefile_cs') }}</div>
        <div class="title" v-else>{{ $t('web_3_copy_select') }}</div>
      </div>
      <div class="structure_wrap">
        <div :class="['root_wrap', currentId === 'root' && selfDirId !== 'root' ? 'selected' : '']" @click="clickRoot">
          <div :class="['root_btn', (docShow ? dirStructure.dirs || dirStructure.docs : dirStructure.dirs) ? 'can_unfold' : '']" @click="setUnfold">{{ (docShow ? dirStructure.dirs || dirStructure.docs : dirStructure.dirs) ? (isUnfold ? '-' : '+') : '' }}</div>
          <div :class="['root', selfDirId === 'root' && 'disable']">{{ $t('web_3_my_files') }}</div>
        </div>
        <singleLevel v-show="isUnfold" class="subdir" :dir="dirStructure" :id="currentId" :newId="newDirId" :selfDirId="selfDirId" :selfDocId="selfDocId" ref="singleLevel" :whenCreate="whenCreateDir" :level="1" :docShow="docShow" @showtoast="showToast" @changestatus="changeCreateStatus" @changeid="changeId" />
      </div>
      <div class="btn_wrap">
        <div class="create_dir" @click="createDir">
          <div class="icon" v-if="hasNewDir"></div>
          <div class="text" v-if="hasNewDir">{{ $t('web_3_newfolder') }}</div>
        </div>
        <div class="btn btn_green" :class="confirmButtonDisabled ? 'disable' : ''" @click="confirm">{{ $t('web_3_confirm') }}</div>
        <div class="btn btn_gray" @click="cancel">{{ $t('web_3_cancel') }}</div>
      </div>
    </div>
    <modal :modalShow="showDirLimit" :title="$t('web_3_newfolder')" :desc="$t('web_3_folderlimit')" :confirmText="$t('web_3_premium_buy')" @confirm="enterPremium" @cancel="cancelPremium" />
    <toast :showToast="toastShow" :type="toastType" :text="toastText" />
    <!-- 文档加密弹窗 -->
    <PwdDialog ref="pwdDialog" @continuePwdDoc="confirm" />
  </div>
</template>
<style lang="scss" scoped src="./index.scss"></style>
<script lang="ts" src="./component.ts"></script>
