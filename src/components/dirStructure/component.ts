import { defineComponent } from 'vue'
import singleLevel from '../singleLevel/SingleLevel.vue'
import modal from '../modal/ModalComponent.vue'
import PwdDialog from '@/components/PwdDialog/PwdDialog.vue'
import toast from '../toast/ToastComponent.vue'
import type { QueryDirResult } from '@/api/sapi'
import { useDirStore } from '@/stores/dir-store'
import { useFileStore } from '@/pages/file/useFileStore'
import { mapState } from 'pinia'

interface DirStructureOptions extends QueryDirResult {
  docs?: {
    title: string
    doc_id: string
  }[]
}

export default defineComponent({
  name: 'dirStructure',
  components: {
    PwdDialog,
    singleLevel,
    modal,
    toast
  },
  props: [
    'show', // 是否展示
    'dialogTitle', // 窗口标题
    'isMove', // 是否是移动操作
    'dirMoveBlackList', // 禁止移动的文件夹列表
    'docShow', // 是否展示文档
    'selfDocId', // 当前所在文档id，页移动时传
    'selfDirId', // 当前所在文件夹id，文档移动时传
    'hasUnfold', // 是否默认展开
    'hasNewDir' // 是否展示新建文件夹
  ],
  data() {
    return {
      isUnfold: true, // 当前文件夹是否展开
      dirStructure: {} as DirStructureOptions, // 账号文件夹结构
      currentId: 'root', // 当前选中文件夹或文档id
      type: 'dir', // 当前选中的是文件夹还是文档
      newDirId: 'root', // 新建文件夹的母文件夹id
      confirmButtonDisabled: false, // 是否禁止点击确认键
      whenCreateDir: false,
      showDirLimit: false,
      toastShow: false,
      toastType: false,
      toastText: '',
      title: ''
    }
  },
  watch: {
    show(newValue) {
      if (newValue) {
        const self = this
        const dirStructure: DirStructureOptions = JSON.parse(window.localStorage.getItem('dirStructure') || '{}') || useDirStore().dirStructure
        dirStructure.docs = JSON.parse(window.localStorage.getItem('rootDocArray') || '{}') || useDirStore().rootDocArray || false
        console.log('dirStructure.docs', dirStructure.docs)
        dirStructure.dir_id = 'root'
        self.dirStructure = dirStructure
      }
    }
  },
  created() {
    const self = this
    const dirStructure: DirStructureOptions = JSON.parse(window.localStorage.getItem('dirStructure') || '{}') || useDirStore().dirStructure
    dirStructure.docs = JSON.parse(window.localStorage.getItem('rootDocArray') || '{}') || useDirStore().rootDocArray || false
    dirStructure.dir_id = 'root'
    self.dirStructure = dirStructure
    if (self.isMove && self.selfDirId === self.currentId) {
      self.confirmButtonDisabled = true
    } else {
      self.confirmButtonDisabled = false
    }
  },
  computed: {
    ...mapState(useFileStore, ['docList'])
  },
  methods: {
    // 展开或收起
    setUnfold() {
      const self = this
      self.isUnfold = !self.isUnfold
    },
    // 点击选中根目录
    clickRoot() {
      const self = this
      self.currentId = self.newDirId = 'root'
      if (self.isMove && self.selfDirId === self.currentId) {
        self.confirmButtonDisabled = true
      } else {
        self.confirmButtonDisabled = false
      }
      self.type = 'dir'
    },
    // 改变当前选中项
    changeId(data: any) {
      const self = this
      self.currentId = data.id
      if (self.isMove && ((self.dirMoveBlackList && self.dirMoveBlackList.indexOf(self.currentId) > -1) || self.selfDirId === self.currentId)) {
        self.confirmButtonDisabled = true
      } else {
        self.confirmButtonDisabled = false
      }
      self.type = data.type
      self.newDirId = data.newDirId
      self.title = data.title
    },
    // 点击确认
    confirm() {
      const { docList } = this
      const target = docList.find(item => item.file_name === this.currentId)
      // return
      const self = this
      if (self.confirmButtonDisabled) {
        return
      }
      const dialog = this.$refs.pwdDialog as any
      dialog.init({ type: 'doc' })
      if (target?.password && !dialog.pass) {
        dialog.handlerDialog(true, { docId: self.currentId, password: target.password, type: 'doc', extra: {} })
        return
      }

      self.$emit('confirm', {
        type: self.type, // 传当前选中项类型 dir or doc
        id: self.currentId, // 传当前选中项id
        title: self.title
      })
      ;(this.$refs.singleLevel as any).cancelNew()
    },
    // 点击取消
    cancel() {
      const self = this
      ;(this.$refs.singleLevel as any).cancelNew()
      self.$emit('cancel')
    },
    createDir() {
      const self = this
      if (self.newDirId === 'root') self.isUnfold = true
      self.whenCreateDir = true
    },
    changeCreateStatus(status: any) {
      const self = this
      self.whenCreateDir = status.whenCreateDir
      self.showDirLimit = status.showDirLimit
      if (status.dirs) {
        self.dirStructure = JSON.parse(status.dirs)
        useDirStore().setDirStructure(self.dirStructure)
        self.$emit('refreshdir')
      }
    },
    showToast(toast: any) {
      const self = this
      if (!toast.toastText) return
      self.toastText = toast.toastText
      self.toastType = toast.toastType || false
      self.toastShow = toast.toastShow
      setTimeout(() => {
        self.toastShow = self.toastType = false
        self.toastText = ''
      }, 3000)
    },
    enterPremium() {
      const self = this
      self.showDirLimit = self.whenCreateDir = false
      self.$router.push({ path: '/premium/index' })
    },
    cancelPremium() {
      const self = this
      self.showDirLimit = self.whenCreateDir = false
    }
  }
})
