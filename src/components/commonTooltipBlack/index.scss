@import '../../styles/variables';

.tooltip_target {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: $z-index_popup;
}
.tooltip {
  position: absolute;
  display: none;
  padding: 6px 8px;
  background: rgba(0, 0, 0, 0.75);
  font-size: 14px;
  color: $white;
  line-height: 22px;
  border-radius: 2px;
  word-break: keep-all;
  white-space: nowrap;
  z-index: 10000;
  opacity: 0;
  cursor: default;
  user-select: none;

  &.show {
    -webkit-animation: show 0.2s linear;
    -o-animation: show 0.2s linear;
    animation: show 0.2s linear;
    opacity: 1;
    display: block;
  }
  &.hide {
    opacity: 0;
  }

  &.showArrow {
    &.direction0:after {
      content: ' ';
      position: absolute;
      top: 100%; /* 提示工具头部 */
      left: 50%;
      margin-left: -4px;
      border: 4px solid transparent;
      border-top-color: rgba(0, 0, 0, 0.75);
    }
    &.direction1:after {
      content: ' ';
      position: absolute;
      top: 50%; /* 提示工具头部 */
      right: 100%;
      margin-top: -4px;
      border: 4px solid transparent;
      border-right-color: rgba(0, 0, 0, 0.75);
    }
    &.direction2:after {
      content: ' ';
      position: absolute;
      bottom: 100%; /* 提示工具头部 */
      left: 50%;
      width: 8px;
      height: 4px;
      transform: translateX(-50%);
      background: url('./images/tooltip-arrow.png') no-repeat;
      background-size: 100% 100%;
      //border: 4px solid transparent;
      //border-bottom-color: rgba(0, 0, 0, .75);
    }
    &.direction3:after {
      content: ' ';
      position: absolute;
      top: 50%; /* 提示工具头部 */
      left: 100%;
      margin-top: -6px;
      border: 6px solid transparent;
      border-left-color: rgba(0, 0, 0, 0.75);
    }
  }
  &.direction0 {
    // 上方
    bottom: 100%;
    left: 50%;
    margin-bottom: var(--tooltip-distance, 10px);
    transform: translateX(-50%);
  }
  &.direction1 {
    // 右方
    top: 50%;
    left: 100%;
    margin-left: var(--tooltip-distance, 10px);
    transform: translateY(-50%);
  }
  &.direction2 {
    // 下方
    top: 100%;
    left: 50%;
    margin-top: var(--tooltip-distance, 10px);
    transform: translateX(-50%);
  }
  &.direction3 {
    // 左方
    top: 50%;
    right: 100%;
    margin-right: var(--tooltip-distance, 10px);
    transform: translateY(-50%);
  }
}

@-webkit-keyframes show {
  0% {
    display: block;
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@-o-keyframes show {
  0% {
    display: block;
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes show {
  0% {
    display: block;
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
