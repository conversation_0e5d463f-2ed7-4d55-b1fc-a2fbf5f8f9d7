import { defineComponent } from 'vue'

import config from '../../config'

export default defineComponent({
  props: {
    i18nKey: {
      type: String,
      default: ''
    },
    direction: {
      type: Number,
      default: config.tooltipLower
    },
    showArrow: {
      type: Boolean,
      default: true
    },
    distance: {
      type: Number,
      default: 10
    },
    keepInView: {
      type: Boolean,
      default: false
    },
    delay: {
      type: Number,
      default: 300
    }
  },
  data() {
    return {
      show: false,
      timer: 0
    }
  },
  mounted() {},
  methods: {
    overFunc() {
      this.timer = window.setTimeout(() => {
        this.show = true
        // 判断是否超出边界
        this.$nextTick(() => {
          if (this.keepInView) {
            const tooltips = this.$refs.tooltip as HTMLElement
            const rect = tooltips.getBoundingClientRect()

            // 超出边界
            if (rect.x < 0) {
              tooltips.style.left = `-16px`
              tooltips.style.transform = `none`
            }
            if (rect.y < 0) {
              tooltips.style.top = `0px`
              tooltips.style.transform = `none`
            }
            if (rect.x > window.innerWidth) {
              tooltips.style.right = `0px`
              tooltips.style.transform = `none`
            }
            if (rect.y > window.innerHeight) {
              tooltips.style.bottom = `0px`
              tooltips.style.transform = `none`
            }
            // tooltips.offsetWidth
            // console.log('after', rect)
          }
        })
      }, this.delay)
    },
    leaveFunc() {
      clearTimeout(this.timer)
      this.show = false
    }
  }
})
