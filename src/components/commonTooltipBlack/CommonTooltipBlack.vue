<template>
  <div class="tooltip_target" @mouseover="overFunc" @mouseleave="leaveFunc">
    <div class="tooltip" ref="tooltip" :class="['direction' + direction, show ? 'show' : 'hide', showArrow ? 'showArrow' : 'hideArrow']" :style="{ '--tooltip-distance': distance + 'px' }">
      {{ $t(i18nKey) }}
      <slot></slot>
    </div>
  </div>
</template>
<script lang="ts" src="./component.ts"></script>
<style lang="scss" scoped src="./index.scss"></style>
