<script lang="ts">
import { defineComponent } from 'vue'
import { mapState } from 'pinia'
import { useUserStore } from '@/stores/user-store'

export default defineComponent({
  name: 'ContactCorpKefu',
  computed: {
    ...mapState(useUserStore, ['showContactKefuModal'])
  },
  methods: {
    handleClose() {
      useUserStore().showContactKefuModal = false
    }
  }
})
</script>

<template>
  <!-- 联系客服弹出框 -->
  <el-dialog :before-close="handleClose" center style="padding: 20px" class="contact-support-dialog" title="企业专属客服" :model-value="showContactKefuModal" :width="460">
    <div class="content-row">
      <div class="title">专属客服</div>
      <div class="desc" style="color: #00b796">021-61514746</div>
    </div>
    <div class="qrcode-container">
      <div class="title">扫码添加专属客户经理</div>
      <img class="qrcode" src="./images/corp-service-qrcode.jpg" />
      <div class="text">专属企微，售后无忧</div>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">
.contact-support-dialog {
  .content-row {
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title {
      font-size: 16px;
      color: #212121;
    }
    .desc {
      font-size: 16px;
      color: #9c9c9c;
    }
  }

  .qrcode-container {
    margin-top: 24px;
    background: #f7f7f7;
    padding: 24px 0;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .title {
      line-height: 20px;
      font-size: 14px;
      font-weight: bold;
      color: #212121;
    }

    .qrcode {
      display: block;
      object-fit: contain;
      margin-top: 4px;
      width: 140px;
      height: 140px;
    }

    .text {
      margin-top: 16px;
      font-size: 12px;
      color: #9c9c9c;

      &:last-child {
        margin-top: 4px;
      }
    }
  }
}
:root {
  --el-color-primary: rgb(12, 189, 151);
}
</style>
