<template>
  <div class="upload_finish_wrap">
    <div class="upload_finish_body">
      <div class="success-info">
        <div class="finish_title">
          {{ $t('web_3_tool_convert_success', [type]) }}
        </div>
        <div class="file_name">
          <img class="ic_toolbox_doc" :src="iconMap[type]" />
          <div class="file_name_text">{{ handleFileName() }}</div>
        </div>
      </div>
      <div class="download_doc-wrap">
        <div class="btn_green download_btn btn_down" @click="debouncedDownload" :class="{ 'disabled': isDisabled }">
          <div class="download_btn_des">{{ $t('web_3_download') + ' ' + type + ' ' + $t('web_3_file') }}</div>
          <div class="download_btn_tips">{{ $t('web_3_freeicon') }}</div>
        </div>
      </div>
    </div>
    <emailDialog :sid="sid" :downloadType="downloadType" :eid="eid" :fileName="fileNameTxt" v-model:dialogVisible="dialogVisible" />
    <loginDialog ref="loginDialog" />
    <DirStructureModal @confirm="saveTo"></DirStructureModal>
  </div>
</template>
<script lang="ts" src="./component.ts"></script>
<style lang="scss" scoped src="./index.scss"></style>
<style lang="scss">
.el-tooltip__popper {
  min-width: 50px;
}
</style>
