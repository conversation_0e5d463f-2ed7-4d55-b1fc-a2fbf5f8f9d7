import { mapActions, mapWritableState } from 'pinia'
import { defineComponent } from 'vue'
import useClipboard from 'vue-clipboard3'

import { modifyTitle } from '@/api/dapi'
import { uploadPdf } from '@/api/oapi'
import emailDialog from '@/components/emailDialog/EmailDialog.vue'
import loginDialog from '@/components/loginDialog/LoginDialog.vue'
import bus from '@/config/bus'
import { checkName, compareVersion, hasElectron, setCookie, trackEvent } from '@/config/utils'
import FLogger from '@/libs/flog'
// @ts-ignore
import Log from '@ccint/log'
import DirStructureModal from '@/pages/file/components/dir-structure-modal/DirStructureModal.vue'
import { TOperation, useFileStore } from '@/pages/file/useFileStore'
import { useUserStore } from '@/stores/user-store'
import { download_file_blob } from '@/api/tools'
import { uploadFile as sapiUpload, queryStorage } from '@/api/sapi'
import { debounce } from 'lodash'
const TAG = 'toolconvert'
import icon_excel from './images/icon_excel.png'
import icon_word from './images/icon_word.png'
import icon_jpg from './images/icon_jpg.png'
import icon_pdf from './images/icon_pdf.png'
import icon_ppt from './images/icon_ppt.png'

// @ts-ignore
import UploadBig from '@/config/uploadBigFile'

export type docSuffix = {
  Word: string
  JPG: string
  Excel: string
  PPT: string
  PDF: string
  [key: string]: string
}
export type keyDocSuffix = keyof docSuffix

export default defineComponent({
  components: {
    DirStructureModal,
    emailDialog,
    loginDialog
  },
  props: {
    sid: {
      type: String,
      default: ''
    },
    downloadType: {
      type: String,
      default: ''
    },
    fileName: {
      type: String,
      default: ''
    },
    downloadLink: {
      type: String,
      default: ''
    },
    eid: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    },
    step: {
      type: String,
      default: 'finish'
    },
    file: {
      type: File,
      default: null
    }
  },
  data() {
    return {
      dirId: '',
      showDrop: false,
      newFileName: '',
      dialogVisible: false,
      isEdit: false,
      emailAccount: '',
      token: '',
      hasOpenShow: false,
      otherHandler: ['wordtopdf', 'exceltopdf', 'ppttopdf'],
      handlerPdf: ['wordtopdf', 'exceltopdf', 'ppttopdf', 'pictopdf', 'mergepdf', 'extractpdfpages', 'rearrangepdfpages', 'rotatepdf', 'deletepagesfrompdf'],
      pdfToOther: ['pdftoword', 'pdftoexcel', 'pdftoppt'],
      newdownloadLink: '',
      toastText: '',
      toastType: '',
      toastShow: false,
      toastDesc: '',
      fileNameTxt: this.$props.fileName,
      parentDocid: (this.$parent as any).docId,
      routeName: '',
      iconMap: {
        Word: icon_word,
        JPG: icon_jpg,
        Excel: icon_excel,
        PPT: icon_ppt,
        PDF: icon_pdf
      } as docSuffix, // iconMap 工具箱文档转换后显示的图标
      docSuffixMap: {
        Word: '.docx',
        JPG: '.jpg',
        Excel: '.xlsx',
        PPT: '.pptx',
        PDF: '.pdf'
      } as docSuffix, // 文档后缀

      uploadBig: '' as any, // 上传大文件实例对象
      isDisabled: false,
      debouncedDownload: debounce(() => {
        this.download(false)
      }, 500),
      routeMap: {
        // 路由map映射
        pdftoword: 'CSPdfToWord',
        pdftoexcel: 'CSPdfToExcel',
        pdftoppt: 'CSPdfToPpt',
        wordtopdf: 'CSWordToPdf',
        exceltopdf: 'CSExcelToPdf',
        ppttopdf: 'CSPptToPdf',
        pdftopic: 'CSPdfToJpg',
        mergepdf: 'CSPdfMerge',
        extractpdfpages:'CSPdfExtract',
        deletepagesfrompdf:'CSPdfDelete',
        rearrangepdfpages:'CSPdfSort',
        rotatepdf:'CSPdfRotate'
      } as {
        [key: string]: string
      },
      TAG:''
    }
  },
  mounted() {
    const self = this
    document.addEventListener('click', function () {
      self.showDrop = false
    })
    bus.on('downloadConvertSuccess', () => {
      self.download(true)
    })
    const routeName = this.$route.name as string
    this.TAG = this.routeMap[routeName]
  },
  beforeUnmount() {
    bus.off('downloadConvertSuccess')
  },
  computed: {
    ...mapWritableState(useFileStore, ['fileDragMode']),
    // 需要展示新的转换成功的按钮页面
    successPdfandJpg() {
      return ['wordtopdf', 'ppttopdf', 'exceltopdf', 'pdftopic', 'pictopdf', 'mergepdf', 'extractpdfpages', 'deletepagesfrompdf', 'rearrangepdfpages', 'rotatepdf']
    }
  },
  created() {
    this.token = useUserStore().token
    this.routeName = (this.$route.name as string) || ''
    if (this.handlerPdf.includes(this.$route.name as string)) {
      this.hasOpenShow = true
    }
    if (this.fileName.lastIndexOf('.') === -1) {
      this.$emit('update:fileName', this.fileName)
    } else {
      this.$emit('update:fileName', this.fileName.substring(0, this.fileName.lastIndexOf('.')))
    }
    this.newdownloadLink = this.downloadLink
    // 登录状态,需要对转换文档保存至用户根目录中
    const routePath = this.$route.path
    // PDF页面合并、排序、删除、提取、旋转，保存，PDF和JPG互转（2个），上述功能，对修改后的输出文档进行保存
    if (this.token && ['mergepdf', 'extractpdfpages', 'rearrangepdfpages', 'rotatepdf', 'deletepagesfrompdf', 'pdftopic', 'pictopdf'].includes(routePath.replace(/^\//, ''))) {
      this.saveDocToUser()
    }
  },
  methods: {
    ...mapActions(useFileStore, ['showDirStructureModal', 'cleanAndReloadDocList','cleanAndReloadDir']),
    handleFileName() {
      const fileName = this.fileNameTxt.split('.')
      return fileName[0] + this.docSuffixMap[this.type as keyDocSuffix]
    },

    /**
     * @method saveDocToUser 对转换后的文档保存至当前用户的我的文档根目录中
     */
    async saveDocToUser() {
      // 文档上传三种类型【图片 和  (pdf ppt word excel) 2种类型】
      // 图片类型使用upload_resouce(且上传原文件，上传pdf在账户内生成的是图片文件，就和转化那边是一样) , 其余类型使用upload_slice
      if (this.type == 'JPG' && (await this.hasStorage(this.file, this.token))) {
        await sapiUpload({
          isDomestic: useUserStore().isDomestic,
          dirId: 'root',
          type: 'pdf',
          token: this.token,
          file: this.file,
          size: this.file.size,
          title: this.file.name,
          id: this.token + new Date().getTime(),
          cancel: ''
        })
      } else if (this.type !== 'JPG') {
        // 获取文档数据流
        const res = await download_file_blob(this.token, this.sid, this.downloadType).then(res => {
          return new File([res], `${this.handleFileName()}`, { type: res.type })
        })
        if (await this.hasStorage(res, this.token)) {
          // 分片传数据
          await this.uploadOfficeFile(res)
        }
      }
      if (this.token !== '') {
        this.cleanAndReloadDocList()
        this.cleanAndReloadDir()
      }
    },
    /**
     * @method uploadOfficeFile 切片上传文档
     * @param file 文件
     */
    uploadOfficeFile(file: any) {
      return new Promise(resolve => {
        this.uploadBig = new UploadBig({
          file: file,
          token: this.token,
          dirId: 'root', // 默认存储到我的文档根目录下
          callback: (res: any) => {
            // 上传成功时的回调函数
            resolve(res)
          },
          drowSpeed(p: number) {
            // 实时更新上传进度99999[-5]
            // console.log('hzh p', p)
          }
        })
        this.uploadBig.startUpload()
      })
    },
    // 查询是否还有多的文件云空间
    async hasStorage(file: File, token: string) {
      // 第一步： 查询当前用户的容量
      const storageData = await queryStorage(token, useUserStore().isDomestic)
      const storageArray = storageData.storage.split('/')
      // 当前用户容量不足，不执行保存
      const currentStorage = parseInt(storageArray[0]) + file.size
      return currentStorage < +storageArray[1]
    },
    showDialog() {
      if (this.token) {
        this.fileDragMode = TOperation.SAVE_FILE
        this.showDirStructureModal()
      } else {
        ;(this.$refs.loginDialog as any).openDialog()
      }
    },
    saveTo(result: { id: string; isNoTips?: boolean }) {
      const self = this
      let fileId = [] as string[]
      const parent = self.$parent as any
      if (this.otherHandler.includes(this.$route.name as string)) {
        fileId = ['10000_add_' + this.$route.meta.convertType + '_' + parent.sid]
      } else if (parent.fileId) {
        fileId = [parent.fileId]
      } else if (parent.fileIds) {
        fileId = parent.fileIds
      }
      if (!result.isNoTips) {
        bus.emit('toast', {
          msg: self.$t('web_3_saving'),
          type: 'progress',
          desc: '',
          total: 1,
          current: 0
        })
      }
      uploadPdf(self.token, self.fileName, self.token + '_' + new Date().getTime(), fileId, result.id)
        .then(res => {
          if (res.error_code === 0) {
            bus.emit('toast', {
              msg: self.$t('cs_save_share_ok'),
              type: 'success'
            })
          } else {
            bus.emit('toast', {
              msg: self.$t('web_3_savefailed')
            })
          }
        })
        .catch(err => {
          bus.emit('toast', {
            msg: self.$t('web_3_savefailed')
          })
          throw err
        })
    },
    checkDrop(event: Event) {
      this.showDrop = !this.showDrop
      event.stopPropagation()
    },
    initData() {
      setCookie('hasReturn', 2, new Date().getTime() + 36000)
      window.location.reload()
    },
    clickRecommend(routerName: string) {
      setCookie('toolsSuccessRouter', JSON.stringify({ oldVal: this.$route.name, newVal: routerName }), new Date().getTime() + 36000)
      this.$emit('update:step', 'finish')
      this.newdownloadLink = encodeURIComponent(this.newdownloadLink)
      setCookie('toolsSuccess', JSON.stringify(this.$props), new Date().getTime() + 36000)
      this.$router.push({ name: routerName })
    },
    emailShare() {
      this.dialogVisible = true
      this.emailAccount = ''
    },
    editFileName() {
      const self = this
      this.newFileName = this.fileName
      self.isEdit = true
      self.$nextTick(() => {
        ;(self.$refs.inputFileName as any).select()
      })
    },
    cancelEdit() {
      this.newFileName = ''
      this.isEdit = false
    },
    download(hasDownload = false) {
      trackEvent(TAG, 'tool_success_download', 'success')
      if (this.isDisabled) return
      const self = this
      this.isDisabled = true
      const t = new Date().getTime()
      if (self.sid && self.downloadType) {
        FLogger.log('toolsDownload', self.newdownloadLink + '&t=' + t)
        Log.action('download')
        if (hasElectron() && window.desktopVersion && compareVersion(window.desktopVersion, '1.0.2') > 0 && !hasDownload) {
          window.ipcRenderer.send('open-savePath', 'convertSuccess', self.fileName)
        } else {
          window.location.href = this.newdownloadLink + '&t=' + t
        }
        setTimeout(() => {
          self.isDisabled = false
        }, 500)
      }
    },
    openShow() {
      trackEvent(TAG, 'tool_success_download', 'success')
      const self = this
      const t = new Date().getTime()
      if (self.sid && self.downloadType) {
        FLogger.log('toolsDownload', this.newdownloadLink + '&t=' + t)
        window.open(this.newdownloadLink + '&t=' + t + '&disposition=inline')
      }
    },
    finishEdit() {
      const self = this
      const checkNameResult = checkName(this.newFileName)
      if (!checkNameResult) {
        this.showToast(self.$t('web_3_rename_special'))
        return false
      }
      const title = this.newFileName
      if (title !== self.fileName) {
        this.$emit('update:fileName', title)
        this.fileNameTxt = this.newFileName
        modifyTitle(self.token, title, self.sid, self.downloadType)
      }
      self.isEdit = false
    },
    showToast(text: string, type?: string, closeTimeout?: number, desc?: string) {
      const self = this
      if (!text) return
      self.toastText = text
      self.toastType = type || ''
      if (desc) self.toastDesc = desc
      self.toastShow = true
      if (type === 'progress') return
      setTimeout(() => {
        self.toastShow = false
        self.toastType = ''
        self.toastText = self.toastDesc = ''
      }, closeTimeout || 2000)
    },
    async copySuccess() {
      const { toClipboard } = useClipboard()
      try {
        await toClipboard(this.newdownloadLink)
        this.$emit('copySuccess')
        trackEvent(TAG, 'tool_success_link', 'success')
        bus.emit('toast', {
          msg: this.$t('web_3_tool_linktip', [this.token ? 7 : 3]),
          type: 'success'
        })
      } catch (e) {
        trackEvent(TAG, 'tool_success_link', 'failed')
        bus.emit('toast', {
          msg: this.$t('web_3_tool_copy_failed')
        })
      }
    }
  }
})
