@import "../../styles/_variables";

.upload_finish_wrap {
  position: relative;
  margin: 20px 40px 0px 40px !important;
  background: #F6FCFB;
  border: 1px solid $gray_middle;
  border-radius: 2px;
  box-sizing: border-box;
  min-height: 392px;
  padding: 48px;
}

.upload_finish_body {
  height: 230px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  .success-info {
    flex: 1;
    display: flex;
    align-items: center;
    flex-direction: column;
  }
  .finish_title {
    font-weight: 500;
    font-size: 28px;
    color:rgba(34, 51, 70, 1);
    text-align: center;
    white-space: pre-line;
    span {
      width: 64px;
      height: 22px;
      font-size: 16px;
      font-weight: 400;
      color: #25C4A4;
      line-height: 22px;
      border-bottom: 1px solid #25C4A4;
      cursor: pointer;
      margin-left: 15px;
    }
  }

  .btn_box {
    display: flex;
    align-items: center;
    margin-top: 24px;
    .el-icon-refresh {
      cursor: pointer;
    }
  }

  .download_doc-wrap {
    margin-top: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-right: 24px;
  }
  .download_btn_tips {
    position: absolute;
    font-size: 14px;
    line-height: 22px;
    text-align: center;
    right: -9%;
    top: -11px;
    width: fit-content;
    padding: 4px 8px 4px 8px;
    height: 22px;
    color:rgba(255, 255, 255, 1);
    background: var(---cs_color_danger, rgba(255, 61, 48, 1));
    border: 1px solid rgba(255, 255, 255, 1);
    border-radius: 4px;
  }
  .download_btn {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 256px;
    max-width: 480px;                 
    height: 60px;
    line-height: 60px;
    border-radius: 4px;
    border: 1px solid #25C4A4;
    padding: 0 48px; 
    position: relative;
    .download_btn_des {
      width: 100%;
      font-size: 20px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    [class^="icon-"],
    [class*=" icon-"] {
      font-size: 22px;
    }
    &.btn_view {
      background: transparent;
      border: 1px solid #25C4A4;
      color: #25C4A4;

      &:hover {
        background: rgba(0, 0, 0, 0.05);
      }
    }

    &.btn_down {
      background: #25C4A4;
      color: #FFFFFF;

      &:hover {
        background: #16A898;
      }
    }
  }
  .disable-btn {
   opacity: 0.5;
    cursor: not-allowed;
  }

  .save_btn {
    display: flex;
    height: 50px;
    background: #19BCAA;
    border-radius: 2px;
    cursor: pointer;
    position: relative;

    span {
      display: inline-block;
      padding: 0 15px;
      height: 50px;
      text-align: center;
      line-height: 50px;
      font-size: 18px;
      font-weight: 400;
      color: #FFFFFF;
    }

    i {
      color: #fff;
      width: 50px;
      height: 50px;
      font-size: 20px;
      line-height: 47px;
      text-align: center;
    }

    i svg {
      height: 1em;
      width: 1em;
    }
  }

  .drop_show {
    i {
      background: #14A797;
      transform: rotate(180deg);
    }
  }

  .drop_list {
    position: absolute;
    top: 55px;
    display: inline-block;
    width: 240px;
    height: 80px;
    box-shadow: 0 1px 3px 0 #CFD8E2;
    border-radius: 2px;

    p {
      background: #FFFFFF;
      height: 40px;
      line-height: 40px;
      font-size: 12px;
      font-weight: 400;
      color: #212121;
      text-indent: 20px;

      &:hover {
        background-color: #e4e6e9;
      }
    }
  }

  .el-icon-check {
    vertical-align: sub;
    font-size: 20px;
    margin-left: 20px;
  }
}

.file_name {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-right: 25px;
  box-sizing: border-box;

  .file_name_text {
    font-size: 14px;
    margin-top: 4px;
    white-space: pre-line; 
    text-align: left;
  }

  .ic_toolbox_doc {
    width: 88px;
    margin-top: 24px;
  }

  .ic_remarks {
    margin-left: 8px;
    cursor: pointer;
  }

  .edit-save {
    display: flex;
    align-items: center;
    margin-left: 10px;
    font-size: 25px;

    i {
      cursor: pointer;

      &:nth-child(1) {
        color: #19BCAA;
      }

      &:nth-child(2) {
        color: #8A8A8A;
        margin-left: 5px;
      }
    }
  }
}

.input_file_name {
  display: block;
  padding: 0 5px;
  height: 24px;
  border-radius: 2px;
  border: 1px solid $gray_middle;
  box-sizing: border-box;
  margin-left: 10px;
  width: 85%;
}

.other_func_wrap {
  display: flex;
  height: 80px;
  border-top: 1px solid $gray_middle;
}

.func_btn {
  flex: 1;
  font-size: 0;
  color: $font_gray_deep;
  line-height: 80px;
  text-align: center;
  cursor: pointer;

  &:hover {
    background: $white;
    box-shadow: 0 2px 15px 0 rgba(46, 103, 87, 0.25);
  }

  span {
    padding: 0 5px;
    font-size: 18px;
    vertical-align: middle;
  }

  [class^="icon-"],
  [class*=" icon-"] {
    font-size: 32px;
  }

  .toolbox_email {
    display: inline-block;
    width: 32px;
    height: 32px;
    background: url("./images/ic_toolbox_mail.png") no-repeat center center;
  }

  .icon-ic_toolbox_word {
    display: inline-block;
    width: 32px;
    height: 32px;
    background: url("./images/word.png") no-repeat center center;
    background-size: contain;
  }

  .icon-ic_toolbox_excel {
    display: inline-block;
    width: 32px;
    height: 32px;
    background: url("./images/excel.png") no-repeat center center;
    background-size: contain;
  }

  .icon-ic_toolbox_merge {
    display: inline-block;
    width: 32px;
    height: 32px;
    background: url("./images/merge.png") no-repeat center center;
    background-size: contain;
  }

  .icon-ic_toolbox_extract {
    display: inline-block;
    width: 32px;
    height: 32px;
    background: url("./images/extract.png") no-repeat center center;
    background-size: contain;
  }
}

.func_btn+.func_btn {
  border-left: 1px solid $gray_middle;
}
