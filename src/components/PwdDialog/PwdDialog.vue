<template>
  <div
    v-if="showDialog"
    class="main_pwd"
  >
    <div
      v-if="!isMobile"
      class="dialog"
      @keydown="handleKeydown"
    >
      <div class="dialog-bg1">
        <p class="title">
          <slot name="title">
            {{ fileInfo.type === 'dir' || fileInfo.isDir ? $t('cs_661_folder_locked_toast') : $t('cs_661_doc_lock') }}
          </slot>
        </p>
        <slot name="desc-text" />
        <div class="input_wrap">
          <form autocomplete="false">
            <input
              ref="input"
              v-model="password"
              class="input"
              :placeholder="fileInfo.type === 'dir' || fileInfo.isDir ? $t('web_3_sign_psw') : $t('cs_670_doc_encrypt_01')"
              type="password"
              autocomplete="new-password"
              @keyup.enter="continuePwd"
            >
          </form>
          <p
            v-if="showTip"
            class="tip_text"
          >
            {{ fileInfo.type === 'dir' || fileInfo.isDir ? $t('web_3_login_wrongpsw') : $t('cs_670_doc_encrypt_02') }}
          </p>
        </div>
        <div class="footer-btn">
          <slot name="more-op-btn" />
          <span
            v-if="hasCancelBtn"
            class="btn cancel"
            @click="cancelPwd"
          >      <slot name="cancel-btn">
            {{ $t('web_3_cancel') }}
          </slot></span>
          <span
            v-if="hasContinueBtn"
            class="btn continue"
            :class="{ 'no_pwd': !password }"
            @click="continuePwd"
          >            <slot name="continue-btn">
            {{ $t('web_3_continuebtn') }}
          </slot></span>
        </div>
      </div>
    </div>
    <div
      v-else
      class="dialog_mobile"
    >
      <div class="dialog_box">
        <p class="guide-text">
          {{ $t('cs_661_doc_lock') }}
        </p>
        <div class="input_box">
          <div class="input_wrap">
            <input
              ref="input2"
              v-model="password"
              class="input"
              :type="inputText"
              :placeholder="$t('cs_670_doc_encrypt_01')"
              @keyup.enter="continuePwd"
            >
            <img
              class="ic_notvisible"
              src="./images/ic_notvisible.png"
              @click="inputText === 'password' ? inputText ='text' : inputText ='password'"
            >
          </div>
        </div>
        <div class="btn_wrap">
          <span
            class="btn btn_cancel"
            @click="cancelPwd"
          >{{ $t('web_3_cancel') }}</span>
          <span
            class="btn btn_continue"
            @click="continuePwd"
          >{{ $t('cs_661_doc_unlock_continue') }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" src="./component.ts"></script>
<style lang="scss" scoped src="./index.scss"></style>
