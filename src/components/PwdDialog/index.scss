ul li {
  list-style: none;
}

.dialog {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 200;
  background: rgba(0, 0, 0, 0.6);
  color: inherit;
  border-radius: 0;
  border: none;

  .dialog-bg1 {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    text-align: center;
    width: 580px;
    height: 210px;
    background: rgba(255, 255, 255, 1);
    border-radius: 4px;
    border: 1px solid rgba(151, 151, 151, 1);
    padding: 0 24px;
    padding-top: 24px;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .title {
      font-size: 20px;
      font-weight: 500;
      text-align: left;
    }

    .input_wrap {
      position: relative;

      .tip_text {
        position: absolute;
        left: 5px;
        bottom: 0px;
        color: #ff3d30;
        font-size: 12px;
      }
    }

    .input {
      width: 93%;
      padding: 14px 16px;
      border-radius: 4px;
      border: 2px solid #19bcaa;
      margin: 24px 0;
      font-size: 16px;
      font-weight: 400;
      display: block;
    }

    .footer-btn {
      display: flex;
      justify-content: flex-end;

      .btn {
        font-size: 17px;
        font-weight: 400;
        border-radius: 4px;
        padding: 10px 60px;
        margin-bottom: 15px;
        cursor: pointer;
        line-height: 21px;
      }

      .cancel {
        border: 1px solid #f1f1f1;
        color: #212121;
        background: #ffffff;
        margin-right: 24px;
      }

      .no_pwd {
        opacity: 0.7;
      }

      .continue {
        color: #ffffff;
        background: #19bcaa;
        border: 1px solid #19bcaa;
      }
    }
  }
}

.dialog_mobile {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9;
  background: rgba(0, 0, 0, 0.6);

  .dialog_box {
    position: absolute;
    bottom: 0;
    text-align: center;
    width: 100vw;
    // height: 300px;
    background: rgb(255, 255, 255);
    border-radius: 8px 8px 0 0;
    .input_wrap {
      display: flex;
      align-items: center;
      background: #f1f1f1;
      border-radius: 4px;
      font-size: 16px;
      color: #9c9c9c;
      margin: 0 auto;
      width: 90%;
    }
    .ic_notvisible {
      width: 20px;
      height: 20px;
      display: inline-table;
      margin-right: 16px;
    }
    .guide-text {
      padding: 16px 0;
      width: 100%;
      height: 20px;
      line-height: 20px;
      font-size: 18px;
      color: #212121;
      text-align: center;
    }
    .input {
      width: 100%;
      padding: 14px 16px;
      border: 0;
      background: transparent;
    }
    .btn_wrap {
      width: 100%;
      display: flex;
      justify-content: space-between;
      padding-bottom: calc(20px + env(safe-area-inset-bottom));
      width: 90%;
      margin: 0 auto;
    }
    .btn {
      font-size: 17px;
      font-weight: 400;
      border-radius: 4px;
      padding: 10px 0;
      width: 47%;
      margin: 15px 0;
    }

    .btn_cancel {
      color: #212121;
      background: #f1f1f1;
    }

    .btn_continue {
      color: #ffffff;
      background: #19bcaa;
    }
  }
}
