import CryptoJS from 'crypto-js'
import { mapState, mapWritableState } from 'pinia'
import { defineComponent } from 'vue'

import { queryPageListWithDoc, type WithDocOption } from '@/api/uapi'
import loginDialog from '@/components/loginDialog/LoginDialog.vue'
import config from '@/config'
import bus from '@/config/bus'
import { encryptCompliance, isMobile } from '@/config/utils'
import FLogger from '@/libs/flog'
import { useCfgStore } from '@/stores/cfg-store'
import { useDirStore } from '@/stores/dir-store'
import { useUserStore } from '@/stores/user-store'

interface fileInfoOption {
  docId?: string
  dirId?: string
  type: string
  password?: string
  verifyCb?: (password: string) => {}
  extra: any
  from?: string
  isDir?: boolean
}

export default defineComponent({
  name: 'PwdDialog',
  components: {
    loginDialog
  },
  props: {
    hasCancelBtn: {
      type: Boolean,
      default: true
    },
    hasContinueBtn: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isMobile: isMobile(),
      inputText: 'password',
      token: useUserStore().token,
      showDialog: false,
      showTip: false,
      fileInfo: {
        type: '',
        docId: '',
        dirId: '',
        password: '',
        isDir: false,
        from: ''
      } as fileInfoOption,
      password: '',
      // 是否通过了
      pass: false,
      docPass: false,
      dirPass: false
    }
  },
  computed: {
    ...mapWritableState(useDirStore, ['dirArray', 'dirMap', 'docDirMap'])
  },
  mounted() {
    // if (useCfgStore().obj.doc_encrypt === 0) {
    //   this.docPass = true
    // }
    // if (useCfgStore().obj.dir_encrypt === 0) {
    //   this.dirPass = true
    // }
  },
  methods: {
    handleKeydown(event: KeyboardEvent) {
      if (event.key === 'Enter') {
        event.preventDefault()
        this.continuePwd()
      }
    },
    init(data: any) {
      console.log('init', data)
      if (useCfgStore().obj.doc_encrypt === 0 && data.type === 'doc') {
        this.pass = true
        console.log('通过了', this.pass)
      }
      if (useCfgStore().obj.dir_encrypt === 0 && data.type === 'dir') {
        this.pass = true
      }
    },
    handlerDialog(isShow: boolean, file: fileInfoOption) {
      console.log('handlerDialog', this.pass)
      FLogger.log('pwdDialog', 'show')
      this.showDialog = isShow
      this.fileInfo = file
      // 提示语是否打开
      this.showTip = this.fileInfo.extra.showTip || false
      this.$nextTick(() => {
        const input = (this.$refs.input as HTMLInputElement) || (this.$refs.input2 as HTMLInputElement)
        input.focus()
        input.select()
      })
    },
    handlerTip(isTip: boolean) {
      this.showTip = isTip
    },
    cancelPwd() {
      console.log('this.fileInfo.', this.fileInfo)
      if (this.fileInfo.from === 'enter_dir') {
        this.$router.push({ path: '/file/manager' })
        this.password = ''
        this.showDialog = false
        this.showTip = false
        this.setPass(false)
        return
      }
      if (this.fileInfo.from === 'detail') {
        this.$router.push({ path: '/file/manager' })
        this.password = ''
        this.showDialog = false
        this.showTip = false
        this.setPass(false)
        return
      }
      this.password = ''
      this.showDialog = false
      this.showTip = false
      this.setPass(false)
      // 取消全部的状态
      this.$emit('cancelAllPwd')
    },
    closeDialog() {
      this.password = ''
      this.showDialog = false
      this.showTip = false
      this.setPass(false)
    },
    setPass(value: boolean) {
      this.pass = value
    },
    continuePwd() {
      if (!this.password) return
      if (this.fileInfo.type === 'pdf_decrypt' && this.fileInfo.verifyCb) {
        // 走pdf自带的解密流程
        this.fileInfo.verifyCb(this.password)
        this.$emit('getPwd', this.password)
        // 直接return
        return
      }
      // let pwd
      const pwd = encryptCompliance(this.password) as string
      const params = {
        md5: this.fileInfo.password,
        pwd: pwd
      } as WithDocOption
      if (this.fileInfo.docId) {
        params.doc_id = this.fileInfo.docId
        params.ttype = 'doc'
      }
      if (this.token) params.token = this.token
      if (this.fileInfo.type === 'dir') params.ttype = 'dir'
      if (this.fileInfo.isDir) params.ttype = 'dir'
      // if (this.fileInfo.)
      queryPageListWithDoc(params).then((res: any) => {
        console.log('res', res)
        // 解密成功
        if (res.data.result === 1) {
          FLogger.log('pwdDialog', 'success')
          this.setPass(true)
          console.log('this.fileInfo', this.fileInfo)
          if (this.fileInfo.from === 'detail') {
            this.$emit('detailOpenDoc', this.fileInfo.extra)
            return
          } else if (this.fileInfo.type === 'doc') {
            this.$emit('continuePwdDoc', this.fileInfo.extra)
          } else if (this.fileInfo.from === 'enter_dir') {
            this.password = ''
            this.showDialog = false
            this.showTip = false
            this.setPass(false)

            this.$nextTick(() => {
              setTimeout(() => {
                useDirStore().setDirMap(this.dirMap)
              }, 500)
              bus.emit('getComputedArrayBefore')
            })

            return
          } else if (this.fileInfo.type === 'dir') {
            this.$emit('continuePwdDir', this.fileInfo.extra)
          } else if (this.fileInfo.type === 'download_item') {
            this.$emit('downloadItem', this.fileInfo.extra)
          } else if (this.fileInfo.type === 'save') {
            this.$emit('saveItem', this.fileInfo.extra)
          } else if (this.fileInfo.type === 'setUnfold') {
            this.$emit('setUnfold', this.fileInfo.extra)
          } else {
            this.$emit('handlerCheckPwd', res.data.result, this.fileInfo.type)
          }
          this.cancelPwd()
        } else {
          FLogger.log('pwdDialog', 'error')
          this.handlerTip(true)
        }
      })
      // if (this.fileInfo.docId) {
      //   pwd = this.md5Salt(this.fileInfo.docId).toLocaleUpperCase()
      //   this.$emit('continuePwdDoc', this.fileInfo.docId, pwd)
      // } else if (this.fileInfo.dirId) {
      //   pwd = this.md5Salt(useUserStore().userId).toLocaleUpperCase()
      //   this.$emit('continuePwdDir', this.fileInfo.dirId, pwd)
      // }
      // console.log('this.docId', this.docId)
      // console.log('this.password', this.password)
      // console.log('pwd', pwd)
    }
    // md5Salt(text: string) {
    //   return CryptoJS.MD5(text + this.password)
    //     .toString()
    //     .substring(0, 32)
    // },
  }
})
