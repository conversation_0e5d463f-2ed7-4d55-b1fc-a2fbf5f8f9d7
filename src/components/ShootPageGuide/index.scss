@import "../../styles/variables";

.bg-layer {
  position: absolute;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  background: rgba(33, 33, 33, .7);
  z-index: 100;
  .guideline {
    font-size: 14px;
    background: #ffffff;
    position: absolute;
    top: 19px;
    left: 480px;
    height: 500px;
    width: 500px;
    z-index: 1000;
    overflow: hidden;
    border-radius: 2px;
  }
  .button{
    font-size: 18px;
    position: absolute;
    top: 19px;
    left: 377px;
    background: #ffffff;
    border-radius: 2px;
  }
  .btn_shoot{
    border-radius: 2px;
    border: 1px solid $green_brand;
    color: $green_brand;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
    img {
      width: 20px;
      height: 20px;
    }
  }
}
