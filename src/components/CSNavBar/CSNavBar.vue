<!-- 组件说明： https://www.notion.so/camscanner/1be4642b05634e4cb9f4f411c2d6f1aa -->
<!-- hide_nav=1&hide_status_bar=1 -->
<template>
  <div class="nav-bar-wrapper" :class="[showTitle ? 'show-title' : '', safeArea ? 'safe-area' : '', isSupportSafeArea ? 'support-safe-area' : '']" :style="navBarWrapperStyle">
    <div class="nav-bar" :class="[backgroundColor ? 'need-set' : '']">
      <div class="btn-back" @click="back">
        <slot name="back">
          <svg class="back-img" :class="[backgroundColor ? 'use-white-default' : '']" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_2753_25590)">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M13.7346 6.67826L14.4129 5.94345L12.9432 4.58691L12.265 5.32173L6.26523 11.8217L5.63916 12.5L6.26523 13.1783L12.265 19.6783L12.9432 20.4131L14.4129 19.0565L13.7346 18.3217L9.20098 13.4101L18.9463 13.4101L19.9463 13.4101L19.9463 11.4101L18.9463 11.4101L9.36695 11.4101L13.7346 6.67826Z" />
            </g>
            <defs>
              <clipPath id="clip0_2753_25590">
                <rect width="24" height="24" />
              </clipPath>
            </defs>
          </svg>
        </slot>
      </div>
      <div class="title" :class="[backgroundColor ? 'use-white-default' : '']" @click="handleTitleClick">
        <slot name="title">{{ title }}</slot>
      </div>
      <div class="btn-menu" @click="handleBtnMenuClick">
        <slot name="btn-menu">{{ btnMenuText }}</slot>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import CSJSAPI from '@/config/CSJSAPI'
import { isSupportSafeArea } from '@/config/utils'

export default defineComponent({
  props: {
    // 标题文字
    title: {
      type: String,
      default() {
        return ''
      }
    },
    // 右侧文字
    btnMenuText: {
      type: String,
      default() {
        return ''
      }
    },
    // 不使用默认的返回功能
    preventBack: {
      type: Boolean,
      default() {
        return false
      }
    },
    height: {
      type: Number,
      default() {
        return 44
      }
    },
    backgroundColor: {
      type: String,
      default() {
        return ''
      }
    },
    safeArea: {
      type: Boolean,
      default() {
        return true
      }
    },
    // true代表永久显示 | false代表初始不显示，向下滚动后显示
    isAlwaysVisible: {
      type: Boolean,
      default() {
        return true
      }
    }
  },
  data() {
    return {
      showTitle: false,
      isSupportSafeArea: isSupportSafeArea()
    }
  },
  computed: {
    navBarWrapperStyle() {
      return {
        '--nav-bar-height': this.height + 'px',
        '--nav-bar-background-color': this.backgroundColor
      }
    }
  },
  mounted() {
    if (this.isAlwaysVisible) {
      this.showTitle = true
    } else {
      window.addEventListener('scroll', this.handleScroll)
    }
  },
  methods: {
    handleScroll() {
      const scrollTop = document.documentElement.scrollTop
      this.showTitle = scrollTop > this.height
    },
    back() {
      if (this.preventBack) {
        this.$emit('backClick')
        return
      }

      // 默认的返回功能
      if (window.jsapi_ready) {
        CSJSAPI.back_view()
      } else {
        history.back()
      }
    },
    handleTitleClick() {
      this.$emit('titleClick')
    },
    handleBtnMenuClick() {
      this.$emit('btnMenuClick')
    }
  },
  unmounted() {
    if (this.isAlwaysVisible) {
      window.removeEventListener('scroll', this.handleScroll)
    }
  }
})
</script>

<style scoped lang="scss">
@import '@/styles/_functions.scss';

.nav-bar-wrapper {
  height: var(--nav-bar-height);

  &.safe-area {
    padding-top: 45px; // android safe-area height
    .nav-bar {
      padding-top: 45px; // android safe-area height
    }

    &.support-safe-area {
      @include safeAreaInsetTop('padding-top', 0);
      .nav-bar {
        @include safeAreaInsetTop('padding-top', 0);
      }
    }
  }

  &.show-title {
    .nav-bar {
      background-color: var(--cs_color_bg_0);
      &.need-set {
        background-color: var(--nav-bar-background-color);
      }
    }

    .title {
      visibility: visible;
    }

    .btn-menu {
      visibility: visible;
    }
  }
}

.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  height: var(--nav-bar-height);
  background-color: rgba(255, 255, 255, 0);
  transition: background-color 0.3s ease;
}

.btn-back {
  width: var(--nav-bar-height);
  height: var(--nav-bar-height);
  position: absolute;
  left: 0;
  bottom: 0;
  cursor: pointer;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  .back-img {
    fill: var(--cs_color_text_4);
    &.use-white-default {
      fill: #212121;
    }
  }
}

.title {
  //   padding: 0 100px;  //防止屏幕太窄，标题文字和右侧文字重叠
  height: var(--nav-bar-height);
  line-height: var(--nav-bar-height);
  font-size: 16px;
  font-weight: 600;
  color: var(--cs_color_text_4);
  &.use-white-default {
    color: #212121;
  }
  min-width: 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: center;
  visibility: hidden;
}

.btn-menu {
  max-width: 75px;
  position: absolute;
  bottom: 0;
  right: 15px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  text-align: center;
  visibility: hidden;
  height: var(--nav-bar-height);
  line-height: 16px;
  font-size: 14px;
  z-index: 10;
}
</style>
