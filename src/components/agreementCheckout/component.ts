import { mapWritableState } from 'pinia'
import { defineComponent } from 'vue'

import config from '@/config'
import { useLoginStore } from '@/pages/user/login/login-store'

interface koCheckoutOption {
  // 是不是14岁
  fourteen: boolean
  // 用户协议
  agreement: boolean
  // 隐私政策
  privacyPolicy: boolean
  // 信息同意收集书
  information: boolean
}

export default defineComponent({
  data() {
    return {
      // 韩国注册勾选看
      koCheckout: {
        // 是不是14岁
        fourteen: false,
        // 用户协议
        agreement: false,
        // 隐私政策
        privacyPolicy: false,
        // 信息同意收集书
        information: false
      }
    }
  },
  computed: {
    ...mapWritableState(useLoginStore, ['registerChecked']),
    v3Host() {
      return config.macro.V3_MAIN_DOMAIN
    },
    v4Host() {
      return config.macro.CAMSCANNER_QRCODE_HOST
    }
  },
  watch: {
    koCheckout: {
      immediate: true,
      handler(newValue: koCheckoutOption) {
        if (newValue.fourteen && newValue.agreement && newValue.privacyPolicy && newValue.information) {
          this.registerChecked = true
        } else {
          this.registerChecked = false
        }
      },
      deep: true
    }
  },
  created() {},
  methods: {}
})
