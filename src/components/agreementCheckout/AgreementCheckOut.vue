<template>
  <div class="main agreementCheckout">
    <el-checkbox v-model="koCheckout.fourteen">
      <span class="privacy-text">{{ $t('cs_620_korea_16') }}</span>
    </el-checkbox>
    <el-checkbox v-model="koCheckout.agreement">
      <span class="privacy-text" v-html="$t('cs_620_korea_02', [`<a target=\'_blank\' href='${v3Host}/app/service?language=${$i18n.locale}'>${$t('web_3_sign_service')}</a>`])"></span>
    </el-checkbox>
    <el-checkbox v-model="koCheckout.privacyPolicy">
      <span class="privacy-text" v-html="$t('cs_620_korea_02', [`<a target=\'_blank\' href='${v3Host}/web/privacy?language=${$i18n.locale}'>${$t('web_3_sign_privacy')}</a>`])"></span>
    </el-checkbox>
    <el-checkbox v-model="koCheckout.information">
      <span class="privacy-text" v-html="$t('cs_620_korea_02', [`<a target=\'_blank\' href='/informedConsent'>${$t('cs_620_korea_01')}</a>`])"></span>
    </el-checkbox>
  </div>
</template>
<script lang="ts" src="./component.ts"></script>
<style lang="scss" scoped src="./index.scss"></style>
<style lang="scss">
.agreementCheckout {
  a {
    font-size: 14px;
    color: #212121;
    border-bottom: 1px solid #a5a5a5;
  }
}
</style>
