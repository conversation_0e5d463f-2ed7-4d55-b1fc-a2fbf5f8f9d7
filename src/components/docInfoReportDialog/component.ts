import { defineComponent } from 'vue'

export default defineComponent({
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      reason: ''
    }
  },
  methods: {
    chooseReason(reason: string) {
      this.reason = reason
    },
    commit() {
      if (!this.reason) {
        this.$emit('cancel')
        return
      }
      this.$emit('commit', this.reason)
    },
    cancel() {
      this.$emit('cancel')
    }
  }
})
