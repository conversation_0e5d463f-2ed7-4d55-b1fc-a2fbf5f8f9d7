<template>
  <div class="mask" v-if="show" @click.stop="">
    <div class="dlg_report_wrap">
      <div class="close" @click="cancel">
        <span class="icon-ic_tag_rename_cancel"></span>
      </div>
      <div class="report_title">举报</div>
      <div class="report_why">为什么举报这个文件？</div>
      <div class="reason_wrap">
        <div class="reason_item" :class="[reason === '淫秽色情' ? 'selected' : '']" @click="chooseReason('淫秽色情')">淫秽色情</div>
        <div class="reason_item" :class="[reason === '血腥暴力' ? 'selected' : '']" @click="chooseReason('血腥暴力')">血腥暴力</div>
      </div>
      <div class="reason_wrap">
        <div class="reason_item" :class="[reason === '政治有害' ? 'selected' : '']" @click="chooseReason('政治有害')">政治有害</div>
        <div class="reason_item" :class="[reason === '泄露商业机密' ? 'selected' : '']" @click="chooseReason('泄露商业机密')">泄露商业机密</div>
      </div>
      <div class="reason_wrap">
        <div class="reason_item" :class="[reason === '侵犯个人隐私' ? 'selected' : '']" @click="chooseReason('侵犯个人隐私')">侵犯个人隐私</div>
        <div class="reason_item" :class="[reason === '违反其他法律法规' ? 'selected' : '']" @click="chooseReason('违反其他法律法规')">违反其他法律法规</div>
      </div>
      <div class="report_btn_wrap">
        <div class="btn_green" @click="commit"><span>提交</span></div>
        <div class="btn_gray" @click="cancel"><span>取消</span></div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped src="./index.scss"></style>
<script lang="ts" src="./component.ts"></script>
