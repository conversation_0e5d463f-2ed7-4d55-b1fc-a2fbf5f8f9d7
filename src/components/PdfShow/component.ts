import { defineComponent } from 'vue'

import { getDeviceId, md5Salt, getSign } from '@/config/utils'


import * as PDFJS from "pdfjs-dist2.16/legacy/build/pdf"
// @ts-ignore
PDFJS.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.16.105/pdf.worker.js'

import SliceDownload from '@/config/sliceDownload'
import logoLoading from '../logoLoading/LoadingView.vue'


export default defineComponent({
  components: {
    logoLoading
  },
  props: {
    shareInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
    eids: {
      type: String,
      default: ''
    },
    dids: {
      type: String,
      default: ''
    },
    sids: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      pdfPages: 0, // pdf文件的页数
      pdfScale: 1.0, // 缩放比例
      encrypt_id: '',
      device_id: '',
      pdfData: null as any, // 切片下载的所有数据
    }
  },
  setup() {
    return {
      pdfDoc: null as any, // 保存加载的pdf文件流
    }
  },

  watch: {
    shareInfo: {
      async handler(value) {
        this.pdfPages = 0
        const shareInfo = value
        if (shareInfo && shareInfo.docId) {
          this.encrypt_id = (this.$route.query.encrypt_id as string) || this.eids
          this.device_id = (this.$route.query.device_id as string) || this.dids || (await getDeviceId() as string)
          const file_id = shareInfo.docId.replace('.jdoc', '')
          this.getPdfDocInfo(file_id, this.sids, this.encrypt_id, this.device_id)
        }
      },
      immediate: true,
      deep: true
    }

  },

  methods: {
    getPdfDocInfo (file_id: string, sid: string = '', encrypt_id: string = '', device_id: string = '') {
      // 多格式文档下载，切片下载
      const { loadFile } = this
      const params = {
        file_id,
        sid,
        dir: 1,
      }

      // params.sign = md5Salt(getSign(params))
      let extraParams:any = {}
      if (encrypt_id) {
        extraParams = {...params, encrypt_id}
      } else {
        extraParams = {...params, device_id}
        extraParams.sign =  md5Salt(getSign(extraParams))
      }
      this.pdfData = new SliceDownload({

        checkFileStateParams: {...params, ...extraParams},
        downloadSliceParams: {...params, ...extraParams},
        file_id,
        returnUrl: true,
        callback (res: any) {
          if (res === false) {
          } else {
            loadFile(res)
            console.log(res)
          }
        }
      })
      this.pdfData.startDownload()
    },


    loadFile (url: string) {
      //@ts-ignore
      const loadingTask = PDFJS.getDocument(url)
      loadingTask.promise.then((pdf:any) => {
        this.pdfDoc = pdf
        this.pdfPages = pdf.numPages
        this.renderPage(1) // 表示渲染第 1 页
      }).catch((error:any) => {
        console.log(error)
      })
    },
    renderPage (num:number) {
      this.pdfDoc.getPage(num).then((page:any) => {
        const canvasId = 'pdf-canvas-' + num // 第num个canvas画布的id

        const canvas: any = document.getElementById(canvasId)
        const ctx: any = canvas.getContext('2d')

        // const dpr = window.devicePixelRatio || 1
        // const bsr = ctx.webkitBackingStorePixelRatio ||
        //             ctx.mozBackingStorePixelRatio ||
        //             ctx.msBackingStorePixelRatio ||
        //             ctx.oBackingStorePixelRatio ||
        //             ctx.backingStorePixelRatio ||
        //             1
        // const ratio = dpr / bsr
        const ratio = 1
        const viewport = page.getViewport({ scale: 1.0 })
        console.log(ratio);
        
        canvas.width = viewport.width * ratio
        canvas.height = viewport.height * ratio
        // canvas.style.width = viewport.width + 'px'
        // canvas.style.height = viewport.height + 'px'

        ctx!.setTransform(ratio, 0, 0, ratio, 0, 0)

        const renderContext = {
          canvasContext: ctx,
          viewport
        }
        page.render(renderContext)

        // 在第num页渲染完毕后，递归调用renderPage方法，去渲染下一页，直到所有页面渲染完毕为止
        if (num < this.pdfPages) {
          this.renderPage(num + 1)
        }
      })
    },

    download() {
      this.pdfData.downloadFile(this.shareInfo && this.shareInfo.title)
    }
  }

})
