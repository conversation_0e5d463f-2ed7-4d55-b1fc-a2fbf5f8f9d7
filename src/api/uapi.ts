import config from '@/config'
import { createNonceStr, emailRegex, encryptCompliance, getBrowserType } from '@/config/utils'
import { getSign, md5Salt } from '@/config/utils'
import { useLoginStore } from '@/pages/user/login/login-store'

import createRequest from '../libs/request'

export function logout(token: string) {
  return createRequest('UAPI').get('/logout', {
    params: {
      token: token
    }
  })
}

export function queryUserInfo(token: string) {
  let api: 'UAPI_DOMESTIC' | 'UAPI_FOREIGN' | 'UAPI_DEFAULT' = 'UAPI_DEFAULT'
  if (token.length === 26 || token.length === 28) {
    const from = token.substr(-2)
    if (from === '01') {
      api = 'UAPI_DOMESTIC'
    } else if (from === '02') {
      api = 'UAPI_FOREIGN'
    }
  }
  return createRequest(api).get<never, LoginSuccessOption>('/user_info2', {
    params: {
      token: token,
      client_app: 'web_' + config.appName + '@' + config.appVersion
    }
  })
}

export interface SendVcodeOption {
  language: string
  timestamp: number
  sign_type?: string
  email?: string
  reason?: string
  area_code?: string
  sign?: string
  mobile?: string
}

export interface SendVcodeResult {
  success: number
  gt: string
  challenge: string
  token?: string
  token_pwd?: string
  sms_token?: string
}

// 校验发送验证码
export function sendVcode(data: SendVcodeOption) {
  return createRequest('UAPI_WAF').get<
    never,
    {
      ret: number
      data: SendVcodeResult
    }
  >('/send_sms_vcode', {
    params: data
  })
}

interface QueryEmailOption {
  email?: string
  mobile?: string
  account?: string
  area_code?: string
  emailEncrypt?: string
  mobileEncrypt?: string
}

// 查询账户是否注册
export function queryAccount() {
  const objData: QueryEmailOption = {}
  const account = useLoginStore().account
  const areaCode = useLoginStore().areaCode
  if (emailRegex().test(account)) {
    objData.emailEncrypt = encryptCompliance(account)
  } else {
    objData.mobileEncrypt = encryptCompliance(account)
    objData.area_code = areaCode
  }
  return createRequest('UAPI').get<
    never,
    {
      ret?: number | string
      msg: string
    }
  >('/query', {
    params: Object.assign({ client_app: 'web_' + config.appName + '@' + config.appVersion }, objData)
  })
}

export interface LoginSuccessOption {
  ret: number
  err: string
  data: {
    sms_token: string
    challenge: string
    gt: string
    is_repeat_checkable: number
    new_captcha: number
    success: number
  }
  user: {
    uid: string
    gid: number
    expire: number
  }
  token: {
    token: string
    expire: number
    token_pwd?: string
  }
  profile: {
    email: string
    displat_name: string
    mobile: string
    area_code: string
    // edu_auth: EduAccountResult
    // edu_af: EduAccountResult
  }
}

// export interface LoginCResult {
//   ret: number
//   xerrcode: string
//   profile?: {
//     edu_auth: EduAccountResult
//     edu_af: EduAccountResult
//   }
//   token?: {
//     token?: string
//     token_pwd?: string
//   }
//   data: {
//     success: number
//     token?: string
//     token_pwd?: string
//     gt: string
//     challenge: string
//     sms_token?: string
//   }
// }

export interface EduAccountResult {
  account: string // 认证的edu 邮箱
  auth_time: string // 认证时间戳
  expiry: string // 过期时间戳
  edu_from?: string // 认证来源  edu_us: 美国edu拉新
}

// 校验登录
export function loginC(data: VerifySmsVcodeOption) {
  return createRequest('UAPI_WAF').get<never, LoginSuccessOption>('/login_c', {
    params: data
  })
}

export interface UpdateTokenByTokenpwdOption {
  email?: string
  mobile?: string
  account?: string
  client_id: string
  token_pwd: string
  account_type?: string
}

export function updateTokenByTokenpwd(data: UpdateTokenByTokenpwdOption) {
  const objData = JSON.parse(JSON.stringify(data))
  if (objData.email) {
    objData.emailEncrypt = encryptCompliance(objData.email)
    delete objData.email
  } else if (objData.mobile) {
    objData.mobileEncrypt = encryptCompliance(objData.mobile)
    delete objData.mobile
  } else if (objData.account) {
    objData.accountEncrypt = encryptCompliance(objData.account)
    delete objData.account
  }
  return createRequest('UAPI').get<
    never,
    {
      ret: number
      login_token: string
    }
  >('/update_token_by_tokenpwd', {
    params: Object.assign(objData, {
      client_app: 'web_' + config.appName + '@' + config.appVersion,
      client: getBrowserType()
    })
  })
}

export interface VerifySmsVcodeOption {
  vcode?: string
  reason?: string
  timestamp?: number
  sign_type?: string
  client?: string
  client_app?: string
  client_id?: string
  sign?: string
  email?: string
  mobile?: string
  area_code?: string
  user?: string
  password?: string
  rememberme?: number
  type?: string
  secret?: string
  token_life?: number
  auto_login?: number
  jy_email?: string
  jy_mobile?: string
  [key: string]: string | number | undefined
}

// 验证码验证
export function verifySmsVcode(data: VerifySmsVcodeOption) {
  return createRequest('UAPI_WAF').get<never, LoginSuccessOption>('/verify_sms_vcode', {
    params: data
  })
}

export interface RegisterEmailOption {
  email: string
  password: string
  language: string
  from: string
  app_name: string
  jy_email: string
  sign?: string
  sign_type?: string
}

// 校验发送邮箱验证码
export function registerEmail(data: RegisterEmailOption) {
  return createRequest('UAPI_WAF').get<
    never,
    {
      ret: number
      data: SendVcodeResult
    }
  >('/reg_email', {
    params: data
  })
}

export interface SetPasswordOption {
  sms_token: any
  new_pass: string
  sign?: string
  sign_type?: string
}

// 修改密码
export function setPassword(data: SetPasswordOption) {
  return createRequest('UAPI_WAF').get<
    never,
    {
      ret: number
      data: SendVcodeResult
    }
  >('/set_password', {
    params: data
  })
}

interface UploadFeedbackOption {
  token: string
  email: string
  platform: string
  content: string
}

export function uploadFeedback(data: UploadFeedbackOption) {
  return createRequest('OLD_WEB_API_USER').post<
    never,
    {
      errno?: string | number
    }
  >('/uploadFeedback', data)
}

// ==================== 企业版 历史遗留的迁移相关 ====================
export interface CropCheckOption {
  account: string
  area_code?: number
  name?: string
}

export interface CropCheckResult {
  ret: number
  data: {
    token: string
    MAPI: string
    OAPI: string
    PAPI: string
    SAPI: string
    TAPI: string
    UAPI: string
    EAPI: string
    WEBEAPI: string
    WSAPI: string
    MIGRATE: string
  }
}

// 查询迁移是否完成
export function cropCheck(action: string, params: { token: string }, data: CropCheckOption) {
  return createRequest('UAPI').post<never, CropCheckResult>(`/corp/manager/${action}`, data, {
    params: params
  })
}

interface CorpActionOption {
  corp_id?: string
  token: string
}

export interface CorpActionResult {
  total: number
  members_count: number
  expire_time: number
  corp_name?: string
  corp_id?: string
}

export function corpAction(action: string, params: CorpActionOption, data?: any) {
  return createRequest('UAPI').post<
    never,
    {
      ret: number
      data: CorpActionResult
    }
  >(`/corp/${action}`, data, {
    params: params
  })
}
// ==================== 企业版 历史遗留的迁移相关 ====================

export interface GetUserAttributeOptions {
  app_name?: string
  token: string
  attribute: string
  cs_ept_d?: string
  device_id?: any
}

interface GetUserAttributeResult {
  dir_pwd?: string
  web_ocr_lang: string
  write_pad_image_type?: string | number
  write_pad_main_tool_tip?: string | number
  write_pad_share_tips?: string | number
  edu_auth_cn_v2?: {
    auth_type: string
    status: number
  }
}

export function getUserAttribute(data: GetUserAttributeOptions) {
  return createRequest('UAPI').get<
    never,
    {
      ret: number
      data: GetUserAttributeResult
      err: string
    }
  >('/web/get_user_attribute', {
    params: data
  })
}

interface SetUserAttributeOptions {
  token: string
  app_name: string
  attribute: string
  value: string
}

export function setUserAttribute(data: SetUserAttributeOptions) {
  return createRequest('UAPI').get('/web/set_user_attribute', {
    params: data
  })
}

export interface ConfigInterfaceResult {
  appId: string
  timestamp: string
  nonceStr: string
  signature: string
}

export function configInterface(data: { url: string }) {
  return createRequest('UAPI').get<
    never,
    {
      ret: string
      status: string
      errcode: string
      message: string
      data: ConfigInterfaceResult
    }
  >('/wx/config_interface', {
    params: data
  })
}

interface AddVipGiftData {
  gift_name: string
  token: string
  client_app: string
  act_id: string
  invite_token: string
}

// 查询 奖品下发状况
export function addVipGift(data: AddVipGiftData) {
  return createRequest('OLD_WEB_USER').post('/app/addVipGift', Object.assign({}, data, { api_domain: config.api['PAPI'] }))
}

export interface WxOauthInfoOption {
  code: string
  appid: string
}

export function wxOauthInfo(data: WxOauthInfoOption) {
  return createRequest('UAPI').get<
    never,
    {
      data: {
        unionid: string
      }
    }
  >('/wx/oauth', {
    params: data
  })
}

export interface WxLoginOption {
  unionid: string
  device_id: string
  client: string
  client_app: string
  client_id: string
  language: string
}

export interface WxLoginResult {
  token: string
}

export function wxLogin(data: WxLoginOption) {
  return createRequest('UAPI').get<
    never,
    {
      ret: number
      data: WxLoginResult
    }
  >('/wx/login_v2', {
    params: data
  })
}

// ============= 660企业版 =============
// https://web-api.intsig.net/project/2918/interface/api/245892
interface CorpLoginOption {
  token: string // 当前登入的token
  corp_id: string // 企业id
  corp_encrypt_id: string // 需要登录的企业账号加密user_id
  // token_life: number
  // client_app: string
  // client_id: string
  // sign: string
}

interface CorpLoginResult {
  ret: string | number
  err: string
  data: {
    token: {
      token: string // 企业账号token
      expire: number // 企业token过期时间 unix时间戳
    }
    user: {
      uid: string // 企业账号uid
      encrypt_id: string // 企业账号加密uid，暂不需要
      gid: string // 问了后台用来判断国内外，后台写死 1000000
    }
    apis: {
      MAPI: string
      OAPI: string
      PAPI: string
      SAPI: string
      TAPI: string
      UAPI: string
      DAPI: string
      WEBEAPI: string
      WSAPI: string
      MIGRATE: string
      [key: string]: string
    }
    //还有一些额外的信息，暂时不需要
  }
}

// 获取企业账号token、apis
export function corpLogin(loginParams: CorpLoginOption) {
  const salt = config.env === 'test' || config.env === 'dev' ? 'intsig_v2_fa3d26bd4887afdc' : 'intsig_v2_84ee85cdaaaf1867'
  const needSignParams = {
    ...loginParams,
    token_life: 604800, //默认7天
    client_app: 'web_' + config.appName + '@' + config.appVersion, // 我不知道啊
    client_id: createNonceStr()
  }
  const sign = md5Salt(getSign(needSignParams), salt)
  const params = { ...needSignParams, sign }

  return createRequest('CORP_TOKEN_API').get<never, CorpLoginResult>('/user/account/corp/login', { params })
}

type TCompanyInfoParams = {
  token: string
  corp_id: string
}

export type TCompanyInfo = {
  ret: number
  err: string
  data: {
    corp_name: string //企业名称
    corp_id: string //企业ID
    present_num: number //当前用户数
    member_count: number //可加入用户总数
    corp_logo: string //企业logo
    relevance_encrypt_id: string //企业所属者
    creat_time: number //过期时间
    expire_time: number // 过期时间
    description: string //公司简介
    is_auto_inform?: number //公司简介
  }
}

export type TCommonResoponse = {
  ret: number
  err: string
}
// 获取企业信息
export function getCompanyInfo(params: TCompanyInfoParams) {
  return createRequest('UAPI').get<never, TCompanyInfo>('/corp/info', { params })
}

// 更新企业信息
export function updateCompanyInfo(params: TCompanyInfoParams, data: Partial<TCompanyInfo['data']>) {
  return createRequest('UAPI').post<never, { ret: number; err: string }>('corp/manager/update', data, { params })
}

export function getPicList(params: { token: string }) {
  return createRequest('UAPI').get<never, { ret: number; err: string; data: { pic_list: string[] } }>('/corp/member/get_pic_list', { params })
}

export interface TInviteLink extends TCommonResoponse {
  data: { origin_url: string; short_url: string } // 邀请链接
}

// 获取邀请链接
export function getInviteLink(params: TCompanyInfoParams, data: { url: string; name: string; company_name: string; corp_id: string; corp_logo: string, dept_id: string, dept_name: string, op_user: string }) {
  return createRequest('UAPI').post<never, TInviteLink>('/corp/member/get_invite_link', data, { params })
}

export type TPersonalInfo = {
  ret: number
  err: string
  data: {
    name_remark: string
    profile_pic: string
    role: string
    current_encrypt_id: string
    corp_user_list: {
      corp_encrypt_id: string
      corp_user_type: number
      name_remark: string
    }[]
  }
}
export function getPersonalInfo(params: { token: string; corp_id: string }) {
  return createRequest('UAPI_DOMESTIC').get<never, TPersonalInfo>('/corp/member/info', { params })
}

export function deleteCorpMember(params: { token: string; corp_id: string; encrypt_id: string }) {
  return createRequest('UAPI_DOMESTIC').get<never, TCommonResoponse>('/corp/member/delete', { params })
}

export type TApplyParams = {
  token: string
  invite_sign: string
  corp_id: string
}

export interface TApplyResult extends TCommonResoponse {
  data: string
  info: {
    admin_name: string // 管理员
    admin_account: string
  } // 邀请链接
}

// 员工申请加入企业
export function applyCompany(data: { name_remark: string; user_pic: string; account: string }, params: TApplyParams) {
  return createRequest('UAPI').post<never, TApplyResult>('/corp/member/apply', data, { params })
}

// 上传图片
export function uploadPic(params: { token: string }, data: Blob) {
  return createRequest('UAPI').post<never, { ret: number; data: { url: string } }>('/corp/manager/upload_pic', data, { params })
}

// 获取企业成员列表 https://web-api.intsig.net/project/2918/interface/api/242327
export interface CorpMember {
  role: '001' | '002' //权限 '001':管理员 '002':普通成员
  account: string //账号
  area_code: string //账号分区编号
  name_remark: string //姓名
  profile_pic: string //头像链接
  status: 1 | 2 | -1 //状态 1:正常 2:停用 -1:移除（不显示）
  encrypt_id: string //加密用户id
  dept_id: string
}

export function getCorpMemberList(params: { token: string; corp_id: string, account?: string, name_remark?: string, dept_id?: string }) {
  return createRequest('UAPI').get<never, { ret: number; err: string; data: CorpMember[] }>('/corp/member/list', { params })
}

// 修改员工姓名 https://web-api.intsig.net/project/2918/interface/api/223218
type CorpMemberEditParams = {
  token: string // 当前登录用户的token
  encrypt_id: string // 被编辑员工的加密id
  corp_id: string
}

export function updateCorpMemberName(data: { name_remark: string }, params: CorpMemberEditParams) {
  return createRequest('UAPI').post<never, TCommonResoponse>('corp/member/edit', data, { params })
}

// 暂不移交文档，停用员工 https://web-api.intsig.net/project/2918/interface/api/223218
export function disableMember(params: CorpMemberEditParams, data: { status: number; dept_id?: string } = { status: 2 }) {
  return createRequest('UAPI').post<never, TCommonResoponse>('corp/member/edit', data, { params })
}

// 获取删除企业列表
export type CorpWhiteMemberList = {
  ret: number
  err: string
  data: {
    corp_list: Array<string>
  }
}

export function getCorpWhiteMemberList(params: { token: string }) {
  return createRequest('UAPI').get<never, CorpWhiteMemberList>('corp/member/corp_del_list', { params })
}

// 获取企业申请列表 https://web-api.intsig.net/project/2918/interface/api/242391
export type ApplyMember = {
  apply_time: number //申请时间
  account: string //账号
  name_remark: string //姓名
  user_pic: string //头像链接
  encrypt_user_id: string //加密用户id
  dept_id: string
  dept_name: string
  op_user: string
  apply_status: 1 | 2 | 3 // 申请状态；1:未审批。2审批通过。3:已拒绝
}

export function getApplyList(params: { token: string; corp_id: string }) {
  return createRequest('UAPI').get<never, { ret: number; err: string; data: ApplyMember[] }>('/corp/member/apply_list', { params })
}

// 同意/拒绝申请 https://web-api.intsig.net/project/2918/interface/api/242399
type ApplyList = Array<{
  encrypt_user_id: string
  apply_status: number // 2:通过 3:拒绝
}>

export function submitMemberApproval(params: { token: string; corp_id: string }, data: { apply_list: ApplyList }) {
  return createRequest('UAPI').post<never, TCommonResoponse>('/corp/member/approval', data, { params })
}

// 移交管理员 https://web-api.intsig.net/project/2918/interface/api/249926
export function handoverAdmin(params: { token: string; corp_id: string; encrypt_id: string }) {
  return createRequest('UAPI').get<never, TCommonResoponse>('/corp/member//set_admin', { params })
}

// 移交文档 https://web-api.intsig.net/project/2918/interface/api/249952
export type HandoverDocParams = {
  token: string
  corp_id: string
  encrypt_id: string // 接收人 corp_user_id
  remove_encrypt_id: string // 移除的员工的加密id，通过用户列表中的encrypt_id字段拿到
  op_user: string // 操作人的名称或账号
  dept_id?: string
}

export function handoverDoc(params: HandoverDocParams) {
  return createRequest('UAPI').get<never, TCommonResoponse>('/corp/member/transfer', { params })
}

export interface WithDocOption {
  token?: string
  doc_id?: string
  pwd: string
  md5?: string
  ttype?: 'dir' | 'doc'
}
// 用于获取文档页列表，加密文档解密
export function queryPageListWithDoc(params: WithDocOption) {
  return createRequest('UAPI').get<
    never,
    {
      ret: number
      data: {
        result: number
      }
    }
  >('/check_pwd', { params })
}

export function getSpellLottery(params: any) {
  return createRequest('UAPI').get<
    never,
    {
      ret: number
      err: string
      data: any
    }
  >('/activity/lottery', { params })
}

/**
 * 检查dir密码
 * @member token - token
 * @member pwd - md5加密密码
 * @member ttype - dir
 * * */
export interface TCheckDirPassword {
  token: string
  pwd: string
  ttype: 'dir'
}

/**
 * 检查dir密码返回结果result: 0 失败 1成功
 * @member result - token
 * @member pwd - md5加密密码
 */
export interface TCheckDirPasswordResponse {
  ret: number
  msg: string
  data: {
    result: 0 | 1
  }
}

/**
 * 检查dir password
 * @param params {TCheckDirPassword} 校验密码参数
 * @return {TCheckDirPasswordResponse} 校验密码返回
 */
export function checkDirPassword(params: TCheckDirPassword): Promise<TCheckDirPasswordResponse> {
  return createRequest('UAPI').get('/check_pwd', { params })
}

/**
 * 企业设置列表返回数据接口
 */
export interface SettingsListResponse {
  err: string
  ret: number
  data: {
    is_show_sdir_type_all: number
    sdir_type_all_last_modify_time: number
    is_auto_notify: number // 是否开启自动提醒 0-关闭 1-开启
    is_inform_use: number // 是否开启自动通知未使用成员 0-关闭 1-开启
  }
}

/**
 * 更新企业设置返回数据接口
 */
export interface SettingsUpdateResponse {
  err: string
  ret: number
}

/**
 * 获取企业设置列表
 * @param token 用户token
 * @param corp_id 企业ID
 */
export function getSettingsList(token: string, corp_id: string) {
  return createRequest('UAPI').get<
    never,
    SettingsListResponse
  >('/corp/setting/list', {
    params: {
      token,
      corp_id
    }
  })
}

/**
 * 更新企业设置
 * @param token 用户token
 * @param corp_id 企业ID
 * @param data 设置数据对象
 */
export function updateSettings(token: string, corp_id: string, data: { is_show_sdir_type_all?: number; is_auto_notify?: number }) {
  return createRequest('UAPI').post<
    { is_show_sdir_type_all?: number; is_auto_notify?: number },
    SettingsUpdateResponse
  >('/corp/setting/update',
    data,
    {
      params: {
        token,
        corp_id
      }
    }
  )
}

/**
 * 自动通知未使用成员接口返回数据
 */
export interface InformUseResponse {
  ret: number
  err: string
}

/**
 * 获取自动通知设置
 * @param token 用户token
 * @param corp_id 企业ID
 */
export function getInformUse(token: string, corp_id: string) {
  return createRequest('UAPI').get<
    never,
    { ret: number; err: string; data: { is_inform_use: number } }
  >('/corp/setting/inform_use', {
    params: {
      token,
      corp_id
    }
  })
}

/**
 * 更新自动通知设置
 * @param token 用户token
 * @param corp_id 企业ID
 * @param is_inform_use 是否开启自动通知 0-关闭 1-开启
 */
export function updateInformUse(token: string, corp_id: string, is_inform_use: number) {
  return createRequest('UAPI').get<
    never,
    InformUseResponse
  >('/corp/setting/inform_use', {
    params: {
      token,
      corp_id,
      is_inform_use
    }
  })
}


