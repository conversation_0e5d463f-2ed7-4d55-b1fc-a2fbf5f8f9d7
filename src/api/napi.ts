import createRequest from '../libs/request'
import { encryptCompliance } from '@/config/utils'

const oldCsInstance = createRequest('OLD_WEB_API_USER')
const oldInstance = createRequest('OLD_WEB_USER')

interface CreateOrderOption {
  product_id: string
  payway: string
  token: string
  language: string
  api_domain: any
  email?: string
  mobile?: string
  account?: string
}

// 创建高级用户订单
export function createOrder(data: CreateOrderOption) {
  const objData = JSON.parse(JSON.stringify(data))
  if (objData.email) {
    objData.emailEncrypt = encryptCompliance(objData.email)
    delete objData.email
  } else if (objData.mobile) {
    objData.mobileEncrypt = encryptCompliance(objData.mobile)
    delete objData.mobile
  } else if (objData.account) {
    objData.accountEncrypt = encryptCompliance(objData.account)
    delete objData.account
  }
  return oldCsInstance.post<
    never,
    {
      data?: {
        pay_param: string
        pay_url: string
        pay_postform: string
        code_url: string
        uniq_id: string
      }
    }
  >('/createOrder', objData)
}

interface CheckOrderOption {
  api_domain?: any
  uniq_id: string
  email?: string
  mobile?: string
  account?: string
}

// 检测微信支付订单是否支付
export function checkOrder(data: CheckOrderOption) {
  const objData = JSON.parse(JSON.stringify(data))
  if (objData.email) {
    objData.emailEncrypt = encryptCompliance(objData.email)
    delete objData.email
  } else if (objData.mobile) {
    objData.mobileEncrypt = encryptCompliance(objData.mobile)
    delete objData.mobile
  } else if (objData.account) {
    objData.accountEncrypt = encryptCompliance(objData.account)
    delete objData.account
  }
  return oldCsInstance({
    method: 'post',
    url: '/queryOrder',
    data: objData
  })
}

export interface CreateTeamOrderOption {
  language: string
  num: number
  method: string
  token: any
  title: string
  tel: string
  payway: string
  product_id: string
  team_token?: string
}

// 创建团队版订单
export function createTeamOrder(data: CreateTeamOrderOption) {
  return oldCsInstance({
    method: 'post',
    url: '/createTeamOrder',
    data
  })
}

interface CorpPayOption {
  api_domain: string
  token: string
  product_id: string
  currency: string
  method: string
  app_id: string
  title: string
  num: number
  client: string
  client_app: string
  product_name: string
  product_desc: string
  payway: string
  return_url: string
  corp_id?: string
}

interface CorpPayResult {
  data?: {
    pay_param?: string
    pay_url?: string
    pay_postform?: string
    code_url?: string
    uniq_id: string
  }
}

// 创建企业订单
export function corpPay(data: CorpPayOption) {
  return oldInstance.post<never, CorpPayResult>('/corp/pay', data)
}
