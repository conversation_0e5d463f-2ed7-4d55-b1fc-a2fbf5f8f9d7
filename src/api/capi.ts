import createRequest from '../libs/request'
import { encryptCompliance } from '@/config/utils'
import type { FetchApisOption } from '@/config/utils'

export function fetchApis(params: FetchApisOption) {
  const objData = JSON.parse(JSON.stringify(params))
  if (objData.email) {
    objData.emailEncrypt = encryptCompliance(objData.email)
    delete objData.email
  } else if (objData.mobile) {
    objData.mobileEncrypt = encryptCompliance(objData.mobile)
    delete objData.mobile
  } else if (objData.account) {
    objData.accountEncrypt = encryptCompliance(objData.account)
    delete objData.account
  }
  if (objData.device_id) {
    objData.cs_ept_d = encryptCompliance(objData.device_id)
    delete objData.device_id
  }
  return createRequest('CAPI').get('/apis/v2', {
    params: objData
  })
}

export interface GiftItem {
  act_id: string
  done: number | string
  each_add: string
  max: string
  storage_size: number
  ocr_count: number
  got: string
}
export interface QueryGiftLogOption {
  gift_name: string
  token: string
  act_id?: string
  device_id?: string
  cs_ept_d?: string
  time_zone?: string
  platform?: string
}

export function queryGiftLog(data: QueryGiftLogOption) {
  return createRequest('PAPI').get<never, GiftItem[]>('/query_gift_log', {
    params: data
  })
}
