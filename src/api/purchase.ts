import createRequest from '../libs/request'

// 兑换前置检查接口参数类型
export interface ExchangePremiumPrepareParams {
  account: string
  code: string
  area_code: string
  encrypt_uid?: string
}

// 兑换接口参数类型
export interface ExchangePremiumParams {
  account: string
  encrypt_uid?: string
  code: string
  product_id: string
  area_code: string
}

// 兑换前置检查接口
export function exchangePremiumPrepare(params: ExchangePremiumPrepareParams) {
  const formData = new FormData()
  formData.append('account', params.account)
  formData.append('code', params.code)
  formData.append('area_code', params.area_code)
  formData.append('encrypt_uid', params.encrypt_uid || '')

  return createRequest('PAPI').post('/purchase/exchangePremiumPrepare', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 兑换接口
export function exchangePremium(params: ExchangePremiumParams) {
  const data = new URLSearchParams()
  data.append('account', params.account)
  data.append('encrypt_uid', params.encrypt_uid || '')
  data.append('code', params.code)
  data.append('product_id', params.product_id)
  data.append('area_code', params.area_code)

  return createRequest('PAPI').post('/purchase/exchangePremium', data, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
} 