import createRequest from '../libs/request'
import config from '@/config'
import type { AxiosResponse } from 'axios'

/**
 * 设置用户访问版本
 * @param token
 * @param version
 */

export interface setWebVersionOption {
  token: string
  version: string
}

export function setWebVersion(data: setWebVersionOption) {
  return createRequest('OLD_WEB_API_USER').post('/setWebVersion', data)
}

interface queryPropertyOption {
  token: string
  property: string
}

export interface VipLevelInfo {
  level: number
  score: number
  next_score: number
}

export enum TCorpRole {
  ADMIN = '001',
  NORMAL = '002'
}

export type CorpAccount = {
  corp_user_type: 0 | 1 //0 普通用户, 1 移交用户
  corp_encrypt_id: string // 用户企业加密user_id
  name_remark: string // 用户备注名
  expire_time: number // 企业版过期时间
  role: TCorpRole // 企业版角色  '001' 管理员, '002' 普通成员
  corp_name: string // 企业版名称
  corp_id: string // 企业id
  corp_logo: string // 企业logo 'http://url' ，没有则为空字符串
  create_time: string // 创建时间
  token_domain: string // http://... 获取token的域名
}

export interface PropertyData {
  psnl_vip_property?: {
    expiry: string
    auto_renewal: boolean
    renew_method: string
    grade: number
    vip_level_info: VipLevelInfo
    show_expired: number
    show_expired1: number
    group1_paid: number
    group2_paid: number
    nxt_renew_tm: number
    renew_type: string
    product_id: string
    vip_type: string
    pc_vip?: number
  }
  dir?: {
    total_num: number
    cur_total_num: number
    vip_layer_num: number
    vip_total_num: number
  }
  points: string
  fax_balance: string
  server_time: string
  cs_edu: string
  cs_license: string
  team_vip_property?: {
    expiry: string
  }

  corp_vip_property?: {
    // 外层数据已经失效，不再使用！仅做兼容！
    expire_time: number // 企业版过期时间
    role: string // 企业版角色  '001' 管理员, '002' 普通成员
    corp_name: string // 企业版名称
    corp_id: string // 企业id
    corp_logo: string // 企业logo 'http://url' ，没有则为空字符串
    create_time: string // 创建时间
    token_domain: string // http://... 获取token的域名

    corp_user_list: CorpAccount[]
  }

  balance_webocr?: number
  ocr_balance?: string
}

export function queryProperty(data: queryPropertyOption) {
  return createRequest('OLD_WEB_API_USER').post<
    never,
    {
      ret: string
      data: PropertyData
    }
  >(
    '/queryProperty',
    Object.assign({}, data, {
      api_domain: config.api['PAPI']
    })
  )
}

interface InitMsgResult {
  num: boolean
  unread_msg: string
}

export function initMsg(token: string, isDomestic: boolean) {
  const api = isDomestic ? 'OLD_WEB_API_USER_DOMESTIC' : 'OLD_WEB_API_USER_FOREIGN'
  return createRequest(api).post<
    never,
    {
      data: InitMsgResult
    }
  >('/initMsg', {
    token
  })
}

interface QueryShareInfoParams {
  token: string
  expiry: number
  doc_ids?: string
  dir_id?: string
  doc_id?: string | string[]
  page_ids?: string
  api_domain?: string
  encrypt: number // 是否需要提取码
  share_channel: string
}

export function queryShareInfo(token: string, isDomestic: boolean, docId: any, expiry: number, share_channel: string, encrypt: number, dir_id?: string, page_ids?: any[]) {
  const api = isDomestic ? 'OLD_WEB_API_USER_DOMESTIC' : 'OLD_WEB_API_USER_FOREIGN'
  const params: QueryShareInfoParams = {
    token,
    expiry, 
    encrypt,
    share_channel
  }
  if (docId && docId instanceof Array && docId.length > 1) {
    params.doc_ids = JSON.stringify(docId)
    if (dir_id) {
      params.dir_id = dir_id
    }
  } else if (docId instanceof Array && docId.length === 1) {
    params.doc_id = docId[0]
  } else {
    params.doc_id = docId
  }

  if (page_ids) {
    params.page_ids = JSON.stringify(page_ids)
  }
  params.api_domain = config.api.SAPI
  return createRequest(api).post('/queryShareInfo', params)
}

interface UpdateMsgStatusParams {
  token: string
  status: string
  id?: string
  json_ids?: string
}

export function updateMsgStatus(token: string, isDomestic: boolean, status: string, msgIdArray: string[]) {
  const api = isDomestic ? 'OLD_WEB_API_USER_DOMESTIC' : 'OLD_WEB_API_USER_FOREIGN'
  const postParam: UpdateMsgStatusParams = {
    token,
    status
  }
  if (msgIdArray.length === 1) {
    postParam.id = msgIdArray[0]
  } else {
    postParam.json_ids = JSON.stringify(msgIdArray)
  }
  return createRequest(api).post('/updateMsgStatus', postParam)
}

interface DownloadDocPdfByEncryptIdParams {
  encrypt_id: string
  sid: string
  code?: string
  title?: string
  api_domain: any
  is_dir?: number | null
  doc_id?: string
  page_id?: String
  token?: string
  is_domestic?: 1 | 0
}

export function downloadDocPdfByEncryptId(isDomestic: boolean, encryptId: string, sid: string, title: string, code: string | undefined, is_dir: number | null, docid = '') {
  const api = isDomestic ? 'OLD_WEB_API_USER_DOMESTIC' : 'OLD_WEB_API_USER_FOREIGN'
  const params: DownloadDocPdfByEncryptIdParams = {
    encrypt_id: encryptId,
    sid: sid,
    code: code,
    title: title,
    api_domain: config.api.SAPI
  }
  if (is_dir) {
    params.is_dir = is_dir
  }
  if (docid) {
    params.doc_id = docid.replace('.jdoc', '')
  }
  return createRequest(api).post('/downloadShareDoc', params)
}

interface DownloadDocPdfByDeviceIdParams {
  device_id: string
  sid: string
  code?: string
  title?: string
  api_domain: any
  is_dir?: number | null
  doc_id?: string
  page_id?: string
  token?: string
  is_domestic?: 1 | 0
}

export function downloadDocPdfByDeviceId(deviceId: string, sid: string, title: string, code: undefined | string, is_dir: number | null, docid = '') {
  const params: DownloadDocPdfByDeviceIdParams = {
    device_id: deviceId,
    sid: sid,
    code: code,
    title: title,
    api_domain: config.api.SAPI
  }
  if (is_dir) {
    params.is_dir = is_dir
  }
  if (docid) {
    params.doc_id = docid.replace('.jdoc', '')
  }
  return createRequest('OLD_WEB_API_USER').post('/downloadShareDoc', params)
}

export function downloadPagePdfByEncryptId(isDomestic: boolean, encryptId: string, sid: string, pageId: string | undefined, title: string, code: undefined | string, is_dir: number | null, docid = '') {
  const api = isDomestic ? 'OLD_WEB_API_USER_DOMESTIC' : 'OLD_WEB_API_USER_FOREIGN'
  const params: DownloadDocPdfByEncryptIdParams = {
    encrypt_id: encryptId,
    sid: sid,
    page_id: pageId,
    code: code,
    title: title,
    api_domain: config.api.SAPI
  }
  if (is_dir) {
    params.is_dir = is_dir
  }
  if (docid) {
    params.doc_id = docid.replace('.jdoc', '')
  }
  return createRequest(api).post('/downloadSharePage', params)
}

export function downloadPagePdfByDeviceId(deviceId: string, sid: string, pageId: undefined | string, title: string, code: undefined | string, is_dir: number | null, docid = '') {
  const params: DownloadDocPdfByDeviceIdParams = {
    device_id: deviceId,
    sid: sid,
    page_id: pageId,
    code: code,
    title: title,
    api_domain: config.api.SAPI
  }
  if (is_dir) {
    params.is_dir = is_dir
  }
  if (docid) {
    params.doc_id = docid.replace('.jdoc', '')
  }
  return createRequest('OLD_WEB_API_USER').post('/downloadSharePage', params)
}

export interface AllDataListOptions {
  create_time: string
  file_name: string
  first_page_id: string
  office_first_page_id?: string
  pages: string
  title: string
  password?: string
  upload_time: string
  sid: string
  docId: string
  info: {}
}

export interface QueryShareDirResult {
  dirs: {
    docs: AllDataListOptions[]
  }
  share_info: {
    create_time: string
    dir_id: string
    docs: string
    encrypt: boolean
    share_device: string
  }
}

export function querySharePagesDirByEncryptId(isDomestic: boolean, encryptId: string, sid: string, code: string) {
  const api = isDomestic ? 'OLD_WEB_API_USER_DOMESTIC' : 'OLD_WEB_API_USER_FOREIGN'
  return createRequest(api).post<
    never,
    {
      data: QueryShareDirResult
    }
  >('/queryShareDirByCode', {
    encrypt_id: encryptId,
    sid: sid,
    code: code,
    api_domain: config.api.SAPI
  })
}

export function querySharePagesDirByDeviceId(deviceId: string, sid: string, code: string) {
  return createRequest('OLD_WEB_API_USER').post<
    never,
    {
      data: QueryShareDirResult
    }
  >('/queryShareDirByCode', {
    device_id: deviceId,
    sid: sid,
    code: code,
    api_domain: config.api.SAPI
  })
}

export function queryShareDirByEncryptId(isDomestic: boolean, encryptId: string, sid: string) {
  const api = isDomestic ? 'OLD_WEB_API_USER_DOMESTIC' : 'OLD_WEB_API_USER_FOREIGN'
  return createRequest(api).post<
    never,
    {
      data: QueryShareDirResult
    }
  >('/queryShareDirBySid', {
    encrypt_id: encryptId,
    sid: sid,
    api_domain: config.api.SAPI
  })
}

export function queryShareDirByDeviceId(deviceId: string, sid: string) {
  let api_domain = config.api.SAPI

  api_domain = sid.substring(sid.length - 2).toLocaleLowerCase() === 'us' ? config.api.SAPI_FOREIGN : config.api.SAPI_FOREIGN.SAPI_DOMESTIC

  return createRequest('OLD_WEB_API_USER').post<
    never,
    {
      data: QueryShareDirResult
    }
  >('/queryShareDirBySid', {
    device_id: deviceId,
    sid: sid,
    api_domain: api_domain
  })
}

export function queryShareDirByDeviceIdPdf(deviceId: string, sid: string) {
  const api = sid.substring(sid.length - 2).toLocaleLowerCase() === 'us' ? 'SAPI_JPG_FOREIGN' : 'SAPI_JPG_DOMESTIC'

  const path = ['cn', 'us'].includes(sid.substring(sid.length - 2).toLocaleLowerCase()) ? 'query_device_share_info_with_link_v2' : 'query_device_share_info_with_link'

  const params: {
    device_id: string
    link: string
    encrypt?: string
    dir?: string
  } = {
    device_id: deviceId,
    link: sid,
    dir: '1'
  }
  return createRequest(api).get<
    never,
    {
      error_code: number
      error_msg: string
      status: number
      data: any
    }
  >(path, {
    params
  })
}

export function setReportByEncryptId(isDomestic: boolean, encryptId: string, sid: string, reason: string) {
  let api_domain
  if (encryptId) {
    api_domain = config.api.SAPI_DOMESTIC
  } else {
    api_domain = config.api.OAPI
  }
  const api = isDomestic ? 'OLD_WEB_API_USER_DOMESTIC' : 'OLD_WEB_API_USER_FOREIGN'
  return createRequest(api).post('/setReport', {
    encrypt_id: encryptId,
    sid: sid,
    reason: reason,
    api_domain: api_domain
  })
}

export function setReportByDeviceId(deviceId: string, sid: string, reason: string) {
  return createRequest('OLD_WEB_API_USER').post('/setReport', {
    device_id: deviceId,
    sid: sid,
    reason: reason
  })
}

export function saveShareDocByEncryptId(isDomestic: boolean, encryptId: string, sid: string, code: undefined | string, token: string, userDomestic: boolean, is_dir: number | null, docid = '') {
  const api = isDomestic ? 'OLD_WEB_API_USER_DOMESTIC' : 'OLD_WEB_API_USER_FOREIGN'
  const params: DownloadDocPdfByEncryptIdParams = {
    encrypt_id: encryptId,
    sid: sid,
    code: code,
    token: token,
    is_domestic: userDomestic ? 1 : 0,
    api_domain: config.api.SAPI
  }
  if (is_dir) {
    params.is_dir = is_dir
  }
  if (docid) {
    params.doc_id = docid.replace('.jdoc', '')
  }
  return createRequest(api).post<
    never,
    {
      errno: number
      data: {
        doc_id: string
      }
    }
  >('/saveShareDoc', params)
}

export function saveShareDocByDeviceId(deviceId: string, sid: string, code: undefined | string, token: string, userDomestic: boolean, is_dir: number | null, docid = '') {
  const params: DownloadDocPdfByDeviceIdParams = {
    device_id: deviceId,
    sid: sid,
    code: code,
    token: token,
    is_domestic: userDomestic ? 1 : 0,
    api_domain: config.api.SAPI
  }
  if (is_dir) {
    params.is_dir = is_dir
  }
  if (docid) {
    params.doc_id = docid.replace('.jdoc', '')
  }
  return createRequest('OLD_WEB_API_USER').post<
    never,
    {
      errno: number
      data: {
        doc_id: string
      }
    }
  >('/saveShareDoc', params)
}

export interface SaveShareDocOption {
  token?: string
  api_domain: string
  is_domestic: number
  encrypt_id?: string
  device_id?: string
  sid?: string
  code?: string
  doc_id?: string
  is_dir?: number
}

export function saveShareDoc(body: SaveShareDocOption) {
  return createRequest('OLD_WEB_API_USER').post('/saveShareDoc', body)
}

export interface QueryShareInfoResult {
  doc_id: string
  info: {
    title: string
    share_device: string
    pages: string
  }
  secure: boolean
  updated?: number
  page_id?: string
}

export function queryShareInfoByEncryptId(isDomestic: boolean, encryptId: string, sid: string) {
  const api = isDomestic ? 'OLD_WEB_API_USER_DOMESTIC' : 'OLD_WEB_API_USER_FOREIGN'
  return createRequest(api).post<
    never,
    {
      data: QueryShareInfoResult
    }
  >('/queryShareInfoBySid', {
    encrypt_id: encryptId,
    sid: sid,
    api_domain: config.api.SAPI
  })
}

export function queryShareInfoByDeviceId(deviceId: string, sid: string) {
  return createRequest('OLD_WEB_API_USER').post<
    never,
    {
      data: QueryShareInfoResult
    }
  >('/queryShareInfoBySid', {
    device_id: deviceId,
    sid: sid,
    api_domain: config.api.SAPI
  })
}

export function querySharePagesByEncryptId(isDomestic: boolean, encryptId: string, sid: string, code: string) {
  const api = isDomestic ? 'OLD_WEB_API_USER_DOMESTIC' : 'OLD_WEB_API_USER_FOREIGN'
  return createRequest(api).post<
    never,
    {
      data: string
    }
  >('/queryShareInfoByCode', {
    encrypt_id: encryptId,
    sid: sid,
    code: code,
    api_domain: config.api.SAPI
  })
}

export function querySharePagesByDeviceId(deviceId: string, sid: string, code: string) {
  return createRequest('OLD_WEB_API_USER').post<
    never,
    {
      data: string
    }
  >('/queryShareInfoByCode', {
    device_id: deviceId,
    sid: sid,
    code: code,
    api_domain: config.api.SAPI
  })
}
export interface BindListOption {
  token: string
  from_type?: string
}
// 获取绑定关系
export function getBindList(data: BindListOption) {
  return createRequest('UAPI').get<
    never,
    {
      ret: number
      data: any
    }
  >('/bind_list', {
    params: data
  })
}
export interface certOcrDetailOption {
  token?: string
  cs_ept_d?: string
  platform: string
  cert_cate?: string
  cert_cate_code: number
  func?: string
}

// 身份证识别
export function certOcrDetail(data: Blob, params: certOcrDetailOption) {
  return createRequest('OAPI').post<
    never,
    {
      ret: number
      error_code: number
      cert_detail: any
    }
  >('/cert_ocr_detail', data, {
    params: params
  })
}

export interface personVerifyOption {
  token: string
  sms_token?: string
  func: string
  timestamp: number
  event?: string
  encrypt?: boolean
}
export function personMobileVerify(data: object, params: personVerifyOption) {
  return createRequest('UAPI').post<
    never,
    {
      err: string
      ret: number
      data: {
        jwt: string
        status: number
      }
    }
  >('/person/mobile/verify', data, {
    params: params
  })
}
export interface SendSMSOption {
  area_code?: string
  client: string
  client_app?: string
  client_id?: string
  mobile?: string
  language: string
  reason?: string
  sign?: string
  sign_type?: string
  [key: string]: string | number | undefined
}

// 发送手机验证码
export function sendSMS(params: SendSMSOption) {
  return createRequest('UAPI_WAF').get<
    never,
    {
      ret: number
      data: {
        success: number
        gt: string
        challenge: string
      }
    }
  >('/send_sms_vcode', {
    params: params
  })
}
export interface VerifySmsVcodeOption {
  client: string
  client_app: string
  client_id: string
  timestamp: number
  vcode: string
  area_code?: string
  reason?: string
  jy_mobile?: string
  mobile?: string
  jy_email?: string
  email?: string
  sign?: string
  sign_type?: string
  [key: string]: string | number | undefined
}

// 验证码验证
export function verifySmsVcode(data: VerifySmsVcodeOption) {
  return createRequest('UAPI_WAF').get<
    never,
    {
      ret: number
      data: {
        gt: string
        challenge: string
        sms_token: string
      }
      user_id: number
    }
  >('/verify_sms_vcode', {
    params: data
  })
}
export interface UserInfoOption {
  token: string
  client_app: string
}

export interface UserInfo {
  profile: {
    mobile?: string
    area_code?: string
    email?: string
    display_name?: string
    isBindPhone?: boolean
  }
  licenses_detail?: {
    promote_event: string
  }
}

export function userInfo(data: UserInfoOption) {
  return createRequest('UAPI').get<never, UserInfo>('/user_info2', {
    params: data
  })
}

interface ConfigRes {
  token: string
  user_id: string
  device_id: string
}

export function getConfig(data: { intsig_key?: string; encrypt_uid: string }) {
  return createRequest('OLD_WEB_USER').post<
    never,
    {
      ret: number
      data: ConfigRes
    }
  >(
    '/app/getConfig',
    Object.assign({}, data, {
      api_domain: config.api['UAPI']
    })
  )
}

/**
 * 获取企业数据概况
 */
export interface CorpReportParams {
  token: string
  corp_id: string
}

export interface CorpReportData {
  activate_rate: string
  capacity_use: {
    allocated_capacity: string
    capacity: string
  }
  activate_monthly: number
  activate_num_per_month: Record<string, number>
  account_sum: string
  activate_num: string
  activate_rate_per_month: Record<string, number>
}

export function getCorpReport(params: CorpReportParams): Promise<{
  ret: number
  err: string
  data: CorpReportData
}> {
  return createRequest('UAPI').get('/corp/report/corp_use', { params })
}

/**
 * 获取成员使用情况列表
 */
export interface MemberUsageParams {
  token: string
  corp_id: string
  page?: number
  page_size?: number
}

export interface MemberUsageItem {
  account_sum: string // 账户总数
  activate_num: string // 激活数量
  activate_con_yesterday: string // 昨日连续激活
  activate_rate: string // 激活率
  activate_rate_con_yesterday: string // 昨日连续激活率
  capacity_use: {
    allocated_capacity: string // 已分配容量
    capacity: string // 总容量
  }
  activate_monthly: number // 月度激活
  ret: number // 返回状态
}

export interface MemberUsageListData {
  err: string
  data: MemberUsageItem[]
  account_sum: string
  activate_num: string
  activate_con_yesterday: string
  activate_rate: string
  activate_rate_con_yesterday: string
  capacity_use: {
    allocated_capacity: string
    capacity: string
  }
  activate_monthly: number
}

export function getMemberUsageList(params: MemberUsageParams): Promise<{
  ret: number
  err: string
  data: MemberUsageListData
}> {
  return createRequest('UAPI').get('/corp/report/corp_use', { params })
}
