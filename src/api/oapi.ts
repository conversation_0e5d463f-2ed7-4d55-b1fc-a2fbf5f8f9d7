import type { TDocItem } from '@/api/sapi'

import createRequest from '../libs/request'

interface GetCsCfgOption {
  upload_time: string
  platform: string
  device_id?: string
}

export interface GetCsCfgResult {
  ad_upload_time: number
  appsflyer_set: number
  cfg_upload_time: number
  cs_link_style: number
  cs_pdf_union_share: number
  dir: any // TODO:
  dir_share_style: number
  download_v2: number
  greetingcard_upload_time: number
  ip_country: string
  is_european_union: number
  is_internal_ip: number
  link_page: number
  ocr_free: number
  service_time: number
  share_dir_v2: number
  share_mail_banner_style: string
  upload_time: number
  web_version: string
  web_toc_camera_shoot: boolean
  dir_encrypt?: number
  doc_encrypt?: number
}

export function getCsCfg(data?: GetCsCfgOption) {
  return createRequest('OAPI').get<never, GetCsCfgResult>('/get_cs_cfg', {
    params: data
  })
}

export function getOcrWordlink(token: string, fileName: string, ocrText: string) {
  return createRequest('OAPI').post<
    never,
    {
      link?: string
      create_time?: number
    }
  >(
    '/add_word_share',
    { data: ocrText },
    {
      params: {
        token: token
        // file_name: fileName
      }
    }
  )
}

export function modifyShareData(token: string, sid: string, title: string, type_value: string) {
  const params = {
    token,
    sid,
    type_value
  }
  return createRequest('OAPI').post<
    never,
    {
      ret: number
      err: string
    }
  >(
    '/modify_share_data',
    {
      title
    },
    { params: params }
  )
}

interface DeleteShareGetParams {
  sid: string
  type?: string
  type_value?: string
  device_id?: string
  token?: string
}

export function deleteShare(token: string, deviceId: string, sid: string, type = '', type_value: string) {
  const getParams: DeleteShareGetParams = {
    sid
  }
  if (type) {
    getParams.type = type
  }
  if (type_value) {
    getParams.type_value = type_value
  }
  if (token) {
    getParams.token = token
  } else {
    getParams.device_id = deviceId
  }
  return createRequest('OAPI').get<
    never,
    {
      error_msg: string
      error_code: number
      status: number
    }
  >('/delete_share_data', {
    params: getParams
  })
}

export interface QueryConvertListResult {
  sid: string
  type: string
  modify_time: string
  create_time: string
  dead_time: string
  title: string
  convert_status: number
  convert_id: string
  downloadUrl?: string
  shareUrl?: string
  encrypt_id?: string
}

export function queryConvertList(params: { token: string; type: string }) {
  return createRequest('SAPI_DOC_FOREIGN').get<
    never,
    {
      error_msg: string
      error_code: number
      status: number
      share_info?: QueryConvertListResult[]
      share_num?: number
    }
  >('/query_convert_list', {
    params: params
  })
}

export function deleteConvertData(params: { convert_id: string; type: string; token: string }) {
  return createRequest('SAPI_DOC_FOREIGN').get<
    never,
    {
      error_msg: string
      error_code: number
      status: number
    }
  >('/delete_convert_data', {
    params: params
  })
}

interface UploadPdfParams {
  token: string
  title: string
  id: string
  platform: string
  with_file_id: number
  dir_id?: string
}

// 仅用于保存到帐后内
export function uploadPdf(token: string, title: string, id: string, file_ids: string[], dir_id: string, platform = 'web') {
  const params: UploadPdfParams = {
    token,
    title,
    id,
    platform,
    with_file_id: 1
  }
  if (dir_id && dir_id !== 'root') {
    params.dir_id = dir_id
  }
  return createRequest('SAPI').post<
    never,
    {
      error_msg: string
      error_code: number
      status: number
    }
  >('/upload_pdf', file_ids, {
    params: params
  })
}

export interface QueryFileConvertListResult {
  sid: string
  type: string // 文档类型(0:txt转WORD记录，1:转EXCEL记录,2:pdf转word记录)，6:OCR记录
  modify_time: string
  create_time: string
  dead_time: string
  title: string
}

export function queryFileConvertList(token: string) {
  const params = {
    token,
    type: 'all'
  }
  return createRequest('OAPI').get<
    never,
    {
      error_msg: string
      share_num: number
      error_code: number
      status: string
      share_info: QueryFileConvertListResult[]
    }
  >('/query_share_data_list', {
    params: params
  })
}

/**
 * 新增标签参数
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207227
 * @member token - 用户token
 * @member author - 用户 Camscanner web
 * @member tag_id - 标签id
 * @member title - 标签名称
 */
export interface TAddTagParam {
  author: string
  tag_id: string
  title: string
  token: string
}

/**
 * 新增标签返回结果
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207227
 * @member status - 响应状态码
 */
export type TAddTagResponse =
  | {
      status: 0
      error_msg: string
    }
  | {
      error_msg: string
      error_code: 105
    }

/**
 * 获取标签列表
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207227
 * @param data {TGetTagListParam} 获取标签列表参数
 * @return {TAddTagResponse} 标签列表结果
 */
export function addTag(data: TAddTagParam): Promise<TAddTagResponse> {
  return createRequest('SAPI').post('/add_tag', data)
}

/**
 * 删除标签参数
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207233
 * @member token - 用户token
 * @member tag_id - 标签id
 */
export interface TDeleteTagParam {
  tag_id: string
  token: string
}

/**
 * 删除标签返回结果
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207233
 * @member status - 响应状态码
 */
export type TDeleteTagResponse =
  | {
      status: 0
      error_msg: string
    }
  | {
      error_msg: string
      error_code: 101
    }

/**
 * 更新标签
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207233
 * @param params {TDeleteTagParam} 删除标签参数
 * @return {TDeleteTagResponse} 删除标签结果
 */
export function deleteTag(params: TDeleteTagParam): Promise<TDeleteTagResponse> {
  return createRequest('SAPI').get('/delete_tag', { params })
}

/**
 * 标签列表参数
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207187
 * @member token - 用户token
 * @member order_type - 按create_time排序方式，0: 降序  1: 升序
 */
export interface TGetTagListParam {
  order_type: number
  token: string
}

/**
 * 标签
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207187
 * @member file_name - 后台生成tag_id
 * @member title - 用户自定义名称
 */
export interface TTagItem {
  file_name: string | 'unarchived'
  title: string
}

/**
 * 标签列表返回结果
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207187
 * @member status - 响应状态码
 * @member tag_list - 标签列表
 */
export type TGetTagListResponse =
  | {
      error_code: 0
      data: {
        tag_list: TTagItem[]
      }
    }
  | {
      error_msg: string
      error_code: 105
    }

/**
 * 获取标签列表
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207187
 * @param params {TGetTagListParam} 获取标签列表参数
 * @return {TGetTagListResponse} 标签列表结果
 */
export function getTagList(params: TGetTagListParam): Promise<TGetTagListResponse> {
  return createRequest('SAPI').get('/query_tag_list', { params })
}

/**
 * 更新标签参数
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207233
 * @member token - 用户token
 * @member tag_id - 标签id
 * @member title - 标签名称
 */
export interface TUpdateTagParam {
  tag_id: string
  title: string
  token: string
}

/**
 * 更新标签返回结果
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207233
 * @member status - 响应状态码
 */
export type TUpdateTagResponse =
  | {
      status: 0
      error_msg: string
    }
  | {
      error_msg: string
      error_code: 101
    }

/**
 * 更新标签
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207233
 * @param data {TGetTagListParam} 更新标签参数
 * @return {TUpdateTagResponse} 更新标签结果
 */
export function updateTag(data: TUpdateTagParam): Promise<TUpdateTagResponse> {
  return createRequest('SAPI').post('/update_tag', data)
}

/**
 * 文档转换结果结构
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207250
 * @member token - 文档类型
 * @member sid - sid
 * @member type_value - 文档标题
 */
export interface TDeleteConvertParam {
  token: string
  sid: string
  type_value: TConvertItemType
}

/**
 * 文件转换列表返回结果
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207250
 * @member error_code - 响应状态码
 * @member orderType - 排序类型
 * @member isDomestic - 是否是国内用户 会决定调用接口域名
 * @member share_info - 文件转换列表
 */
export type TDeleteConvertResponse =
  | {
      error_code: 0
      status: number
    }
  | {
      error_msg: string
      error_code: 101
    }

/**
 * 获取全量文件夹接口
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207250
 * @param params {TDirParams} 获取全量文件夹参数
 * @return {TDocItem} 文件夹结果
 */
export function deleteConvert(params: TDeleteConvertParam): Promise<TDeleteConvertResponse> {
  return createRequest('OAPI').get('/delete_share_data', { params })
}

/**
 * 文档转换结果结构
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=158630199
 * @member sid - sid
 * @member type_value - 文档标题
 * @member modify_time - 修改时间
 */
export type TUpdateConvertParam = TDeleteConvertParam

/**
 * 文件转换列表返回结果
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=158630199
 * @member error_code - 响应状态码
 * @member orderType - 排序类型
 * @member isDomestic - 是否是国内用户 会决定调用接口域名
 * @member share_info - 文件转换列表
 */
export type TUpdateConvertResponse =
  | {
      err: string
      ret: 0
    }
  | {
      err: string
      ret: 101
    }

/**
 * 获取全量文件夹接口
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=158630199
 * @param params {TDirParams} 获取全量文件夹参数
 * @param data 待更新文档title
 * @return {TUpdateConvertResponse} 文件夹结果
 */
export function updateConvert(params: TUpdateConvertParam, data: { title: string }): Promise<TUpdateConvertResponse> {
  return createRequest('OAPI').post('/delete_share_data', data, { params })
}

/**
 * 文档转换操作类型
 * 文档类型(0:txt转WORD记录，1:转EXCEL记录,2:pdf转word记录)，6:OCR记录
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207193
 */
export enum TConvertItemType {
  txt = '6',
  doc0 = '0',
  doc2 = '2',
  doc4 = '4',
  xls1 = '1',
  xls3 = '3'
}

/**
 * 文档转换列表参数
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207193
 * @member token - 用户token
 * @member type - 获取文档类型 all表示全部获取
 */
export interface TConvertParam {
  type: string
  token: string
}

/**
 * 文档转换结果结构
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207193
 * @member type - 文档类型
 * @member sid - sid
 * @member title - 文档标题
 * @member modify_time - 修改时间
 */
export interface TConvertItem {
  type: TConvertItemType
  sid: string
  title: string
  modify_time: number
}

/**
 * 文档转换列表返回结果
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207193
 * @member error_code - 响应状态码
 * @member orderType - 排序类型
 * @member isDomestic - 是否是国内用户 会决定调用接口域名
 * @member share_info - 文件转换列表
 */
export type TConvertListResponse =
  | {
      error_msg: string
      share_num: number
      error_code: 0
      status: string
      share_info: TConvertItem[]
    }
  | {
      error_msg: string
      error_code: 105
    }

/**
 * 获取文档转换列表
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207193
 * @param params {TConvertParam} 获取全量文件夹参数
 * @return {TConvertItem} 文件夹结果
 */
export function getConvertList(params: TConvertParam): Promise<TConvertListResponse> {
  return createRequest('OAPI').get('/query_share_data_list', { params })
}

/**
 * 给文档打标签参数
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207241
 * @member token - 用户token
 * @member tag_ids - 标签id 多个由逗号拼接
 * @member doc_id - 文档id
 */
export interface TUpdateDocTagRelationParam {
  tag_ids: TTagItem['file_name']
  doc_id: TDocItem['file_name']
  token: string
}

/**
 * 给文档打标签返回结果
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207241
 * @member status - 响应状态码
 */
export type TUpdateDocTagRelationResponse =
  | {
      status: 0
      error_msg: string
    }
  | {
      error_msg: string
      error_code: 101
    }

/**
 * 给文档打标签
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207241
 * @param params {TGetTagListParam} 给文档打标签参数
 * @return {TUpdateTagResponse} 给文档打标签结果
 */
export function updateRelationDocAndTag(params: TUpdateDocTagRelationParam): Promise<TUpdateDocTagRelationResponse> {
  return createRequest('SAPI').get('/update_relation_between_doc_and_tag', { params })
}
