import createRequest from '../libs/request'
import config from '@/config'
import type { originDocArray } from '@/stores/doc-store'
import { useUserStore } from '@/stores/user-store'
import type { CancelToken } from 'axios'

interface QueryDocListResult {
  file_name: string
  cotoken: string
  create_time: string
  first_page_id: string
  office_first_page_id?: string
  first_page_modify_time: string
  flag?: string
  password?: string
  modify_time: string
  page_num: string
  rotate: number
  sort_key: string
  sort_key_list: string[]
  tag_ids: string
  title: string
}

// filter_esign = 1 查询的文档过滤电子签文档
export function queryDocList(token: string, isDomestic: boolean, orderType: number, filter_esign = 1) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).get<
    never,
    {
      status: number
      data: {
        doc_list: QueryDocListResult[]
      }
    }
  >('/query_doc_list', {
    params: {
      token: token,
      order_type: orderType,
      filter_esign: filter_esign
    }
  })
}

interface QueryDirBasicResult {
  error_code: number
  error_msg: string
  status: number
  upload_time: number
}

export interface QueryDirResult extends QueryDirBasicResult {
  cur_total_num?: number
  total_num?: number
  vip_layer_num?: number
  vip_total_num?: number
  lock?: number
  dir_id?: string
  dirs?: QueryDirDirsResult[]
}

export interface QueryDirDirsResult {
  create_time?: number
  dir_id?: string
  dir_type?: number
  dirs?: QueryDirDirsResult[]
  docs?: originDocArray[]
  title: string
  upload_time?: number
  parent?: string
  children?: string[]
  layer?: number
  lock?: number
  unfold?: boolean
  update_time?: number
  canEdit?: boolean
  parents?: string
  quantity?: number
  selected?: boolean
  modify_time?: number
}

export function queryDir(token: string, isDomestic: boolean, orderType: number) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).get<never, QueryDirResult>('/query_dir/v2', {
    params: {
      token: token,
      order_type: orderType,
      show_advanced_dir: 1
    }
  })
}

export interface queryTagListResult {
  tag_list: {
    file_name: string
    title: string
  }[]
}

export function queryTagList(token: string, isDomestic: boolean, orderType: number = 0) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).get<
    never,
    {
      status: number
      data: queryTagListResult
    }
  >('/query_tag_list', {
    params: {
      token: token,
      order_type: orderType
    }
  })
}

export function queryStorage(token: string, isDomestic: boolean) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).get<
    never,
    {
      storage: string
    }
  >('/query_storage', {
    params: {
      token: token
    }
  })
}

interface UploadFileOption extends UploadFileParam {
  isDomestic: boolean
  dirId: string
  file: any
  cancel: any
}

interface UploadFileParam {
  type: string
  token: string
  size: string | number
  title: string
  id: string
  platform?: string
  dir_id?: string
  overwrite?: boolean
}

export function uploadFile(data: UploadFileOption) {
  let pf = 'web'
  if (data.platform) pf = data.platform
  console.log(data)
  const params: UploadFileParam = {
    type: data.type,
    token: data.token,
    size: data.size,
    title: data.title,
    id: data.id,
    platform: pf
  }
  const api = data.isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  if (data.dirId && data.dirId !== config.dirRoot) {
    params.dir_id = data.dirId
  }
  if (data.overwrite) {
    params.overwrite = data.overwrite
  }
  return createRequest(api).post<
    never,
    {
      ret: number
      data: {
        doc_id: string
      }
    }
  >('/upload_resource', data.file, {
    params: { ...params, thumb: 1 },
    cancelToken: data.cancel.token
  })
}

export function updateDocTag(token: string, isDomestic: boolean, docId: string, tagIds: string) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).get<
    never,
    {
      error_code: number
      error_msg: string
      status: number
    }
  >('/update_relation_between_doc_and_tag', {
    params: {
      token,
      doc_id: docId,
      tag_ids: tagIds
    }
  })
}

export function addRecentDoc(isDomestic: boolean, token: string, docId: string) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).post(
    '/add_view_file',
    [
      {
        file_name: `${docId}.jdoc`,
        timestamp: Math.floor(Date.now() / 1000)
      }
    ],
    {
      params: {
        token: token,
        device_id: 'web_camscanner'
      }
    }
  )
}

export interface DownloadDocPdfResult {
  url: string
  title: string
  pdf_url: string
}

export function downloadDocPdf(download_v2: number, token: string, isDomestic: boolean, title: string, postData: string) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  let url: string
  if (download_v2 === 1) {
    url = '/download_doc_pdf/v2'
  } else {
    url = '/download_doc_pdf'
  }
  return createRequest(api).post<
    never,
    {
      data?: DownloadDocPdfResult
    }
  >(`${url}?token=${token}&title=${title}`, 'download_data=' + postData)
}

export function downloadDocJpg(download_v2: number, token: string, isDomestic: boolean, title: string, postData: string) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  let url: string
  if (download_v2 === 1) {
    url = '/download_doc_jpg/v2'
  } else {
    url = '/download_doc_jpg'
  }
  return createRequest(api).post<
    never,
    {
      data?: {
        url: string
        title: string
      }
    }
  >(`${url}?token=${token}&title=${title}`, 'download_data=' + postData)
}

export interface NewDirInfoOptions {
  dir?: {
    title: string
  }
  dir_id?: string
  doc_id?: string
  to?: {
    dir_id: string
    update_time?: number
  }
  from?: {
    doc_list: string[]
    dir_id?: string
  }
  title?: string
  upload_time?: number
}

export function updateDir(token: string, isDomestic: boolean, op: number, postParam: NewDirInfoOptions) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).post<never, any>(`/update_dir?token=${token}&op=${op}`, JSON.stringify(postParam))
}

interface QueryDocInfoResult {
  cotoken: string
  create_time: string
  locked: number
  tag_ids: string
  title: string
  password?: string
  upload_time: string
  office_doc_size: number
  page_list: PageItemResult[]
  first_page_id?: string
}

interface PageItemResult {
  file_name: string
  modify_time: string
  note: string
  rotate: number
  title: string
}

export function queryDocInfo(token: string, isDomestic: boolean, docId: string) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).get<
    never,
    {
      error_code: number
      error_msg: string
      status: number
      data: QueryDocInfoResult
    }
  >('/query_doc_info', {
    params: {
      token: token,
      doc_id: docId
    }
  })
}

export function getPageInfo(isDomestic: boolean, token: string, fileName: string) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).get<
    never,
    {
      note: string
      border: string
      rotate: number | string
      ori_rotate?: number
    }
  >('/download_file', {
    params: {
      token: token,
      file_name: `${fileName}.jpage`,
      time: new Date().getTime()
    }
  })
}

//下载原图接口
export function getOringinImg(isDomestic: boolean, token: string, fileName: string) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).get<never, Blob>('/download_file', {
    params: {
      token: token,
      file_name: `${fileName}.jpage`,
      type: 'ori'
    },
    responseType: 'blob'
  })
}

export function updateDocName(isDomestic: boolean, token: string, docId: string, title: string) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).post<
    never,
    {
      error_msg: string
      error_code: number
      status: number
    }
  >('/update_doc', {
    check_title: 1,
    token: token,
    doc_id: docId,
    title: title
  })
}

interface UpdatePageOptions {
  token: string
  page_id: string
  title?: string
  note?: string
}

export function updatePage(isDomestic: boolean, token: string, pageId: string, title?: string, note?: string) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  const postParam: UpdatePageOptions = {
    token: token,
    page_id: pageId
  }
  // if (rotate || rotate === 0) postParam.rotate = rotate
  if (title) postParam.title = title
  if (note || note === '') postParam.note = note
  return createRequest(api).post<
    never,
    {
      error_msg: string
      error_code: number
      status: number
    }
  >('/update_page', postParam, {
    params: {
      token: token
    }
  })
}

interface QueryOcrDocListResult {
  doc_list: {
    doc_id: string
    upload_time: string
  }[]
}

export function queryOcrDocList(isDomestic: boolean, token: string) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).post<
    never,
    {
      data: QueryOcrDocListResult
      error_code: number
      error_msg: string
      status: number
    }
  >(`/query_ocr_doc_list?token=${token}`)
}

interface QueryOcrListResult {
  ocr_list: {
    doc_id: string
    ocr: string
    page_id: string
  }[]
}

export function queryOcrList(isDomestic: boolean, token: string, docId: string) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).post<
    never,
    {
      data: QueryOcrListResult
      error_code: number
      error_msg: string
      status: number
    }
  >(`/query_ocr_list?token=${token}&doc_id=${docId}`)
}

export interface CloudWebOcrResult {
  cloud_ocr:
    | string
    | {
        'ocr-user-text': string
      }
  ocr_user_text: string
  error_code: number
  error_msg: string
  status: number
}

export function cloudWebOcr(isDomestic: boolean, token: string, fileName: string, languages: string[]) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return new Promise<{
    body?: CloudWebOcrResult
    fileName: string
  }>(resolve => {
    createRequest(api)
      .get<never, CloudWebOcrResult>(`/web/cloud_ocr`, {
        params: {
          token: token,
          file_name: `${fileName}.jpage`,
          recent_langs: languages.join(',')
        }
      })
      .then(res => {
        resolve({
          body: res,
          fileName: fileName
        })
      })
      .catch(() => {
        resolve({
          fileName: fileName
        })
      })
  })
}

export function updateOcr(isDomestic: boolean, token: string, fileName: string, ocrText: string) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).post<
    never,
    {
      error_code: number
      error_msg: string
      status: number
    }
  >(`/update_ocr?token=${token}&manual=1&file_name=${fileName}.jpage`, ocrText)
}

interface QuerySignatureListResult {
  sig_list: {
    create_time: string
    file_name: string
    file_type: string
  }[]
  error_code: number
  error_msg: string
  status: number
}

export function querySignatureList(isDomestic: boolean, token: string) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).get<never, QuerySignatureListResult>(`/query_signature_list`, {
    params: {
      token: token
    }
  })
}

interface UploadSignatureOResult {
  error_code: number
  error_msg: string
  sig_id: string
  status: number
}

export function uploadSignatureO(isDomestic: boolean, token: string, data: any, with_pick: 0 | 1) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).post<never, UploadSignatureOResult>('/upload_signature', data, {
    params: {
      token,
      with_pick
    }
  })
}

export function deleteSignature(isDomestic: boolean, token: string, signId: string) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).get<
    never,
    {
      error_code: number
      error_msg: string
      status: number
    }
  >('/delete_signature', {
    params: {
      token: token,
      sig_id: signId
    }
  })
}

interface UploadPageOptions {
  token: string
  doc_id: string
  title: string
  with_copy: 1 | 0
  page_id?: string | string[]
}

export function uploadPage(data: any, isDomestic: boolean, token: string, docId: string, pageId: string, title: string, withCopy: boolean) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  const params: UploadPageOptions = {
    token: token,
    doc_id: docId,
    title: title,
    with_copy: withCopy ? 1 : 0,
    page_id: pageId
  }

  return createRequest(api).post<
    never,
    {
      error_code: number
      error_msg: string
      status: number
      doc_id: string
    }
  >('/update_jpg', data, {
    params: params
  })
}

interface MovePageOptions {
  to_dir_id?: string
  title?: string
  to_doc_id?: string
  pages?: string[]
}

interface MovePageParams {
  token: string
  doc_id: string
  op: string
  page_id?: string
}

export function movePage(isDomestic: boolean, token: string, docId: string, pageId: string | string[], isMove: boolean, data: MovePageOptions) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  const params: MovePageParams = {
    token: token,
    doc_id: docId,
    op: isMove ? '2' : '1'
  }
  if (pageId instanceof Array && pageId.length > 0) {
    data.pages = pageId
  } else if (typeof pageId === 'string') {
    params.page_id = pageId
  }
  return createRequest(api).post<
    never,
    {
      error_code: number
      error_msg: string
      status: number
    }
  >('/move_page', data, {
    params: params
  })
}

export function deletePage(isDomestic: boolean, token: string, pageId: string | string[]) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  const params: {
    token: string
    page_id?: string
  } = {
    token: token
  }
  if (typeof pageId === 'string') {
    params.page_id = pageId
    return createRequest(api).get<
      never,
      {
        error_code: number
        error_msg: string
        status: number
      }
    >(`/delete_page`, {
      params: params
    })
  } else {
    const data = {} as {
      pages: string[]
    }
    data.pages = pageId
    return createRequest(api).post<
      never,
      {
        error_code: number
        error_msg: string
        status: number
      }
    >(`/delete_page`, data, {
      params: params
    })
  }
}

export function docAddPicture(token: string, isDomestic: boolean, doc_id: string, types: string, file: any, title: string, platform = 'web') {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  const params = {
    types,
    token,
    doc_id,
    platform,
    title
  }
  return createRequest(api).post<
    never,
    {
      error_code: number
      error_msg: string
      status: number
      data: {
        doc_id: string
        page_id: string
      }
    }
  >('/web/doc_add_picture', file, {
    params: params
  })
}

export interface UpdatePictureRotateOptions {
  page_id: string
  rotate: any
}

export function updatePictureRotate(token: string, isDomestic: boolean, doc_id: string, params: UpdatePictureRotateOptions[]) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).post<
    never,
    {
      error_code: number
      error_msg: string
      status: number
    }
  >('/web/update_picture_rotate', params, {
    params: {
      token: token,
      doc_id: doc_id
    }
  })
}

export function docPictureSort(token: string, isDomestic: boolean, doc_id: string, params: { pages: string[] }) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).post('/web/doc_picture_sort', params, {
    params: {
      token: token,
      doc_id: doc_id
    }
  })
}

export function addTag(token: string, isDomestic: boolean, tagId: string, tagName: string) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).post<
    never,
    {
      status: number
    }
  >('/add_tag', {
    token,
    tag_id: tagId,
    title: tagName,
    author: 'CamScanner(Web)'
  })
}

export function updateTag(token: string, isDomestic: boolean, tagId: string, tagName: string) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).post<
    never,
    {
      error_code: number
      error_msg: string
      status: number
    }
  >('/update_tag', {
    token,
    tag_id: tagId,
    title: tagName
  })
}

export function deleteTag(token: string, isDomestic: boolean, tagId: string) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).get<
    never,
    {
      error_code: number
      error_msg: string
      status: number
    }
  >('/delete_tag', {
    params: {
      token,
      tag_id: tagId
    }
  })
}

export function queryRecentDocList(token: string, isDomestic: boolean, orderType: number, num: number, platform = 'web') {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).get<
    never,
    {
      error_code: number
      error_msg: string
      status: number
      doc_list: TDocItem[]
    }
  >('/query_recent_doc_list', {
    params: {
      token: token,
      order_type: orderType,
      platform: platform,
      num: num
    }
  })
}

export function deleteDoc(token: string, isDomestic: boolean, docId: string) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).get('/delete_doc', {
    params: {
      token: token,
      doc_id: docId
    }
  })
}

interface CheckFileStateResult {
  list: any[]
  upload_time: string
  file_md5: string
  encrypt_id?: string
  device_id?: string
}

export function checkFileState(
  data: any,
  params: {
    token?: string
    file_id: string
    encrypt_id?: string
    device_id?: string
    dir?: string
    sid?: string
  }
) {
  let api = 'SAPI'

  if (params.sid) {
    api = params.sid!.substring(params.sid!.length - 2).toLocaleLowerCase() === 'us' ? 'SAPI_JPG_FOREIGN' : 'SAPI_JPG_DOMESTIC'
  }

  const url = params.token || params['encrypt_id'] ? '/bigfile/file_state' : '/bigfile/device/file_state'
  // @ts-ignore
  return createRequest(api).post<
    never,
    {
      ret: number
      err: string
      data: CheckFileStateResult
    }
  >(url, data, {
    params: params
  })
}

interface DownloadSliceParams {
  token?: string
  file_id: any
  slice_id: any
  sid?: string
  upload_time: string
  encrypt_id?: string
  device_id?: string
  dir?: string
}

export function downloadSlice(data: any, params: DownloadSliceParams) {
  let api = 'SAPI'
  if (params.sid) {
    api = params.sid!.substring(params.sid!.length - 2).toLocaleLowerCase() === 'us' ? 'SAPI_JPG_FOREIGN' : 'SAPI_JPG_DOMESTIC'
  }
  //@ts-ignore
  const sapi = createRequest(api)
  const url = params.token || params['encrypt_id'] ? '/bigfile/download_slice' : '/bigfile/device/download_slice'
  return sapi({
    method: 'post',
    url: url,
    data: data,
    params: {
      encrypt_id: params.encrypt_id,
      device_id: params.device_id,
      file_id: params.file_id,
      token: params.token,
      slice_id: params.slice_id,
      sid: params.sid,
      upload_time: params.upload_time,
      dir: params.dir
    },
    responseType: 'arraybuffer'
  })
}

export function combineJdocProgress(token: string, isDomestic: boolean, progress_id: string) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).get<
    never,
    {
      error_code: number
      error_msg: string
      status: number
      data: {
        total: number
        now_complete_page: number
      }
    }
  >('/web/combine_jdoc_progress', {
    params: {
      token: token,
      progress_id: progress_id
    }
  })
}

interface CombineJdocParams {
  doc_list: string
  title: string
  progress_id: string
  to_dir_id?: string
}

export function combineJdoc(token: string, isDomestic: boolean, doc_list: string, title: string, to_dir_id: string, progress_id: string) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  const params: CombineJdocParams = {
    doc_list,
    title,
    progress_id
  }
  if (to_dir_id && to_dir_id !== 'root') {
    params.to_dir_id = to_dir_id
  }
  return createRequest(api).post<
    never,
    {
      error_code: number
      error_msg: string
      status: number
    }
  >('/combine_jdoc', params, {
    params: {
      token: token,
      with_copy: 1
    }
  })
}

export type MultiSearchResult = (TDocItem | TDirItem) & {
  matched_type: number
  matched_str: string
}

export function multiSearch(token: string, isDomestic: boolean, orderType: number, keywords: string) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).get<
    never,
    {
      error_code: number
      num: number
      list: MultiSearchResult[]
    }
  >('/multi_search', {
    params: {
      token: token,
      order_type: orderType,
      keywords: keywords
    }
  })
}

interface UploadPdfParams {
  token: string
  title: string
  id: string
  platform: string
  dir_id?: string
}

export function uploadPdf(token: string, isDomestic: boolean, title: string, id: string, fileData: any, dirId = '', platform = 'web') {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  const getParam: UploadPdfParams = {
    token,
    title,
    id,
    platform
  }
  if (dirId) {
    getParam['dir_id'] = dirId
  }
  return createRequest(api).post('/upload_pdf', fileData, {
    params: getParam
  })
}

export interface QueryDeletedListResult {
  dir_id?: string
  file_name?: string
  title: string
  upload_time: number
  create_time?: number
  dirs?: DeleteDirOptions[]
  first_page_id?: string
  page_num?: string
  doc?: {
    file_name: string
    first_page_id: string
    title: string
  }
  docs?: { doc_id: string }[]
  dir_id_1?: string
  dir_id_2?: string
  dir_id_3?: string
  dir_id_4?: string
  dir_id_5?: string
  dir_id_6?: string
}

export interface DeleteDirOptions {
  dir_id: string
  title: string
  create_time: number
  upload_time: number
  dirs?: DeleteDirOptions[]
  docs?: { doc_id: string }[]
}

export function queryDeletedList(token: string, isDomestic: boolean, orderType: number, start: number, isQueryAll: number) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).get<
    never,
    {
      num: number
      list?: QueryDeletedListResult[]
    }
  >('/query_deleted_dir_list', {
    params: {
      token: token,
      start_num: start,
      order_type: orderType,
      isQueryAll: isQueryAll
    }
  })
}

export interface DeletedJsonOptions {
  pages?: string[]
  docs?: string[]
  dirs?: {
    dir_id?: string
    docs?: {
      doc_id: string
    }[]
    dirs?: DeleteDirOptions[]
  }[]
}

export function deleteDeletedList(token: string, isDomestic: boolean, deletedJson: DeletedJsonOptions) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).post('/ignore_dir', deletedJson, {
    params: {
      token: token
    }
  })
}

export interface RevertDeletedListOptions {
  docs?: RecoverInfoOptions[]
  pages?: RecoverPagesOptions[]
  dirs?: QueryDeletedListResult[]
}

export interface RecoverInfoOptions {
  file_name: string
  dir_id_1?: string
  dir_id_2?: string
  dir_id_3?: string
  dir_id_4?: string
  dir_id_5?: string
  dir_id_6?: string
}

export interface RecoverPagesOptions {
  file_name: string
  doc_id: string
}

export function revertDeletedList(token: string, isDomestic: boolean, deletedArray: RevertDeletedListOptions, cancelToken: CancelToken) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).post('/revert_dir_list', deletedArray, {
    params: {
      token: token
    },
    cancelToken: cancelToken
  })
}

export function uploadChunk(data: any, params: any, cancel: any) {
  const sapi = createRequest('SAPI')
  const blob = new Blob([data])
  return sapi({
    method: 'post',
    url: `/bigfile/upload_slice?slice_id=${params.slice_id}&upload_time=0&req_id=${params.req_id}&file_id=${params.file_id}&token=${params.token}&total=${params.total}`,
    data: blob,
    cancelToken: cancel.token
  })
}

export function checkFile(data: any, params: any, cancel: any) {
  return createRequest('SAPI').post('/bigfile/check_file', data, {
    params: params,
    cancelToken: cancel.token
  })
}

export function addMultiFile(data: any, params: any, cancel: any) {
  return createRequest('SAPI').post('/web/add_multi_file', '', {
    params: params,
    cancelToken: cancel.token
  })
}

interface QueryShareAccountInfoParams {
  encrypt_id: string
  device_id?: string
  sid: string
  dir?: number
}

export interface QueryShareAccountInfoResult {
  nickname: string
  account: string
  area_code: number
  server_time: number
  dead_time: number
  day?: number
}

// 获取分享人信息
export function queryShareAccountInfo(params: QueryShareAccountInfoParams) {
  const fileDomesticFlag = params.sid.substring(params.sid.length - 2).toLocaleLowerCase() === 'cn'
  let fileDomestic: 'SAPI_JPG_DOMESTIC' | 'SAPI_JPG_FOREIGN' = 'SAPI_JPG_FOREIGN'
  if (fileDomesticFlag) {
    fileDomestic = 'SAPI_JPG_DOMESTIC'
  }
  return createRequest(fileDomestic).get<
    never,
    {
      ret: number
      msg: string
      data: QueryShareAccountInfoResult
    }
  >('/query_share_account_info', {
    params: params
  })
}

export function queryDeeplinkFromShare(shorten_id: string) {
  return createRequest('SAPI').get('/query_deeplink_from_share?platform=web&shorten_id=' + shorten_id, {
    params: shorten_id
  })
}

interface AutoCutParams {
  token: string
  card_pos: number //是否需要返回原图的自动切边坐标,0：不需要，1：需要
}

export interface UploadedItem {
  jpg_md5: string //图片md5
  page_id: string
  position: string //用于接口的 position。"w1,h1,w2,h2,x1,y1,x2,y2,x3,y3,x4,y4"，即原图宽高、切边宽高、四个切边点坐标
  upload_time: number //上传处理时间戳
}

// web 上传图片并切边
export function getImageAutoCut(data: Blob, params: AutoCutParams) {
  const { isDomestic } = useUserStore()
  const API = isDomestic ? 'EDAPI_DOMESTIC' : 'EDAPI_FOREIGN'
  return createRequest(API).post<
    never,
    {
      ret: number
      err: string
      data: UploadedItem
    }
  >('/image/image_handle/auto_cut', data, {
    params
  })
}

export interface EnhanceParams {
  /**
   * 图像切边处理坐标:12个逗号分隔的数字，其中第0和第1个是原图宽高。第2和第3个是切边图宽高。后面4～11个是角点坐标xy值，从左上点开始，顺时针方向
   */
  border?: string
  /**
   * 需要处理的图片(24位id + ".jpg"）
   */
  file_name?: string
  /**
   * 之前传过的md5 后台返回的
   */
  jpg_md5?: string
  /**
   * 图像增强处理类型：-1:图像不做处理,1:增亮(老照片修复),2:增强锐化,3:黑白,4:灰度,5:省墨
   */
  mode?: number
  /**
   * 图像按照中心旋转,角度参数，可选值0~360  e.g.  270，不需要带单位
   */
  rotate?: string
  /**
   * 用户token
   */
  token?: string
  /**
   * 0:只做图像处理，不保存，1:处理并保存
   */
  ttype?: number
  /**
   * 时间戳，后台返回的
   */
  upload_time?: number
  // 后台从哪里下载图片
  source?: string
}
// 图片裁剪 滤镜 旋转操作
// ttype 1 表示会保存图片
// 用于最后保存结果
export function enhancePng(params: EnhanceParams) {
  const { isDomestic } = useUserStore()
  const API = isDomestic ? 'EDAPI_DOMESTIC' : 'EDAPI_FOREIGN'
  return createRequest(API).get<
    never,
    {
      ret: number
      err: string
      data: {
        jpg_md5: string
        upload_time: number
        new_page_id: string
        result_pos: string // 用于接口的 position。"w1,h1,w2,h2,x1,y1,x2,y2,x3,y3,x4,y4"，即原图宽高、切边宽高、四个切边点坐标
      }
    }
  >('/image/image_handle/enhance', { params })
}

// 图片操作 但是不保存
// 返回blob
// 适合用户临时操作调用
export function enhancePngToBlob(params: EnhanceParams): Promise<Blob> {
  const { isDomestic } = useUserStore()
  const API = isDomestic ? 'EDAPI_DOMESTIC' : 'EDAPI_FOREIGN'
  return createRequest(API).get<never, Blob>('/image/image_handle/enhance', { params, responseType: 'blob' })
}

// 保存并生成文档
export interface SaveDocParams {
  images: Image[]
  title: string
  // 上传到的文件夹
  dir_id?: string
}

export interface Image {
  jpg_md5: string
  page_id?: string
  upload_time: number
  border: string //切边坐标 12个点
  ori_rotate: number //原图的旋转角度
}
export function saveDoc(params: any, data: SaveDocParams) {
  const { isDomestic } = useUserStore()
  const API = isDomestic ? 'EDAPI_DOMESTIC' : 'EDAPI_FOREIGN'
  return createRequest(API).post<
    never,
    {
      ret: number
      err: string
      data: {
        doc_id: string
      }
    }
  >('/image/image_handle/mass_upload', data, { params })
}

export interface downloadParams {
  token: string
  jpg_md5: string
  upload_time: number
}

export function downloadPic(params: downloadParams) {
  const { isDomestic } = useUserStore()
  const API = isDomestic ? 'EDAPI_DOMESTIC' : 'EDAPI_FOREIGN'
  return createRequest(API).get<never, Blob>('/image/image_handle/download', { params, responseType: 'blob' })
}

// 查询是否存在电子签名文件夹
export function esignDirQuery(params: { token: string }) {
  return createRequest('SAPI').get<
    never,
    {
      ret: number
      data: {
        exist: number
      }
    }
  >('/esign/exist', { params })
}

// pdf加密文档下载轮训状态发起

interface downPdfCommitOption {
  token: string
  doc_id: string
  upload_time: string
}
export function downPdfCommit(params: downPdfCommitOption) {
  return createRequest('SAPI').get<
    never,
    {
      ret: number
      err: string
      data: {
        task_id: string
      }
    }
  >('/bigfile/encrypt/pdf/commit', { params })
}

// pdf加密轮训状态查询

interface downPdfStatusOption {
  token: string
  doc_id: string
  task_id: string
}
export function downPdfStatus(params: downPdfStatusOption) {
  return createRequest('SAPI').get<
    never,
    {
      ret: number
      err: string
      data: {
        task_id: string
        url: string
      }
    }
  >('/bigfile/encrypt/pdf/status', { params })
}

interface preCheckOptionParam {
  token: string
  client_app?: string
  client?: string
  client_id?: string
  cs_ept_d?: string
  language?: string
  timestamp?: number
}
interface preCheckOption {
  docs?: string[] // 还原doc-id列表
  pages?: string[] // 还原page-id列表
}

export interface preCheckOptionResult {
  ret: number
  err: string
  data: {
    err_doc?: Array<{
      err_cnt: number
      file_name: string // 文档id
      title: string // 文档名称
    }>
    err_page?: Array<{
      file_name: string // 文档id
      title: string // 文档名称
    }>
  }
}

// 回收站还原预检查，检查是否存在损坏的文件未能还原
export function revert_pre_check(params: preCheckOptionParam, data: preCheckOption) {
  return createRequest('SAPI').post<never, preCheckOptionResult>('revert_pre_check', data, { params })
}

// 回收站还原时，检查到有损坏的文件，任然继续还原，将破损的文件删除
export function revert_delete_lose_page(params: preCheckOptionParam, data: preCheckOption) {
  return createRequest('SAPI').post<
    never,
    {
      ret: number
      err: string
      data?: object
    }
  >('revert_pre_fix', data, { params })
}

/**
 * 文件夹是否被加密 0 未加密 1 已加密
 */
export enum TDirEncrypted {
  Encrypted = '1',
  Unencrypted = '0'
}

/**
 * 文件夹类型
 * 0 普通文件夹
 * 101 成长记录
 * 102 公文包
 * 103 灵感库
 * 104 家庭文件收纳
 * 105 证件卡包
 * 201 模板文件夹-入职
 * 201 发票夹
 */
// todo 待补充共享文件夹逻辑 owner 和 duuid属性
export enum TDirType {
  'PUTONGWENJIANJIA' = 0,
  'CHENGZHANGJILU' = 101,
  'GONGWENBAO' = 102,
  'LINGANKU' = 103,
  'JIATINGWENJIANSHOUNA' = 104,
  'ZHENGJIAKABAO' = 105,
  'FAPIAOJIA' = 201
}

/**
 * dir结构
 * dir_id 安卓会有特殊文件夹dir_id=back_up
 * // todo 待补充共享文件夹逻辑 owner 和 duuid属性
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=375554090
 * @member title - 文件夹标题
 * @member dir_id - 文件夹id 后台生成 {}
 * @member docs - 文件夹下文档id数组
 * @member lock - 文件夹是否被加密 {@see TDirEncrypted}
 * @member create_time - 文件夹创建时间 如果没有modify_time 该字段可以代替
 * @member modify_time - 文件夹修改时间 如果只是创建文件夹后未更新 该字段不存在
 * @member upload_time - 文件夹版本号 客户端专用 不要使用和修改
 */
export interface TDirItem {
  title: string
  upload_time: number | string
  lock?: TDirEncrypted
  create_time: string | number
  dir_type: TDirType
  dir_id: string | 'root'
  dirs?: TDirItem[]
  docs?: {
    doc_id: TDocItem['file_name']
  }[]
  modify_time?: string | number
}

/**
 * 获取全量文件夹参数
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=375554090
 * @member token - 用户token
 * @member order_type - 排序类型
 * @member isDomestic - 是否是国内用户 会决定调用接口域名
 * @member show_advanced_dir - 是否展示高级文件夹 默认为1
 */
export interface TDirParams {
  token: string
  order_type: TOrderType
  isDomestic: boolean
  show_advanced_dir: number
}

/**
 * 获取全量文件夹返回结果
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=375554090
 * @member token - 用户token
 * @member isDomestic - 是否是国内用户 会决定调用接口域名
 * @member show_advanced_dir - 是否展示高级文件夹 默认为1
 */
export type TDirResponse =
  | {
      error_code: 0
      msg: string
      dirs: TDirItem[]
    }
  | {
      error_code: 101
      msg: string
    }

/**
 * 获取全量文件夹接口
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=375554090
 * @param params {TDirParams} 获取全量文件夹参数
 * @return {TDocItem} 文件夹结果
 */
export function getDirList(params: TDirParams): Promise<TDirResponse> {
  const { isDomestic } = params
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).get('/query_dir/v2', { params })
}

export enum TDirOpType {
  'ADD' = 1,
  'RENAME',
  'DELETE',
  'MOVE' = 6,
  'COPY'
}

export enum TDocOpType {
  'MOVE' = 4,
  'COPY'
}

/**
 * 操作文件夹参数
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40206988
 * @member token - 用户token
 * @member isDomestic - 是否是国内用户 会决定调用接口域名
 * @member op - 操作类型 删除3
 */
export interface TUpdateDirOrDocParams {
  token: string
  isDomestic: boolean
  op: TDirOpType | TDocOpType
}

/**
 * 删除文件夹参数
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40206988
 * @member dir_id - dir_id
 * @member upload_time - 文件夹版本号
 * @member title - 文件夹标题
 */
export type TDeleteDirData = Pick<TDirItem, 'dir_id' | 'upload_time' | 'title'>

/**
 * 移动文档参数
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40206988
 * @member from - 选中的文档所在的dir_id
 * @member to - 移动到的文件夹id 不传则移动到根目录
 */
export type TMoveDocData = {
  from: {
    dir_id: TDirItem['dir_id']
    doc_list: TDocItem['file_name'][]
  }
  to?: {
    dir_id: TDirItem['dir_id']
  }
}

/**
 * 复制文档参数
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40206988
 * @member from - 选中的文档所在的dir_id
 * @member to - 移动到的文件夹id 不传则移动到根目录
 */
export type TCopyDocData = {
  doc_id: TDocItem['file_name']
  to?: {
    dir_id: TDirItem['dir_id']
  }
}

/**
 * 复制文件夹参数
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40206988
 * @member from - 选中的文档所在的dir_id
 * @member to - 移动到的文件夹id 不传则移动到根目录
 */
export type TCopyDirData = {
  dir_id: TDirItem['dir_id']
  to?: {
    dir_id: TDirItem['dir_id']
  }
}

/**
 * 移动文件夹参数
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40206988
 * @member dir_id - 被移动的文件夹id
 * @member to - 需要移动到的文件夹id 不传则移动到根目录
 */
export type TMoveDirData = {
  dir_id: TDirItem['dir_id']
  to?: {
    dir_id: TDirItem['dir_id']
  }
}

/**
 * 重命名文件夹参数
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40206988
 * @member dir_id - dir_id
 * @member upload_time - 文件夹版本号
 * @member title - 文件夹标题
 */
export type TRenameDirData = TDeleteDirData

/**
 * 新增文件夹参数
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40206988
 * @member title - 文件夹标题
 * @member dir_id - 不是根目录必传
 */
export type TAddDirData = {
  dir: {
    title: TDirItem['title']
  }
  dir_id?: TDirItem['dir_id']
  title?: TDirItem['title']
}

/**
 * 没有返回
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40206988
 */
export type TUpdateDirResponse = {}

/**
 * 获取全量文件夹接口
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40206988
 * @param params {TUpdateDirOrDocParams} 文件夹操作参数
 * @param data { TDeleteDirData | TMoveDocData | TMoveDirData } 文件夹操作参数
 * @return {TDocItem} 文件夹结果
 */
export function updateDirNew(params: TUpdateDirOrDocParams, data: TDeleteDirData | TMoveDocData | TMoveDirData | TCopyDocData | TCopyDirData | TRenameDirData | TAddDirData): Promise<TUpdateDirResponse> {
  const { isDomestic } = params
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).post('/update_dir', data, { params })
}

export function addRecentDocNew(isDomestic: boolean, token: string, docId: string) {
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).post(
    '/add_view_file',
    [
      {
        file_name: `${docId}.jdoc`,
        timestamp: Math.floor(Date.now() / 1000)
      }
    ],
    {
      params: {
        token: token,
        device_id: 'web_camscanner'
      }
    }
  )
}

/**
 * 删除文档参数
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207246
 * @member token - 用户token
 * @member doc_id - doc_id
 * @member isDomestic - 是否是国内用户，用于确定调用接口域名
 */
export interface TDeleteDocParam {
  token: string
  doc_id: string
  isDomestic: boolean
}

/**
 * 删除文档返回结果
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207246
 * @member error_code - 响应状态码
 */
export type TDeleteDocResponse =
  | {
      error_code: 0
      status: number
    }
  | {
      error_msg: string
      error_code: 101
    }

/**
 * 删除文档接口
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207246
 * @param params {TDeleteDocParam} 获取全量文件夹参数
 * @return {TDeleteDocResponse} 文件夹结果
 */
export function deleteDocNew(params: TDeleteDocParam): Promise<TDeleteDocResponse> {
  const { isDomestic } = params
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).get('/delete_doc', { params })
}

/**
 * 请求文档页列表排序参数枚举值
 * @member sortByCreateDesc - 创建时间降序
 * @member sortByCreateAsc - 创建时间升序
 * @member sortByUpdateDesc - 修改时间降序
 * @member sortByUpdateAsc - 修改时间升序
 * @member sortByNameDesc - 名称降序
 * @member sortByNameAsc - 名称升序
 * @member sortByFileType - 文件类型 会按照图片 → PDF→ docx→ xlsx→ pptx 排序
 */
export enum TOrderType {
  sortByCreateDesc,
  sortByCreateAsc,
  sortByUpdateDesc,
  sortByUpdateAsc,
  sortByNameDesc,
  sortByNameAsc,
  sortByFileType
}

/**
 * 请求文档页列表参数
 * api地址  {@link https://doc.intsig.net/pages/viewpage.action?pageId=426148018}
 * @member token - 用户token
 * @member order_type - 文档排序方式
 * @member filter_esign - 是否过滤电子文档
 * @member isDomestic - 是否是国内分区
 */
export interface TGetDocListParam {
  token: string
  order_type: TOrderType
  filter_esign: number
  isDomestic: boolean
}

/**
 * 请求文档页列表参数
 * api地址  {@link https://doc.intsig.net/pages/viewpage.action?pageId=426148018}
 * @member title - 文档标题，显示给用户
 * // todo 确定file_name是否全局唯一
 * @member file_name - 后台定义文件名，带文件类型后缀，可以用于判断文档类型
 * @member cotoken - 协作人token 团队版专用
 * @member first_page_id - 如果是非电子文档 用该字段渲染文档头图
 * @member page_num - 文档数量
 * @member rotate - 文档旋转角度
 * @member tag_ids - 文档标签列表
 * @member modify_time - 页面更新时间
 * @member create_time - 文档创建时间
 * @member first_page_modify_time - 文档更新时间
 * @member flag - 没有flag 表示是根目录文档
 * @member password - 有该字段表示文档被加密 需要单独UI处理逻辑
 */
export interface TDocItem {
  title: string
  file_name: string
  cotoken: string
  first_page_id: string
  page_num: number
  rotate: number
  sort_key: string
  sort_key_list: string[]
  tag_ids: string
  modify_time: number
  create_time: number
  first_page_modify_time: number
  flag?: string
  password?: string
}

/**
 * 请求文档页列表可能的返回结果
 * api地址  {@link https://doc.intsig.net/pages/viewpage.action?pageId=426148018}
 * @member ret - ret = 0 成功  否则失败
 * @member msg - 请求返回信息
 * @member data - TDocList[] | {} 数据为空是后台需要处理{}为空数组
 */
export type TGetDocListResponse =
  | {
      error_code: 0
      error_msg: string
      data: {
        doc_list: TDocItem[]
      }
      user_id: string
    }
  | {
      error_code: 101
      error_msg: string
    }

/**
 * 获取全量文档列表
 *
 * api地址  {@link https://doc.intsig.net/pages/viewpage.action?pageId=426148018}
 * @param params {TGetDocListParam} 获取文档列表参数
 * @return {TDocItem} 文档结果
 */
export function getDocList(params: TGetDocListParam): Promise<TGetDocListResponse> {
  const { isDomestic } = params
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).get('/query_doc_list', { params })
}

/**
 * 更新文档参数
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207237
 * @member token - 用户token
 * @member docId - doc_id
 * @member title - 文档标题
 */
export interface TUpdateDocParam {
  isDomestic: boolean
  token: string
  docId: string
  title: string
}

export interface TUpdateDocResponse {
  error_msg: string
  error_code: number
  status: number
}

/**
 * 更新文档接口
 * @link https://doc.intsig.net/pages/viewpage.action?pageId=40207237
 * @param params {TUpdateDocParam} 更新文档参数
 * @return {TUpdateDocResponse} 更新文档结果
 */
export function updateDocNameNew(params: TUpdateDocParam): Promise<TUpdateDocResponse> {
  const { isDomestic, token, docId, title } = params
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).post('/update_doc', {
    check_title: 1,
    token: token,
    doc_id: docId,
    title: title
  })
}

/**
 * 通知后台生成pdf首图
 * @link https://web-api.intsig.net/project/2529/interface/api/308283
 */
export function genFirstPageOfPdf(params: { token: string; doc_id: string }): Promise<{
  ret: number
  data: {
    upload_time: number
    first_page_id: string
  }
  err: string
}> {
  return createRequest('SAPI').get('/bigfile/gen_pdf_thumb', { params })
}

export interface SplitRequest
  extends Array<{
    // 要提取的文档页面索引数组（所有拆分项都需要）
    page_index: number[]

    // 以下字段根据拆分类型二选一
    // 追加到现有文档时的目标文档ID
    append_doc_id?: string

    // 创建新文档时的目标目录ID
    dir_id?: string
    // 创建新文档时的标题
    title?: string
    // 需要删除原文档时候的数组
    pages?: number[]
  }> {}

export interface PdfBaseQuery {
  token: string
  doc_id: string
  timeStamp?: string
}

/**
 * @method PdfExtract 页面管理pdf 拆分和提取
 * @param params
 * @returns
 */
export function PdfExtract(
  params: {
    page_list: SplitRequest
  },
  query?: PdfBaseQuery
): Promise<{
  ret: number
  data: {}
  err: string
}> {
  const { isDomestic } = useUserStore()
  const API = isDomestic ? 'EDAPI_DOMESTIC' : 'EDAPI_FOREIGN'
  return createRequest(API).post(
    '/pdf_opt/extract',
    { ...params },
    {
      params: {
        ...query,
        timeStamp: Date.now()
      }
    }
  )
}

export interface ImportSourceBase {
  file_type: 'pdf' | 'image'
  doc_id?: string
  file_id?: string
  upload_time?: number
  width?: number
  height?: number
}

// 基本页面信息
export interface PageStatus {
  // 基础属性（必须）
  page: number
  page_id?: string
  // 操作属性（全部可选）
  move_to?: number // 移动操作
  rotate?: number // 旋转操作
  delete?: boolean // 删除操作
  import_from?: ImportSourceBase // 导入操作
}

/**
 * @method tempFileUpload 页面管理上传pdf/图片文件到临时空间
 * @param params
 * @returns
 */
export function tempFileUpload(
  file: any,
  query: {
    token: string
    doc_id: string
    file_type: 'pdf' | 'image'
  }
): Promise<{
  ret: number
  data: any
  err: string
}> {
  const { isDomestic } = useUserStore()
  const API = isDomestic ? 'EDAPI_DOMESTIC' : 'EDAPI_FOREIGN'
  return createRequest(API).post('/pdf_opt/upload', file, { params: { ...query, t: Date.now() } })
}

export interface SplitRequest
  extends Array<{
    // 要提取的文档页面索引数组（所有拆分项都需要）
    page_index: number[]

    // 以下字段根据拆分类型二选一
    // 追加到现有文档时的目标文档ID
    append_doc_id?: string

    // 创建新文档时的目标目录ID
    dir_id?: string
    // 创建新文档时的标题
    title?: string
    // 需要删除原文档时候的数组
    pages?: number[]
  }> {}

export interface PdfBaseQuery {
  token: string
  doc_id: string
  timeStamp?: string
}

export interface ImportSourceBase {
  file_type: 'pdf' | 'image'
  doc_id?: string
  file_id?: string
  upload_time?: number
  width?: number
  height?: number
}

// 基本页面信息
export interface PageStatus {
  // 基础属性（必须）
  page: number
  page_id?: string
  // 操作属性（全部可选）
  move_to?: number // 移动操作
  rotate?: number // 旋转操作
  delete?: boolean // 删除操作
  import_from?: ImportSourceBase // 导入操作
}
export interface PageListRequest {
  page_list: PageStatus[]
}
/**
 * @method saveCloudPdfEdit 页面管理pdf编辑保存
 * @param params
 * @returns
 */
export function saveCloudPdfEdit(
  params: PageListRequest,
  query: PdfBaseQuery
): Promise<{
  ret: number
  data: any
  err: string
}> {
  const { isDomestic } = useUserStore()
  const API = isDomestic ? 'EDAPI_DOMESTIC' : 'EDAPI_FOREIGN'
  return createRequest(API).post('/pdf_opt/edit', params, {
    params: {
      ...query,
      timeStamp: Date.now()
    }
  })
}

export interface WebOfficeResponse {
  refresh_token: string // 刷新令牌
  request_id: string // 请求 ID
  access_token: string // 访问令牌
  web_office_url: string // WebOffice URL
  access_token_expired_time: string // 访问令牌过期时间
  refresh_token_expired_time: string // 刷新令牌过期时间
  uploaded: number // 是否上传成功
}

export interface GetWebOfficeTokenParams {
  token: string
  doc_id: string
  action: string // preview/edit  默认 preview模式
  t: string
}
export interface GetWebOfficeTokenResponse {
  code: number
  data: WebOfficeResponse
  err: string
}
// 获取webOffice 的token
export function getWebOfficeToken(params: GetWebOfficeTokenParams): Promise<GetWebOfficeTokenResponse> {
  const { isDomestic } = useUserStore()
  const API = isDomestic ? 'SAPI_OFFICE_DOMESTIC' : 'SAPI_OFFICE_FOREIGN'
  return createRequest(API).get('/sync/weboffice/get_token', { params })
}

export interface RefreshTokenParams {
  access_token: string
  token: string
  doc_id: string
  t: string
  refresh_token: string
}
// 刷新webOffice 的token
export function refreshToken(params: RefreshTokenParams): Promise<{
  ret: number
  data: WebOfficeResponse
  err: string
}> {
  const { isDomestic } = useUserStore()
  const API = isDomestic ? 'SAPI_OFFICE_DOMESTIC' : 'SAPI_OFFICE_FOREIGN'
  return createRequest(API).get('/sync/weboffice/refresh_token', { params })
}

// pdf 解密发起
interface PdfDecryptParams {
  token: string
  doc_id: string
  upload_time: string | number
  password: string
  title: string
}

export function pdfDecrypt(params: PdfDecryptParams): Promise<{
  ret: number
  data: any
  err: string
}> {
  const { isDomestic } = useUserStore()
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).get('/bigfile/decrypt/pdf/commit', { params })
}

// pdf解密状态轮询
interface PdfDecryptStatusParams {
  token: string
  save: number
  task_id: string
  title: string
  dir_id: string | undefined
}

export function pdfDecryptStatus(params: PdfDecryptStatusParams): Promise<{
  ret: number
  data: any
  err: string
}> {
  const { isDomestic } = useUserStore()
  const api = isDomestic ? 'SAPI_DOMESTIC' : 'SAPI_FOREIGN'
  return createRequest(api).get('/bigfile/decrypt/pdf/status', { params })
}
