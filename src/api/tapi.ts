import createRequest from '../libs/request'

export interface QueryTeamListResult {
  team_token: string //团队token
  m_user_id: any //团队管理员user_id
  title: string //团队名称
  create_time: number //团队创建时间
  upload_time: number //团队自身更新时间，跟query_dir_list无关
  expire: number //团队过期时间，精确到毫秒
  content: {
    //团队其他信息
    pdf_ori: number //团队pdf方向（0：自动，1：纵向，2：横向）
    pdf_title: string //团队pdf尺寸类型名称
    pdf_size: string //团队pdf尺寸值（像素）
    review: number //审核功能，0：关闭，1：开放
    top_doc: number //团队根目录下放置文档，0：不允许，1：允许
  }
  area: number //团队所在区域，1表示国内，2表示国外，根据area决定调用国内外域名
  num?: number | string
  cur_num?: number | string
  quantity?: number | string
}

export function queryTeamList(isDomestic: string | boolean, token: string) {
  const api = isDomestic ? 'TAPI_DOMESTIC' : 'TAPI_FOREIGN'
  return createRequest(api).get<
    never,
    {
      status: number
      service_time: number
      list: QueryTeamListResult[]
    }
  >('/query_team_list', {
    params: {
      token: token,
      area: isDomestic ? 1 : 2
    }
  })
}

export interface QueryUserListResult {
  dir_id: string
  user_list: UserListChild[]
}

interface UserListChild {
  user_id: string | number //成员user_id,0表示为注册
  account: string //成员账号，若为空""，表示该账号已注销
  area_code: string //手机号国家码，若为手机注册账号，则有; 不是手机号注册，则返回""
  nickname: string //成员团队昵称
  permission: number //成员权限，若为 -16，表示删除，-8表示停用，其他表示新增修改
  create_time: number //成员加入团队或文件夹时间，精确到毫秒
  group_list: string[] //成员属于的分组列表
}

export function queryUserList(isDomestic: boolean, token: string, teamToken: string) {
  const api = isDomestic ? 'TAPI_DOMESTIC' : 'TAPI_FOREIGN'
  return createRequest(api).get<
    never,
    {
      upload_time: number
      list?: QueryUserListResult[]
    }
  >('/query_user_list', {
    params: {
      token: token,
      team_token: teamToken
    }
  })
}

export interface QueryDirListResult {
  layer?: number
  parent?: string
  teamToken?: string
  dir_id: string
  title: string
  create_time: number
  upload_time: number
  permission: number
  dirs?: QueryDirListResult[]
  docs?: QueryDirListDocsResult[]
}

interface QueryDirListDocsResult {
  doc_id: string
  upload_time: number
  user_id: number
  account: string
  nickname: string
  permission: number
}

export function queryDirList(isDomestic: boolean, token: string, teamToken: string, sort: number) {
  const api = isDomestic ? 'TAPI_DOMESTIC' : 'TAPI_FOREIGN'
  return createRequest(api).get<
    never,
    {
      list?: QueryDirListResult[]
    }
  >('/query_dir_list', {
    params: {
      token: token,
      team_token: teamToken,
      order_type: sort
    }
  })
}

interface SetPermissionOptions {
  user_id?: string
  permission: number
  op_account?: string
  account?: string
  area_code?: string
  dir_id?: string
  doc_id?: string
  op_area_code?: string
}

export function setPermission(isDomestic: boolean, token: string, teamToken: string, postData: SetPermissionOptions[]) {
  const api = isDomestic ? 'TAPI_DOMESTIC' : 'TAPI_FOREIGN'
  return createRequest(api).post<
    never,
    {
      error_code: number
      error_msg: string
      status: number
    }
  >(`/set_permission?token=${token}&team_token=${teamToken}`, postData)
}
