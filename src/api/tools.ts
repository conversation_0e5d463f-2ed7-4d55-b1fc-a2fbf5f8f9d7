import createRequest from '../libs/request'

interface QueryShareLinkResult {
  share_info: {
    doc_id: string //单文档分享 和 单文档多页分享时存在该字段
    pages: string //当前文档分享需要展示的page列表，有序
    create_time: string
    dead_time: string
    encrypt: string //前端依据该字段值是否为空字符串判断是否加密，空字符串：未加密，不为空：加密
    share_device: string // 分享者设备device_id
  }
  doc_info: {
    page_list: {
      modify_time: string
      title: string
      note: string
      rotate: number
      file_name: string
    }[]
  }
}

export function query_share_info_with_link(encrypt_id: string, link: string, is_domestic: boolean) {
  if (is_domestic) {
    return createRequest('SAPI_DOMESTIC').get<
      never,
      {
        error_code: number
        data: QueryShareLinkResult
      }
    >('/query_share_info_with_link', {
      params: {
        encrypt_id,
        link
      }
    })
  } else {
    return createRequest('SAPI_FOREIGN').get<
      never,
      {
        error_code: number
        data: QueryShareLinkResult
      }
    >('/query_share_info_with_link', {
      params: {
        encrypt_id,
        link
      }
    })
  }
}

interface CloudOcrResult {
  points: string | number
  ocr_user_text: string
}

export function cloud_ocr(token: string, file_name: string, is_domestic: boolean) {
  if (is_domestic) {
    return createRequest('SAPI_DOMESTIC').get<never, CloudOcrResult>('/cloud_ocr', {
      params: {
        token: token,
        file_name: file_name + '.jpage'
      }
    })
  } else {
    return createRequest('SAPI_FOREIGN').get<never, CloudOcrResult>('/cloud_ocr', {
      params: {
        token: token,
        file_name: file_name + '.jpage'
      }
    })
  }
}

export function update_ocr(token: string, file_name: string, ocr: string, is_domestic: boolean) {
  if (is_domestic) {
    return createRequest('SAPI_DOMESTIC').post('/update_ocr', ocr, {
      params: {
        token: token,
        file_name: file_name + '.jpage',
        manual: 1
      }
    })
  } else {
    return createRequest('SAPI_FOREIGN').post('/update_ocr', ocr, {
      params: {
        token: token,
        file_name: file_name + '.jpage',
        manual: 1
      }
    })
  }
}

export interface QueryOcrListResult {
  page_id: string
  ocr: string
  doc_id: string
  note: string
}

export function query_ocr_list(token: string, doc_id: string, is_domestic: boolean) {
  if (is_domestic) {
    return createRequest('SAPI_DOMESTIC').get<
      never,
      {
        data: {
          ocr_list: QueryOcrListResult[]
        }
      }
    >('/query_ocr_list', {
      params: {
        token: token,
        doc_id: doc_id
      }
    })
  } else {
    return createRequest('SAPI_FOREIGN').get<
      never,
      {
        data: {
          ocr_list: QueryOcrListResult[]
        }
      }
    >('/query_ocr_list', {
      params: {
        token: token,
        doc_id: doc_id
      }
    })
  }
}

export function add_word_share(token: string, file_name: string, ocr: string) {
  return createRequest('OAPI').post<
    never,
    {
      link: string
    }
  >(
    '/add_word_share',
    { data: ocr },
    {
      params: {
        token: token
        // file_name: file_name
      }
    }
  )
}

export function delete_doc(token: string, doc_id: string, is_domestic: boolean) {
  if (is_domestic) {
    return createRequest('SAPI_DOMESTIC').get('/delete_doc', {
      params: {
        token: token,
        doc_id: doc_id
      }
    })
  } else {
    return createRequest('SAPI_FOREIGN').get('/delete_doc', {
      params: {
        token: token,
        doc_id: doc_id
      }
    })
  }
}

interface QueryOcrDocListResult {
  doc_id: string
  upload_time: string
}

export function query_ocr_doc_list(token: string, is_domestic: boolean) {
  if (is_domestic) {
    return createRequest('SAPI_DOMESTIC').get<
      never,
      {
        error_code: number
        status: number
        error_msg: number
        data: {
          doc_list: QueryOcrDocListResult[]
        }
      }
    >('/query_ocr_doc_list', {
      params: {
        token
      }
    })
  } else {
    return createRequest('SAPI_FOREIGN').get('/query_ocr_doc_list', {
      params: {
        token
      }
    })
  }
}

interface UploadJpgResult {
  doc_id: string
  page_id: string
}

export function upload_jpg(token: string, data: any, doc_id: string, is_domestic: boolean, pages = '', last = '') {
  const params: {
    token: string
    pages: string
    doc_id?: string
    last?: number
  } = {
    token: token,
    pages: pages
  }
  if (doc_id) {
    params['doc_id'] = doc_id
  }
  if (last) {
    params['last'] = 1
  }
  if (is_domestic) {
    return createRequest('SAPI_DOMESTIC').post<never, UploadJpgResult>('/upload_jpg', data, {
      params: params
    })
  } else {
    return createRequest('SAPI_FOREIGN').post<never, UploadJpgResult>('/upload_jpg', data, {
      params: params
    })
  }
}

export function download_file_blob(token: string, sid: string, type: string) {
  return createRequest('DAPI').get<never, Blob>('/download_file', {
    params: {
      token: token,
      sid: sid,
      type: type,
      platform: 'web'
    },
    responseType: 'blob'
  })
}