import createRequest from '@/libs/request'
import type { AxiosProgressEvent } from 'axios'

export function uploadFile(file: File, progressCallback: (event: AxiosProgressEvent) => void) {
  return createRequest('GPTS_API')
    .post<
      never,
      {
        ret: number
        data: {
          file_id: string
        }
      }
    >('/file/upload', file, {
      onUploadProgress(event) {
        progressCallback && progressCallback(event)
      }
    })
    .then(res => {
      if (res.ret) {
        throw res
      }
      return res.data.file_id
    })
}
