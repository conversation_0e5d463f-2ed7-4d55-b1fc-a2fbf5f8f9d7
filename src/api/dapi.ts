import type { CancelTokenSource } from 'axios'
import qs from 'qs'

import createRequest from '../libs/request'

interface UploadPdfParams {
  title: string
  platform: string
  doc_id?: string
  type?: string
  size?: string
  token?: string
}

export function uploadPdf(token: string, file: any, size: string, title: string, cancel: CancelTokenSource, doc_id: string, platform = 'web', type = '') {
  const params: UploadPdfParams = {
    title,
    platform
  }
  let api: 'SAPI_DOC_FOREIGN' | 'DAPI' = 'DAPI'
  if (doc_id) {
    params.doc_id = doc_id
  }
  if (type) {
    params.type = type
  }
  if (size) {
    params.size = size
  }
  if (token) {
    params.token = token
  }
  if (token) {
    api = 'SAPI_DOC_FOREIGN'
  }
  if (doc_id) {
    return createRequest(api).get<never, UploadImgResult>('/upload_pdf', {
      params: params,
      cancelToken: cancel.token
    })
  } else {
    return createRequest(api).post<never, UploadImgResult>('/upload_pdf', file, {
      params: params,
      cancelToken: cancel.token
    })
  }
}

interface UploadImgParams {
  title: string
  platform: string
  type: string
  page_id?: string
  size?: string
  token?: string
}

export interface UploadImgResult {
  ret: number
  err: string
  data: {
    file_id: string
  }
}

export function uploadImg(token: string, file: string, size: string, title: string, cancel: CancelTokenSource, page_id: string, platform = 'web') {
  const params: UploadImgParams = {
    title,
    platform,
    type: 'images2pdf'
  }
  if (page_id) {
    params.page_id = page_id
  }
  if (size) {
    params.size = size
  }
  if (token) {
    params.token = token
  }
  let api: 'SAPI_DOC_FOREIGN' | 'DAPI' = 'DAPI'
  if (token) {
    api = 'SAPI_DOC_FOREIGN'
  }
  if (page_id) {
    return createRequest(api).get<never, UploadImgResult>('/upload_image', {
      params: params,
      cancelToken: cancel.token
    })
  } else {
    return createRequest(api).post<never, UploadImgResult>('/upload_image', file, {
      params: params,
      cancelToken: cancel.token
    })
  }
}

export function uploadPdfTo(data: { page_list: string[] }, params: any) {
  // TODO:
  return createRequest('SAPI_DOC_FOREIGN').post<never, UploadImgResult>('/upload_pdf', data, {
    params: params
  })
}

interface ConvertFileParams {
  title: string
  platform: string
  type: string
  file_id?: string
  token?: string
  doc_id?: string
}

export function convertFile(token: string, title: string, fileId: string, docId: string, type: string, platform = 'web', cancel: CancelTokenSource) {
  const params: ConvertFileParams = {
    title,
    platform,
    type
  }
  if (token) {
    params.token = token
  }
  if (fileId !== '') {
    params.file_id = fileId
  } else {
    params.doc_id = docId
  }

  // let otherToPdf = ['word2pdf', 'excel2pdf', 'ppt2pdf', 'images2pdf']
  let api: 'SAPI_DOC_FOREIGN' | 'DAPI' = 'DAPI'
  if (token) {
    api = 'SAPI_DOC_FOREIGN'
  }
  return createRequest(api).get<
    never,
    {
      ret: number
      err: string
      data: {
        sid?: string
        encrypt_id?: string
        type: string
        file_id?: string
      }
    }
  >('/pdf_convert', {
    params: params,
    cancelToken: cancel.token
  })
}

// pdf文件转换取消
export function cancelPdfConvert(token: string, file_id: string) {
  const params = {
    token,
    file_id,
    platform: 'web'
  }
  let api: 'SAPI_DOC_DOMESTIC' | 'DAPI' = 'DAPI'
  if (token) {
    api = 'SAPI_DOC_DOMESTIC'
  }
  return createRequest(api).get<
    never,
    {
      code: number
      msg: string
    }
  >('/pdf_convert_cancel', {
    params: params
  })
}

interface ConvertPostFileParams {
  title: string
  platform: string
  type: string
  t: number
  file_id?: string
  token?: string
  combine_pages?: number[]
  rotate_pages?: number[]
  rotate_angles?: number[]
}

export interface ConvertPostFileData {
  file_ids?: string[]
  combine_pages?: number[]
  rotate_pages?: number[]
  rotate_angles?: number[]
}

export function convertPostFile(token: string, title: string, type: string, file_id: string, platform = 'web', data: ConvertPostFileData, cancel: CancelTokenSource) {
  const params: ConvertPostFileParams = {
    title,
    platform,
    type,
    t: new Date().getTime()
  }
  if (file_id) {
    params.file_id = file_id
  }
  if (token) {
    params.token = token
  }
  // let otherToPdf = ['word2pdf', 'excel2pdf', 'ppt2pdf', 'images2pdf']
  let api: 'SAPI_DOC_FOREIGN' | 'DAPI' = 'DAPI'
  if (token) {
    api = 'SAPI_DOC_FOREIGN'
  }
  return createRequest(api).post<
    never,
    {
      ret: number
      err: string
      data: {
        sid: string
        encrypt_id: string
        type: string
      }
    }
  >(`/pdf_convert?${qs.stringify(params)}`, data, {
    cancelToken: cancel.token
  })
}

interface UploadWordParams {
  size: string
  title: string
  platform: string
  doc_id?: string
  token?: string
}

export function uploadWord(token: string, file: any, size: string, title: string, cancel: CancelTokenSource, doc_id: string, platform = 'web') {
  const params: UploadWordParams = {
    size,
    title,
    platform
  }
  if (doc_id) {
    params.doc_id = doc_id
  }
  if (token) {
    params.token = token
  }
  let api: 'SAPI_DOC_FOREIGN' | 'DAPI' = 'DAPI'
  if (token) {
    api = 'SAPI_DOC_FOREIGN'
  }
  if (doc_id) {
    return createRequest(api).get<never, UploadImgResult>('/upload_word', {
      params: params,
      cancelToken: cancel.token
    })
  } else {
    return createRequest(api).post<never, UploadImgResult>('/upload_word', file, {
      params: params,
      cancelToken: cancel.token
    })
  }
}

export function uploadExcel(token: string, file: any, size: string, title: string, cancel: CancelTokenSource, doc_id: string, platform = 'web') {
  const params: UploadWordParams = {
    size,
    title,
    platform
  }
  if (doc_id) {
    params.doc_id = doc_id
  }
  if (token) {
    params.token = token
  }
  let api: 'SAPI_DOC_FOREIGN' | 'DAPI' = 'DAPI'
  if (token) {
    api = 'SAPI_DOC_FOREIGN'
  }
  if (doc_id) {
    return createRequest(api).get<never, UploadImgResult>('/upload_excel', {
      params: params,
      cancelToken: cancel.token
    })
  } else {
    return createRequest(api).post<never, UploadImgResult>('/upload_excel', file, {
      params: params,
      cancelToken: cancel.token
    })
  }
}

export function uploadPpt(token: string, file: any, size: string, title: string, cancel: CancelTokenSource, doc_id: string, platform = 'web') {
  const params: UploadWordParams = {
    size,
    title,
    platform
  }
  if (doc_id) {
    params.doc_id = doc_id
  }
  if (token) {
    params.token = token
  }
  let api: 'SAPI_DOC_FOREIGN' | 'DAPI' = 'DAPI'
  if (token) {
    api = 'SAPI_DOC_FOREIGN'
  }
  if (doc_id) {
    return createRequest(api).get<never, UploadImgResult>('/upload_ppt', {
      params: params,
      cancelToken: cancel.token
    })
  } else {
    return createRequest(api).post<never, UploadImgResult>('/upload_ppt', file, {
      params: params,
      cancelToken: cancel.token
    })
  }
}

interface EmailShareParams {
  title: string
  sid: string
  type: string
  platform: string
  to_account: string
  token?: string
  encrypt_id?: string
}

export function emailShare(token: string, encrypt_id: string, title: string, sid: string, type: string, platform = 'web', to_account: string) {
  const params: EmailShareParams = {
    title,
    sid,
    type,
    platform,
    to_account
  }
  if (token) {
    params.token = token
  }
  let api: 'SAPI_DOC_FOREIGN' | 'DAPI' = 'DAPI'
  if (token) {
    api = 'SAPI_DOC_FOREIGN'
  }
  if (encrypt_id) {
    params.encrypt_id = encrypt_id
  }
  return createRequest(api).get<
    never,
    {
      ret: number
      err: string
    }
  >('/send_email_share_file', {
    params
  })
}

interface ModifyTitleParams {
  title: string
  sid: string
  type: string
  platform: string
  token?: string
}

export function modifyTitle(token: string, title: string, sid: string, type: string, platform = 'web') {
  const params: ModifyTitleParams = {
    title,
    sid,
    type,
    platform
  }
  if (token) {
    params.token = token
  }
  let api: 'SAPI_DOC_FOREIGN' | 'DAPI' = 'DAPI'
  if (token) {
    api = 'SAPI_DOC_FOREIGN'
  }
  return createRequest(api).get('/modify_file_title', {
    params
  })
}

export interface ToolsUploadOption {
  token?: string
  size: string
  title: string
  page_id: string
  platform: 'web'
  doc_id: string
  type: string
}
export function toolsUploadImg(params: ToolsUploadOption, file: File, cancel: CancelTokenSource) {
  let api: 'SAPI_DOC_FOREIGN' | 'DAPI' = 'DAPI'
  if (params.token) {
    api = 'SAPI_DOC_FOREIGN'
  } else {
    delete params.token
  }
  if (params.page_id) {
    return createRequest(api).get<never, UploadImgResult>('/upload_image', {
      params: params,
      cancelToken: cancel.token
    })
  } else {
    return createRequest(api).post<never, UploadImgResult>('/upload_image', file, {
      params: params,
      cancelToken: cancel.token
    })
  }

}

export function toolsUploadPdf(params: ToolsUploadOption, file: File, cancel: CancelTokenSource) {
  let api: 'SAPI_DOC_FOREIGN' | 'DAPI' = 'DAPI'
  if (params.token) {
    api = 'SAPI_DOC_FOREIGN'
  } else {
    delete params.token
  }
  if (params.doc_id) {
    return createRequest(api).get<never, UploadImgResult>('/upload_pdf', {
      params: params,
      cancelToken: cancel.token
    })
  } else {
    return createRequest(api).post<never, UploadImgResult>('/upload_pdf', file, {
      params: params,
      cancelToken: cancel.token
    })
  }
}

export function toolsUploadWord(params: ToolsUploadOption, file: File, cancel: CancelTokenSource) {
  let api: 'SAPI_DOC_FOREIGN' | 'DAPI' = 'DAPI'
  if (params.token) {
    api = 'SAPI_DOC_FOREIGN'
  } else {
    delete params.token
  }
  return createRequest(api).post<never, UploadImgResult>('/upload_word', file, {
    params: params,
    cancelToken: cancel.token
  })
}

export function toolsUploadPpt(params: ToolsUploadOption, file: File, cancel: CancelTokenSource) {
  let api: 'SAPI_DOC_FOREIGN' | 'DAPI' = 'DAPI'
  if (params.token) {
    api = 'SAPI_DOC_FOREIGN'
  } else {
    delete params.token
  }
  return createRequest(api).post<never, UploadImgResult>('/upload_ppt', file, {
    params: params,
    cancelToken: cancel.token
  })
}

export function toolsUploadExcel(params: ToolsUploadOption, file: File, cancel: CancelTokenSource) {
  let api: 'SAPI_DOC_FOREIGN' | 'DAPI' = 'DAPI'
  if (params.token) {
    api = 'SAPI_DOC_FOREIGN'
  } else {
    delete params.token
  }
  return createRequest(api).post<never, UploadImgResult>('/upload_excel', file, {
    params: params,
    cancelToken: cancel.token
  })
}

export interface PdfConvertOption {
  token?: string
  title: string
  type: string
  doc_id?: string
  file_id: string
  platform: 'web'
}

export function pdfConvert(params: PdfConvertOption, data: ConvertPostFileData, cancel: CancelTokenSource) {
  let api: 'SAPI_DOC_FOREIGN' | 'DAPI' = 'DAPI'
  if (params.token) {
    api = 'SAPI_DOC_FOREIGN'
  } else {
    delete params.token
  }
  return createRequest(api).post<
    never,
    {
      ret: number
      err: string
      data: {
        file_id: string
        sid: string
        encrypt_id: string
        type: string
      }
    }
  >(`/pdf_convert`, data, {
    params: params,
    cancelToken: cancel.token
  })
}
