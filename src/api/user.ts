import createRequest from '../libs/request'
import config from '@/config'
import type { PropertyData } from './csapi'

export function testApi() {
  return createRequest('UAPI').post<
    never,
    {
      ret: number
      data: boolean
    }
  >('/test-api')
}

export function logout(token: string) {
  return createRequest('UAPI').get('/logout', {
    params: {
      token
    }
  })
}

interface QueryVipPropertyOption {
  token: string
  property_id?: string
}

export function queryVipProperty(token: string, property_id: string = '') {
  const params: QueryVipPropertyOption = {
    token: token
  }
  if (property_id) {
    params.property_id = property_id
  }
  return createRequest('PAPI').get<
    never,
    {
      ret: string
      data: PropertyData
    }
  >('/query_property', {
    params: params
  })
}

export function shareDownload(downloadUrl: string, fileName: string) {
  return createRequest('OLD_WEB_API_SHARE').post<
    never,
    any // TODO:
  >(
    '/queryDownloadUrl',
    {
      show_url: downloadUrl,
      file_name: fileName
    },
    {}
  )
}

export function qrInit(typeArray: string[]) {
  return createRequest('API_QR_LOGIN').get<
    never,
    {
      qrl_id: string
    }
  >('/init', {
    params: {
      host: config.macro.CAMSCANNER_QRCODE_HOST,
      types: typeArray ? typeArray.join('|') : ''
    }
  })
}

interface QrPushData {
  qrl_id: string
  token: string
  status: number
}

interface QrPushParams {
  domain: string
  type: string
  token: string
}

export function qrPush(data: QrPushParams, params: QrPushData) {
  return createRequest('API_QR_LOGIN').post('/push', data, {
    params: params
  })
}

export interface QrQueryRseult {
  status: number
  type: string
  file_link: string
  file_type: string
  file_name: string
  create_time: string
  token: string
  apis: any
  file_id?: string
  doc_type?: string
  from?: string
}

export function qrQuery(qrl_id: string) {
  return createRequest('API_QR_LOGIN').get<
    never,
    {
      ret: number
      data: QrQueryRseult
    }
  >('/query', {
    params: {
      qrl_id: qrl_id
    }
  })
}

export interface GetProductInfoOption {
  token: string
  business_type: string
  currency: string
  payload: {
    pay_type: string
    product_id: string
    quantity: number
    product_info: {
      membership_years: number
      space_gb_num?: number
    }
  }
}

export function getProductInfo(data: GetProductInfoOption) {
  return createRequest('PAPI').post<
    never,
    {
      ret: string
      data: {
        total_amount: string
        price: string
      }
    }
  >('/pay/get_product_info', data)
}

export interface LoginByTokenResult {
  token: {
    token: string
    expire: number
  }
  user: {
    gid: string
    uid: string
  }
}

export function loginByToken(token: string) {
  return createRequest('UAPI').get<never, LoginByTokenResult>('/user_info2', {
    params: {
      token: token,
      client_app: 'web_camscanner@5.8.5'
    }
  })
}

export interface QueryOrderListResult {
  order_id: string
  product_id: string
  quantity?: string
  currency: string
  total_amount?: string
  create_time: string
  price?: string
  amount?: string
  selection?: boolean
  product_name?: string
}

// 查询可开票订单列表
export function queryOrderList(params: { token: string; page: number; property: string }) {
  return createRequest('PAPI').get<
    never,
    {
      ret: string
      err: string
      data: QueryOrderListResult[]
    }
  >('/billing/query_order_list', {
    params: params
  })
}

export interface BillingApplyData {
  order_list: string[]
  recipient_name: string
  recipient_email: string
  mobile: string
  invoice_type: number
  issue_type: number
  location: string
  address: string
  entpr_info?: {
    name: string
    tax_no: string
    address: string
    mobile: string
    opening_bank: string
    bank_account: string
  }
}

// 申请开票
export function billingApply(data: BillingApplyData, params: { token: string }) {
  return createRequest('PAPI').post<
    never,
    {
      ret: string
      err: string
      data: {
        billing_id: string
      }
    }
  >('/billing/apply', data, {
    params: params
  })
}

export interface BillingQueryListResult {
  total_amount: string
  invoice_type: number
  issue_type: number
  status: number
  tracking_no: string
  billing_id: string
  reason: string
  create_time: string
  elec_invoice_id: string
  download_url: string
}

// 查询已开票列表
export function billingQueryList(params: { token: string; page: number; offst: number }) {
  return createRequest('PAPI').get<
    never,
    {
      ret: string
      err: string
      data: BillingQueryListResult[]
    }
  >('/billing/query_list', {
    params: params
  })
}

// 开票发送邮箱
export function billingSendEmail(params: { token: string; email: string; billing_id: string }) {
  return createRequest('PAPI').get<
    never,
    {
      ret: string
      err: string
    }
  >('/billing/send_email', {
    params: params
  })
}

// 取消开票
export function billingCancel(params: { token: string; billing_id: string }) {
  return createRequest('PAPI').get<
    never,
    {
      ret: string
      err: string
    }
  >('/billing/cancel', {
    params: params
  })
}

// 获取绑定列表
export function getBindList(token: string, from_type?: string) {
  return createRequest('UAPI').get<
    never,
    {
      ret: number
      data: {
        [key: string]: {
          account: string
          type: string
          nickname?: string
          area_code?: string
        }
      }
    }
  >('/bind_list', {
    params: {
      token,
      from_type: from_type ? from_type : ''
    }
  })
}

export interface CloseAccountOptions {
  email?: string //        邮箱
  mobile?: string //        手机号（邮箱和手机号必传其一）
  area_code?: string //        账号为手机号时必传
  token: string //        当前用户token
  timestamp: string //        请求时间戳
  property_id: string //        操作类型
  client_app: string //        <EMAIL>
  device_id: string //        设备号
  client_id: string //
  client: string //
  country: string //       国家码，如：cn
  sign?: string //        签名信息
  sign_type?: string //       签名方式（默认md5）
  emailEncrypt?: string //       加密邮箱
  mobileEncrypt?: string //       加密手机
  from: string
}
// 账号注销
export function closeAccount(params: CloseAccountOptions) {
  return createRequest('UAPI').get('/close_account', {
    params
  })
}
