<template>
  <div class="scan-complete-container">
    <!-- Header Section -->
    <div class="header-section">
      <div class="success-icon">
        <div class="icon-background"></div>
        <div class="check-mark"></div>
      </div>
      <h1 class="completion-title">扫描完成，已扫描120张</h1>
    </div>

    <!-- Statistics Section -->
    <div class="statistics-section">
      <!-- Success Count -->
      <div class="stat-item">
        <div class="stat-number success">120</div>
        <div class="stat-label">识别成功(张)</div>
      </div>

      <!-- Divider -->
      <div class="stat-divider"></div>

      <!-- Failed Count -->
      <div class="stat-item">
        <div class="stat-number failed">0</div>
        <div class="stat-label">识别失败(张)</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ScanComplete',
  props: {
    successCount: {
      type: Number,
      default: 120
    },
    failedCount: {
      type: Number,
      default: 0
    },
    totalCount: {
      type: Number,
      default: 120
    }
  },
  computed: {
    completionText() {
      return `扫描完成，已扫描${this.totalCount}张`
    }
  },
  methods: {
    onContinue() {
      this.$emit('continue')
    },
    onRetry() {
      this.$emit('retry')
    }
  },
  emits: ['continue', 'retry']
}
</script>

<style scoped>
.scan-complete-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 60px;
  padding: 40px;
  background: #FFFFFF;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  max-width: 800px;
  margin: 0 auto;
}

/* Header Section */
.header-section {
  display: flex;
  align-items: center;
  gap: 18px;
}

.success-icon {
  width: 72px;
  height: 72px;
  position: relative;
  flex-shrink: 0;
}

.icon-background {
  width: 72px;
  height: 72px;
  background: #FFFFFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-mark {
  position: absolute;
  top: 3px;
  left: 3px;
  width: 66px;
  height: 66px;
  background: #04AA65;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-mark::after {
  content: '✓';
  color: #FFFFFF;
  font-size: 36px;
  font-weight: bold;
}

.completion-title {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 44px;
  line-height: 1.36em;
  color: #262626;
  margin: 0;
  white-space: nowrap;
}

/* Statistics Section */
.statistics-section {
  display: flex;
  align-items: center;
  gap: 120px;
  padding: 80px 120px;
  background: #FFFFFF;
  border-radius: 32px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  width: 204px;
}

.stat-number {
  font-family: 'zihunbiantaoti', 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 120px;
  line-height: 1.2em;
  text-align: center;
  width: 100%;
}

.stat-number.success {
  color: #141414;
}

.stat-number.failed {
  color: #BFBFBF;
}

.stat-label {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 36px;
  line-height: 1.33em;
  color: #595959;
  text-align: center;
  width: 100%;
}

.stat-divider {
  width: 2px;
  height: 72px;
  background: #BFBFBF;
  flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .scan-complete-container {
    gap: 40px;
    padding: 24px;
  }

  .header-section {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .success-icon {
    width: 60px;
    height: 60px;
  }

  .icon-background {
    width: 60px;
    height: 60px;
  }

  .check-mark {
    width: 54px;
    height: 54px;
  }

  .check-mark::after {
    font-size: 28px;
  }

  .completion-title {
    font-size: 32px;
    white-space: normal;
    text-align: center;
  }

  .statistics-section {
    gap: 60px;
    padding: 40px 60px;
    flex-direction: column;
  }

  .stat-item {
    width: 180px;
  }

  .stat-number {
    font-size: 80px;
  }

  .stat-label {
    font-size: 24px;
  }

  .stat-divider {
    width: 72px;
    height: 2px;
    transform: rotate(90deg);
  }
}

@media (max-width: 480px) {
  .completion-title {
    font-size: 24px;
  }

  .statistics-section {
    gap: 40px;
    padding: 24px 40px;
  }

  .stat-number {
    font-size: 60px;
  }

  .stat-label {
    font-size: 18px;
  }

  .stat-item {
    width: 150px;
  }
}

/* Animation */
.scan-complete-container {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-number {
  animation: countUp 1s ease-out 0.3s both;
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.check-mark {
  animation: checkMarkAppear 0.5s ease-out 0.2s both;
}

@keyframes checkMarkAppear {
  from {
    opacity: 0;
    transform: scale(0);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>