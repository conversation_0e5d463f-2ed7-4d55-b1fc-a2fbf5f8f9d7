<template>
  <div class="design-v2">
    <div class="layout">
      <!-- Frame 1 -->
      <div class="frame-row">
        <div class="square gray"></div>
        <div class="square purple"></div>
      </div>
      
      <!-- Frame 2 -->
      <div class="frame-row">
        <div class="square gray"></div>
        <div class="square gray"></div>
      </div>
      
      <!-- Group 1321317659 (SVG Vector) -->
      <div class="vector-group">
        <img src="/images/i-ve-changed.svg" alt="I've changed" class="vector-icon" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DesignV2'
}
</script>

<style scoped>
.design-v2 {
  width: 194px;
  height: 292px;
  border: 2px solid #000000;
  box-sizing: content-box;
}

.layout {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

.frame-row {
  display: flex;
  flex-direction: row;
  gap: 12px;
}

.square {
  width: 75px;
  height: 75px;
}

.square.gray {
  background-color: #D9D9D9;
}

.square.purple {
  background-color: #7B61FF;
}

.vector-group {
  width: 162px;
  height: 77.25px;
  position: relative;
}

.vector-icon {
  width: 100%;
  height: 100%;
  display: block;
}
</style>