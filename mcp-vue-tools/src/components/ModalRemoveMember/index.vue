<template>
  <div class="modal-overlay">
    <div class="modal-container">
      <!-- Header -->
      <div class="modal-header">
        <h2 class="modal-title">移除成员</h2>
        <button class="close-btn" @click="$emit('close')">
          <div class="close-icon">
            <div class="close-bg"></div>
            <div class="close-x"></div>
          </div>
        </button>
      </div>

      <!-- Description -->
      <p class="modal-description">
        确定移除 x 名成员吗？移除成员文档将一并删除
      </p>

      <!-- Input Area -->
      <div class="input-section">
        <div class="input-container">
          <div class="input-wrapper">
            <textarea 
              class="input-textarea"
              placeholder="填写内容"
              v-model="inputContent"
            >王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、</textarea>
          </div>
        </div>
      </div>

      <!-- Buttons -->
      <div class="button-section">
        <div class="button-group">
          <button class="btn btn-cancel" @click="$emit('cancel')">
            取消
          </button>
          <button class="btn btn-confirm" @click="$emit('confirm')">
            确认
          </button>
        </div>
      </div>

      <!-- Scrollbar -->
      <div class="scrollbar"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ModalRemoveMember',
  data() {
    return {
      inputContent: '王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、'
    }
  },
  emits: ['close', 'cancel', 'confirm']
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  width: 464px;
  background: #FFFFFF;
  border-radius: 4px;
  border: 0.5px solid #DCDCDC;
  box-shadow: 0px 5px 30px 0px rgba(48, 61, 60, 0.15), 0px 2px 8px 0px rgba(48, 61, 60, 0.1);
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  position: relative;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.modal-title {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.5em;
  color: rgba(0, 0, 0, 0.9);
  margin: 0;
  flex: 1;
}

.close-btn {
  width: 18px;
  height: 18px;
  border: none;
  background: transparent;
  cursor: pointer;
  padding: 0;
  border-radius: 1.5px;
}

.close-icon {
  position: relative;
  width: 18px;
  height: 18px;
}

.close-bg {
  width: 18px;
  height: 18px;
  background: #C4C4C4;
}

.close-x {
  position: absolute;
  top: 2.9px;
  left: 2.9px;
  width: 12.2px;
  height: 12.2px;
  background: #D9D9D9;
  border-radius: 0.375px;
}

.modal-description {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.4285714285714286em;
  color: #5A5A5A;
  margin: 0;
}

.input-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.input-container {
  background: #F1F1F1;
  border-radius: 4px;
  padding: 12px;
  height: 200px;
  display: flex;
  flex-direction: column;
  gap: 22px;
}

.input-wrapper {
  height: 176px;
  position: relative;
}

.input-textarea {
  width: 100%;
  height: 100%;
  border: none;
  background: transparent;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.4285714285714286em;
  color: #212121;
  resize: none;
  outline: none;
  padding: 0;
}

.button-section {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-top: 12px;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
}

.btn {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 7px 12px;
  width: 100px;
  height: 32px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  transition: all 0.2s ease;
}

.btn-cancel {
  background: transparent;
  color: #2B7DF7;
  font-weight: 400;
  line-height: 1.5714285714285714em;
}

.btn-cancel:hover {
  background: rgba(43, 125, 247, 0.1);
}

.btn-confirm {
  background: #2B7DF7;
  color: #FFFFFF;
  font-weight: 500;
  line-height: 1.4285714285714286em;
  padding: 7px 24px;
}

.btn-confirm:hover {
  background: #1a6bd6;
}

.scrollbar {
  position: absolute;
  right: 440px;
  top: 91px;
  width: 4px;
  height: 149px;
  background: #CCCCCC;
  border-radius: 12px;
}
</style>