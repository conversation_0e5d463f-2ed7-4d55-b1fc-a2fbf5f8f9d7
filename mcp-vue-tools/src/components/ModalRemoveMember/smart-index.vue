<template>
  <div class="modal">
    <div class="header">
      <span class="text-title">移除成员</span>
      <div class="icon-ic-close" @click="$emit('close')">
        <img src="./images/ic_close.svg" alt="ic_close" />
      </div>
    </div>
    <span class="text-description">确定移除 x 名成员吗？移除成员文档将一并删除</span>
    <div class="输入框">
      <div class="问题描述">
        <div class="frame-427319677">
          <span class="text-填写内容">王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、</span>
        </div>
      </div>
    </div>
    <div class="btn-button" @click="handleClick">
      <div class="frame-524">
        <button class="btn-button" @click="handleClick">
          <span class="text-upload">取消</span>
        </button>
        <button class="btn-button" @click="handleClick">
          <span class="text-upload">确认</span>
        </button>
      </div>
    </div>
    <div class="rectangle-34624856" />
  </div>
</template>

<script>
export default {
  name: 'ModalRemoveMember',
  emits: ["close"],
  data() {
    return {
    "inputContent": "王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、王菲长、"
};
  },
  methods: {
    handleInput(value) {
      this.inputContent = value;
    },
    handleClick() {
      // 处理点击事件
    }
  }
}
</script>

<style scoped>
.modal {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
  gap: 12px;
  padding: 20px;
  width: 464px;
  background-color: #FFFFFF;
  border-radius: 4px;
  border: 0.5px solid #DCDCDC;
  box-shadow: 0px 5px 30px 0px rgba(48, 61, 60, 0.15), 0px 2px 8px 0px rgba(48, 61, 60, 0.1);
}

.header {
  display: flex;
  flex-direction: row;
  justify-content: center;
  gap: 16px;
}

.text-title {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.5em;
  text-align: left;
  background-color: rgba(0, 0, 0, 0.9);
}

.icon-ic-close {
  width: 18px;
  height: 18px;
  border-radius: 1.5px;
}

.group-295 {
  border-radius: 0px 0px 0px 0px;
}

.group-341 {
  border-radius: 0px 0px 0px 0px;
}

.rectangle-688 {
}

.rectangle-686 {
  background-color: #FFFFFF;
  border-radius: 0.45000001788139343px;
}

.text-description {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.4285714285714286em;
  text-align: left;
  background-color: #5A5A5A;
}

.输入框 {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.问题描述 {
  display: flex;
  flex-direction: column;
  gap: 22px;
  padding: 12px;
  height: 200px;
  background-color: #F1F1F1;
  border-radius: 4px;
}

.frame-427319677 {
}

.text-填写内容 {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.4285714285714286em;
  text-align: left;
  background-color: #212121;
}

.btn-button {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  padding: 12px 0px 0px;
}

.frame-524 {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
}

.btn-button {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 7px 12px;
  width: 100px;
  height: 32px;
  background-color: #FFFFFF;
  border-radius: 4px;
}

.text-upload {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.5714285714285714em;
  text-align: left;
  background-color: #2B7DF7;
}

.btn-button {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 7px 24px;
  width: 100px;
  height: 32px;
  background-color: #2B7DF7;
  border-radius: 4px;
}

.text-upload {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.4285714285714286em;
  text-align: left;
  background-color: #FFFFFF;
}

.rectangle-34624856 {
  width: 4px;
  height: 149px;
  position: absolute;
  left: 440px;
  top: 91px;
  background-color: #CCCCCC;
  border-radius: 12px;
}


</style>