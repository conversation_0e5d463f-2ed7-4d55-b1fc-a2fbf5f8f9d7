<template>
  <div class="exchange-success-ultra">
    <!-- 状态栏 (0,0,375,44) -->
    <div class="status-bar">
      <div class="time">9:41</div>
      <div class="status-right">
        <div class="signal"></div>
        <div class="wifi"></div>
        <div class="battery"></div>
      </div>
    </div>

    <!-- Logo (120,104,134,32) -->
    <img src="../ExchangeSuccess/images/logo.svg" alt="Logo" class="logo" />

    <!-- 成功提示 (73.5,160,228,28) -->
    <div class="success-section">
      <div class="success-icon-bg">
        <img src="../ExchangeSuccess/images/success_icon.svg" alt="成功" class="success-icon" />
      </div>
      <span class="success-text">高级会员兑换成功</span>
    </div>

    <!-- 会员卡片 (14,256,347,295) -->
    <div class="card-area">
      <!-- 阴影 -->
      <div class="card-shadow"></div>
      <!-- 主卡片 -->
      <div class="card-main">
        <img src="../ExchangeSuccess/images/card_decoration.svg" alt="装饰" class="card-decoration" />
        <div class="card-text">
          <div class="account">帐户：<br/><EMAIL></div>
          <div class="validity">有效期至：<br/>2026-09-06</div>
        </div>
        <div class="card-border"></div>
      </div>
    </div>

    <!-- 特权标题 (24,573,327,26) -->
    <div class="privilege-header">
      <div class="star">★</div>
      <span class="privilege-title">尊享 20+ 高级会员特权</span>
      <div class="star">★</div>
    </div>

    <!-- 特权图标 (24,613,327,86) -->
    <div class="privilege-icons">
      <div class="icon-item">
        <div class="icon-bg">
          <img src="../ExchangeSuccess/images/scan_id_icon.svg" alt="证件扫描" class="icon" />
        </div>
        <span class="icon-text">证件扫描</span>
      </div>
      <div class="icon-item">
        <div class="icon-bg">
          <img src="../ExchangeSuccess/images/pdf_word_icon.svg" alt="PDF转Word" class="icon" />
        </div>
        <span class="icon-text">PDF转Word</span>
      </div>
      <div class="icon-item">
        <div class="icon-bg">
          <img src="../ExchangeSuccess/images/scan_excel_icon.svg" alt="表格识别" class="icon" />
        </div>
        <span class="icon-text">表格识别</span>
      </div>
      <div class="icon-item">
        <div class="icon-bg">
          <img src="../ExchangeSuccess/images/remove_ad_icon.svg" alt="去广告" class="icon" />
        </div>
        <span class="icon-text">去广告</span>
      </div>
      <div class="icon-item">
        <div class="icon-bg">
          <img src="../ExchangeSuccess/images/folder_icon.svg" alt="文档管理" class="icon" />
        </div>
        <span class="icon-text">文档管理</span>
      </div>
    </div>

    <!-- 返回按钮 (16,778,24,24) -->
    <img src="../ExchangeSuccess/images/back_icon.svg" alt="返回" class="back-btn" />

    <!-- Home Indicator (121,799,134,5) -->
    <div class="home-indicator"></div>
  </div>
</template>

<script>
export default {
  name: 'ExchangeSuccessUltra'
}
</script>

<style scoped>
/* 基础容器 */
.exchange-success-ultra {
  position: relative;
  width: 375px;
  height: 812px;
  background: #1E2334;
  font-family: 'PingFang SC', sans-serif;
  overflow: hidden;
}

/* 状态栏 */
.status-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 375px;
  height: 44px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 21px;
}

.time {
  font-size: 14px;
  font-weight: 600;
  color: #FFFFFF;
}

.status-right {
  display: flex;
  gap: 5px;
  align-items: center;
}

.signal, .wifi, .battery {
  width: 17px;
  height: 11px;
  background: #FFFFFF;
  border-radius: 1px;
}

/* Logo */
.logo {
  position: absolute;
  top: 104px;
  left: 120px;
  width: 134px;
  height: 32px;
}

/* 成功提示 */
.success-section {
  position: absolute;
  top: 160px;
  left: 73.5px;
  width: 228px;
  height: 28px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.success-icon-bg {
  width: 28px;
  height: 28px;
  background: linear-gradient(180deg, #F6E2A7 0%, #CCAD74 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.success-icon {
  width: 21px;
  height: 15.75px;
}

.success-text {
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(180deg, #F6E2A7 0%, #CCAD74 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

/* 会员卡片 */
.card-area {
  position: absolute;
  top: 256px;
  left: 14px;
  width: 347px;
  height: 295px;
}

.card-shadow {
  position: absolute;
  top: 53px;
  left: 17.5px;
  width: 311px;
  height: 220px;
  background: #30384E;
  border-radius: 8px;
}

.card-main {
  position: absolute;
  top: 31.5px;
  left: 32px;
  width: 283px;
  height: 212px;
  background: linear-gradient(180deg, #FFF3CE 0%, #F6E1A7 100%);
  border-radius: 8px;
  overflow: hidden;
}

.card-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 208px;
  height: 151px;
  opacity: 0.6;
}

.card-text {
  position: absolute;
  top: 24px;
  left: 16px;
  z-index: 2;
}

.account, .validity {
  font-size: 16px;
  color: #1E2334;
  line-height: 1.5;
  margin-bottom: 8px;
}

.card-border {
  position: absolute;
  top: 4px;
  left: 4px;
  right: 4px;
  bottom: 4px;
  border: 1px solid #E8CB91;
  border-radius: 6px;
}

/* 特权标题 */
.privilege-header {
  position: absolute;
  top: 573px;
  left: 24px;
  width: 327px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.star {
  color: #D5B87F;
  font-size: 16px;
}

.privilege-title {
  font-size: 18px;
  font-weight: 500;
  color: #D5B87F;
}

/* 特权图标 */
.privilege-icons {
  position: absolute;
  top: 613px;
  left: 24px;
  width: 327px;
  height: 86px;
  display: flex;
  justify-content: space-between;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  width: 56px;
}

.icon-bg {
  width: 40px;
  height: 40px;
  background: #333949;
  border: 1px solid #5B6171;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon {
  width: 24px;
  height: 24px;
}

.icon-text {
  font-size: 12px;
  color: #D5B87F;
  text-align: center;
}

/* 返回按钮 */
.back-btn {
  position: absolute;
  top: 778px;
  left: 16px;
  width: 24px;
  height: 24px;
}

/* Home Indicator */
.home-indicator {
  position: absolute;
  top: 799px;
  left: 121px;
  width: 134px;
  height: 5px;
  background: #FFFFFF;
  border-radius: 2.5px;
  opacity: 0.3;
}

/* 禁用动画 */
* {
  transition: none !important;
  animation: none !important;
}
</style>
