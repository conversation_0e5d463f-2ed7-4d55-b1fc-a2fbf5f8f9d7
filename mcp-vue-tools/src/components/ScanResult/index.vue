<template>
  <div class="scan-result">
    <!-- 顶部区域 - 图标和标题 -->
    <div class="top-section">
      <!-- 成功图标容器 -->
      <div class="icon-container">
        <img src="./images/success_check_icon.svg" alt="扫描成功" class="success-icon" />
      </div>
      
      <!-- 标题文字 -->
      <h1 class="title">扫描完成，已扫描120张</h1>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-card">
      <!-- 成功统计 -->
      <div class="stats-item success-stats">
        <div class="stats-number success-number">120</div>
        <div class="stats-label">识别成功(张)</div>
      </div>

      <!-- 分隔线 -->
      <div class="divider"></div>

      <!-- 失败统计 -->
      <div class="stats-item failure-stats">
        <div class="stats-number failure-number">0</div>
        <div class="stats-label">识别失败(张)</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ScanResult',
  props: {
    successCount: {
      type: Number,
      default: 120
    },
    failureCount: {
      type: Number,
      default: 0
    },
    totalCount: {
      type: Number,
      default: 120
    }
  },
  computed: {
    titleText() {
      return `扫描完成，已扫描${this.totalCount}张`
    }
  },
  emits: ['continue', 'retry'],
  methods: {
    handleContinue() {
      this.$emit('continue')
    },
    handleRetry() {
      this.$emit('retry')
    }
  }
}
</script>

<style scoped>
/* 基础容器 */
.scan-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 60px;
  padding: 40px 20px;
  background: transparent;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 顶部区域 - 图标和标题 */
.top-section {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 18px;
}

/* 成功图标容器 */
.icon-container {
  width: 72px;
  height: 72px;
  background: #FFFFFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.success-icon {
  width: 66px;
  height: 66px;
  object-fit: contain;
}

/* 标题文字 */
.title {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 44px;
  line-height: 1.3636363636363635em;
  color: #262626;
  margin: 0;
  text-align: left;
}

/* 统计卡片区域 */
.stats-card {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 120px;
  padding: 80px 120px;
  background: #FFFFFF;
  border-radius: 32px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

/* 统计项 */
.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  width: 204px;
}

/* 统计数字 */
.stats-number {
  font-family: 'zihunbiantaoti', 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 120px;
  text-align: center;
  margin: 0;
  width: 100%;
}

.success-number {
  line-height: 1.2em;
  color: #141414;
}

.failure-number {
  line-height: 1em;
  color: #BFBFBF;
}

/* 统计标签 */
.stats-label {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 36px;
  line-height: 1.3333333333333333em;
  color: #595959;
  text-align: left;
  width: 100%;
}

/* 分隔线 */
.divider {
  width: 2px;
  height: 72px;
  background: #BFBFBF;
  border-radius: 1px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .scan-result {
    gap: 40px;
    padding: 20px 16px;
  }
  
  .top-section {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .title {
    font-size: 32px;
    text-align: center;
  }
  
  .stats-card {
    flex-direction: column;
    gap: 40px;
    padding: 40px 60px;
  }
  
  .divider {
    width: 72px;
    height: 2px;
  }
  
  .stats-number {
    font-size: 80px;
  }
  
  .stats-label {
    font-size: 24px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 24px;
  }
  
  .stats-card {
    padding: 30px 40px;
  }
  
  .stats-number {
    font-size: 60px;
  }
  
  .stats-label {
    font-size: 18px;
  }
  
  .icon-container {
    width: 60px;
    height: 60px;
  }
  
  .success-icon {
    width: 54px;
    height: 54px;
  }
}

/* 动画效果 */
.scan-result {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.icon-container {
  animation: scaleIn 0.5s ease-out 0.2s both;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.stats-card {
  animation: slideInUp 0.6s ease-out 0.4s both;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 交互效果 */
.stats-card:hover {
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
  transition: box-shadow 0.3s ease;
}

.icon-container:hover {
  transform: scale(1.05);
  transition: transform 0.2s ease;
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .scan-result,
  .icon-container,
  .stats-card {
    animation: none;
  }
  
  .stats-card:hover,
  .icon-container:hover {
    transform: none;
  }
}
</style>
