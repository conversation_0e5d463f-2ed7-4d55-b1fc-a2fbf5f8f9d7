<template>
  <div class="exchange-success-optimized">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-content">
        <div class="time">9:41</div>
        <div class="status-icons">
          <div class="signal-icon"></div>
          <div class="wifi-icon"></div>
          <div class="battery-icon"></div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 顶部Logo -->
      <div class="logo-section">
        <img src="./images/logo.svg" alt="扫描全能王" class="app-logo" />
      </div>

      <!-- 成功状态 -->
      <div class="success-section">
        <div class="success-indicator">
          <div class="success-circle">
            <img src="./images/success_icon.svg" alt="成功" class="success-icon" />
          </div>
          <h1 class="success-title">高级会员兑换成功</h1>
        </div>
        <p class="success-subtitle">下载扫描全能王 App 查看高级会员特权</p>
      </div>

      <!-- 会员卡片 -->
      <div class="member-card-container">
        <!-- 卡片阴影背景 -->
        <div class="card-shadow"></div>

        <!-- 主卡片 -->
        <div class="member-card">
          <!-- 卡片背景装饰 -->
          <div class="card-background">
            <img src="./images/card_decoration.svg" alt="装饰" class="card-decoration" />
          </div>

          <!-- 卡片内容 -->
          <div class="card-content">
            <!-- 顶部区域 -->
            <div class="card-top">
              <div class="member-badge">
                <span class="member-text">高级会员</span>
              </div>
              <div class="card-logo">
                <div class="logo-circle"></div>
              </div>
            </div>

            <!-- 中间区域 -->
            <div class="card-middle">
              <div class="member-avatar">
                <div class="avatar-circle">
                  <span class="avatar-text">王</span>
                </div>
              </div>
              <div class="member-details">
                <div class="member-name">王小明</div>
                <div class="member-id">ID: 123456789</div>
              </div>
            </div>

            <!-- 底部区域 -->
            <div class="card-bottom">
              <div class="validity-info">
                <div class="validity-label">有效期至</div>
                <div class="validity-date">2024.12.31</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 功能列表 -->
      <div class="features-section">
        <div class="features-title">高级会员特权</div>
        <div class="features-grid">
          <!-- 第一行功能 -->
          <div class="feature-row">
            <div class="feature-item">
              <div class="feature-icon">
                <img src="./images/pdf_word_icon.svg" alt="PDF转Word" />
              </div>
              <span class="feature-text">PDF转Word</span>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <img src="./images/table_recognition_icon.svg" alt="表格识别" />
              </div>
              <span class="feature-text">表格识别</span>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <img src="./images/remove_ad_icon.svg" alt="去广告" />
              </div>
              <span class="feature-text">去广告</span>
            </div>
          </div>

          <!-- 第二行功能 -->
          <div class="feature-row">
            <div class="feature-item">
              <div class="feature-icon">
                <img src="./images/scan_id_icon.svg" alt="证件扫描" />
              </div>
              <span class="feature-text">证件扫描</span>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <img src="./images/document_manage_icon.svg" alt="文档管理" />
              </div>
              <span class="feature-text">文档管理</span>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <div class="more-icon">+</div>
              </div>
              <span class="feature-text">更多特权</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="action-buttons">
        <button class="download-btn">
          <span class="btn-text">立即下载 App</span>
        </button>
        <button class="back-btn" @click="handleBack">
          <img src="./images/back_icon.svg" alt="返回" class="back-icon" />
          <span class="btn-text">返回</span>
        </button>
      </div>
    </div>

    <!-- 底部指示器 -->
    <div class="bottom-indicator">
      <div class="home-indicator"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ExchangeSuccessOptimized',
  emits: ['back', 'download'],
  data() {
    return {
      memberInfo: {
        name: '王小明',
        id: '123456789',
        validUntil: '2024.12.31'
      }
    }
  },
  methods: {
    handleBack() {
      this.$emit('back')
    },
    handleDownload() {
      this.$emit('download')
    }
  }
}
</script>

<style scoped>
/* 基础容器 - 精确匹配Figma尺寸 */
.exchange-success-optimized {
  width: 375px;
  height: 812px;
  position: relative;
  background: #1E2334;
  font-family: 'HarmonyOS Sans SC', 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  overflow: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 状态栏 */
.status-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 375px;
  height: 44px;
  background: transparent;
  z-index: 100;
}

.status-content {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  box-sizing: border-box;
}

.time {
  font-size: 17px;
  font-weight: 600;
  color: #FFFFFF;
  line-height: 22px;
}

.status-icons {
  display: flex;
  align-items: center;
  gap: 6px;
}

.signal-icon,
.wifi-icon,
.battery-icon {
  width: 24px;
  height: 11px;
  background: #FFFFFF;
  border-radius: 2px;
}

/* 主要内容区域 */
.main-content {
  position: absolute;
  top: 44px;
  left: 0;
  width: 375px;
  height: 734px;
  padding: 60px 20px 0;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Logo区域 */
.logo-section {
  margin-bottom: 60px;
}

.app-logo {
  width: 134px;
  height: 32px;
  object-fit: contain;
}

/* 成功状态区域 */
.success-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px;
}

.success-indicator {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.success-circle {
  width: 28px;
  height: 28px;
  background: linear-gradient(180deg, #F6E2A7 0%, #CCAD74 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.success-icon {
  width: 21px;
  height: 15.75px;
  object-fit: contain;
}

.success-title {
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-size: 24px;
  font-weight: 700;
  color: transparent;
  background: linear-gradient(180deg, #F6E2A7 0%, #CCAD74 100%);
  background-clip: text;
  -webkit-background-clip: text;
  line-height: 36px;
  margin: 0;
}

.success-subtitle {
  font-family: 'PingFang SC', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #D5B87F;
  line-height: 24px;
  text-align: center;
  margin: 0;
}

/* 会员卡片容器 */
.member-card-container {
  width: 347px;
  height: 295px;
  margin-bottom: 40px;
  position: relative;
}

.card-shadow {
  position: absolute;
  top: 53px;
  left: 17.5px;
  width: 311px;
  height: 220px;
  background: #30384E;
  border-radius: 8px;
  z-index: 1;
}

.member-card {
  position: absolute;
  top: 0;
  left: 31.5px;
  width: 283px;
  height: 212px;
  border-radius: 8px;
  overflow: hidden;
  z-index: 2;
}

.card-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, #FFF3CE 0%, #F6E1A7 85.85%);
  border-radius: 8px;
}

.card-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 208px;
  height: 151px;
  object-fit: cover;
  opacity: 0.6;
}

.card-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 3;
}

.card-top {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.member-badge {
  background: #30384E;
  border-radius: 12px;
  padding: 6px 12px;
}

.member-text {
  font-size: 12px;
  font-weight: 600;
  color: #F6E2A7;
  line-height: 16px;
}

.card-logo {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-circle {
  width: 24px;
  height: 24px;
  background: rgba(30, 35, 52, 0.1);
  border-radius: 50%;
}

.card-middle {
  display: flex;
  align-items: center;
  gap: 16px;
  margin: 20px 0;
}

.member-avatar {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-circle {
  width: 40px;
  height: 40px;
  background: rgba(30, 35, 52, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-text {
  font-size: 16px;
  font-weight: 600;
  color: #1E2334;
  line-height: 20px;
}

.member-details {
  flex: 1;
}

.member-name {
  font-size: 16px;
  font-weight: 600;
  color: #1E2334;
  line-height: 22px;
  margin-bottom: 4px;
}

.member-id {
  font-size: 11px;
  font-weight: 400;
  color: rgba(30, 35, 52, 0.6);
  line-height: 14px;
}

.card-bottom {
  display: flex;
  justify-content: center;
}

.validity-info {
  text-align: center;
}

.validity-label {
  font-size: 11px;
  font-weight: 400;
  color: rgba(30, 35, 52, 0.6);
  line-height: 14px;
  margin-bottom: 4px;
}

.validity-date {
  font-size: 16px;
  font-weight: 700;
  color: #1E2334;
  line-height: 22px;
}

/* 功能列表 */
.features-section {
  width: 100%;
  margin-bottom: 40px;
}

.features-title {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  line-height: 24px;
  text-align: center;
  margin-bottom: 24px;
}

.features-grid {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.feature-row {
  display: flex;
  justify-content: space-around;
  gap: 20px;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.feature-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-icon img {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.more-icon {
  font-size: 24px;
  font-weight: 300;
  color: #FFFFFF;
  line-height: 24px;
}

.feature-text {
  font-size: 12px;
  font-weight: 400;
  color: #D5B87F;
  line-height: 16px;
  text-align: center;
}

/* 底部按钮 */
.action-buttons {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: auto;
}

.download-btn {
  width: 100%;
  height: 48px;
  background: linear-gradient(180deg, #F6E2A7 0%, #CCAD74 100%);
  border: none;
  border-radius: 24px;
  cursor: pointer;
}

.download-btn .btn-text {
  font-size: 16px;
  font-weight: 600;
  color: #1E2334;
  line-height: 22px;
}

.back-btn {
  width: 100%;
  height: 48px;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
}

.back-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

.back-btn .btn-text {
  font-size: 16px;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 22px;
}

/* 底部指示器 */
.bottom-indicator {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
}

.home-indicator {
  width: 134px;
  height: 5px;
  background: #FFFFFF;
  border-radius: 2.5px;
  opacity: 0.3;
}

/* 交互效果 */
.download-btn:hover,
.back-btn:hover {
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.download-btn:active,
.back-btn:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

.feature-item:hover .feature-icon {
  background: rgba(255, 255, 255, 0.2);
  transition: background 0.2s ease;
}

/* 响应式优化 */
@media (max-width: 375px) {
  .exchange-success-optimized {
    width: 100vw;
  }
  
  .status-bar,
  .main-content {
    width: 100%;
  }
}
</style>
