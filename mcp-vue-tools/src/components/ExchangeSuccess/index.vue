<template>
  <div class="exchange-success-page">
    <!-- Status Bar -->
    <div class="status-bar">
      <div class="status-content">
        <div class="left-side">
          <div class="time-display">9:41</div>
        </div>
        <div class="right-side">
          <div class="signal-icons">
            <div class="mobile-signal"></div>
            <div class="wifi-signal"></div>
            <div class="battery">
              <div class="battery-outline"></div>
              <div class="battery-level"></div>
              <div class="battery-tip"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Logo -->
    <div class="logo-section">
      <div class="logo-container">
        <div class="logo-icon">
          <div class="logo-bg"></div>
          <div class="logo-content">
            <div class="logo-text">扫描</div>
            <div class="logo-text-full">全能王</div>
          </div>
        </div>
        <div class="logo-tm">TM</div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
      <!-- Card Container -->
      <div class="card-container">
        <!-- Background Card -->
        <div class="background-card"></div>
        
        <!-- Main Card -->
        <div class="main-card">
          <div class="card-background"></div>
          <div class="card-decoration"></div>
          <div class="card-badge">
            <div class="badge-content"></div>
          </div>
          
          <!-- Account Info -->
          <div class="account-info">
            <div class="account-text">
              帐户：<br><EMAIL>
            </div>
            <div class="validity-text">
              有效期至：<br>2026-09-06
            </div>
          </div>
          
          <div class="card-border"></div>
        </div>

        <!-- Bottom Card -->
        <div class="bottom-card">
          <div class="bottom-card-bg"></div>
          <div class="side-blur left-blur"></div>
          <div class="side-blur right-blur"></div>
          <div class="bottom-card-content">
            <div class="bottom-card-inner"></div>
            <div class="bottom-card-border"></div>
            <div class="bottom-shadow"></div>
          </div>
        </div>

        <!-- Decorative Elements -->
        <div class="decoration-left">
          <div class="decoration-bg"></div>
          <div class="decoration-image"></div>
        </div>
        <div class="decoration-right">
          <div class="decoration-bg"></div>
          <div class="decoration-image"></div>
        </div>
      </div>

      <!-- Features Section -->
      <div class="features-section">
        <div class="features-header">
          <div class="star-decoration"></div>
          <div class="features-title">尊享 20+ 高级会员特权</div>
          <div class="star-decoration"></div>
        </div>

        <div class="features-grid">
          <div class="feature-item">
            <div class="feature-icon">
              <div class="feature-icon-bg"></div>
              <div class="feature-icon-content"></div>
            </div>
            <div class="feature-text">证件扫描</div>
          </div>

          <div class="feature-item">
            <div class="feature-icon">
              <div class="feature-icon-bg"></div>
              <div class="feature-icon-content"></div>
            </div>
            <div class="feature-text">PDF转换</div>
          </div>

          <div class="feature-item">
            <div class="feature-icon">
              <div class="feature-icon-bg"></div>
              <div class="feature-icon-content"></div>
            </div>
            <div class="feature-text">文字识别</div>
          </div>

          <div class="feature-item">
            <div class="feature-icon">
              <div class="feature-icon-bg"></div>
              <div class="feature-icon-content"></div>
            </div>
            <div class="feature-text">表格识别</div>
          </div>

          <div class="feature-item">
            <div class="feature-icon">
              <div class="feature-icon-bg"></div>
              <div class="feature-icon-content"></div>
            </div>
            <div class="feature-text">去水印</div>
          </div>

          <div class="feature-item">
            <div class="feature-icon">
              <div class="feature-icon-bg"></div>
              <div class="feature-icon-content"></div>
            </div>
            <div class="feature-text">去广告</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Success Badge -->
    <div class="success-badge">
      <div class="success-icon">
        <div class="success-bg"></div>
        <div class="success-check"></div>
      </div>
      <div class="success-text">兑换成功</div>
    </div>

    <!-- Bottom Text -->
    <div class="bottom-text">
      恭喜您成功兑换扫描全能王高级会员！
    </div>
  </div>
</template>

<script>
export default {
  name: 'ExchangeSuccess',
  data() {
    return {}
  }
}
</script>

<style scoped>
.exchange-success-page {
  width: 375px;
  height: 812px;
  background: #FFFFFF;
  position: relative;
  overflow: hidden;
}

/* Status Bar */
.status-bar {
  width: 375px;
  height: 44px;
  background: #FFFFFF;
  position: relative;
}

.status-content {
  width: 100%;
  height: 100%;
  position: relative;
}

.left-side {
  position: absolute;
  left: 21px;
  top: 12px;
  width: 54px;
  height: 21px;
}

.time-display {
  width: 100%;
  height: 100%;
  background: #FFFFFF;
  border-radius: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
}

.right-side {
  position: absolute;
  right: 14.67px;
  top: 17.33px;
  width: 66.66px;
  height: 11.34px;
}

.signal-icons {
  display: flex;
  align-items: center;
  gap: 5px;
}

.mobile-signal,
.wifi-signal {
  width: 17px;
  height: 10.67px;
  background: #FFFFFF;
}

.battery {
  width: 24.33px;
  height: 11.33px;
  position: relative;
}

.battery-outline {
  width: 22px;
  height: 11.33px;
  border: 1px solid rgba(255, 255, 255, 0.35);
  border-radius: 2.67px;
}

.battery-level {
  position: absolute;
  left: 2px;
  top: 2px;
  width: 18px;
  height: 7.33px;
  background: #FFFFFF;
  border-radius: 1.33px;
}

.battery-tip {
  position: absolute;
  right: 0;
  top: 3.67px;
  width: 1.33px;
  height: 4px;
  background: rgba(255, 255, 255, 0.4);
}

/* Logo Section */
.logo-section {
  margin-top: 60px;
  display: flex;
  justify-content: center;
}

.logo-container {
  width: 134px;
  height: 32px;
  position: relative;
}

.logo-icon {
  width: 26.88px;
  height: 26.88px;
  background: #FFFFFF;
  border-radius: 5.99px;
  position: relative;
}

.logo-bg {
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, #FFF1CE 0%, #D5B87F 100%);
  border-radius: 5.99px;
}

.logo-content {
  position: absolute;
  top: 4.71px;
  left: 3.31px;
  width: 20.47px;
  height: 12.1px;
}

.logo-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: #FFFFFF;
}

.logo-text-full {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: #FFFFFF;
  margin-left: 36px;
}

.logo-tm {
  position: absolute;
  right: 0;
  top: 0.09px;
  width: 3.84px;
  height: 1.68px;
  font-family: 'PingFang SC', sans-serif;
  font-size: 8px;
  color: #FFFFFF;
}

/* Main Content */
.main-content {
  margin-top: 120px;
  padding: 0 14px;
}

.card-container {
  width: 347px;
  height: 295px;
  position: relative;
}

.background-card {
  position: absolute;
  left: 17.5px;
  top: 53px;
  width: 311px;
  height: 220px;
  background: #30384E;
  border-radius: 8px;
}

.main-card {
  position: absolute;
  left: 31.5px;
  top: 0;
  width: 283px;
  height: 212px;
  background: linear-gradient(180deg, #FFF3CE 0%, #F6E1A7 100%);
  border-radius: 8px;
  position: relative;
}

.card-background {
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, #FFF3CE 0%, #F6E1A7 100%);
  border-radius: 8px;
}

.card-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 208px;
  height: 151px;
  background: linear-gradient(33.93deg, #FFFCF2 6.62%, rgba(255, 252, 242, 0) 68.54%);
}

.card-badge {
  position: absolute;
  right: 96px;
  top: 77.16px;
  width: 94.26px;
  height: 89.84px;
  background: linear-gradient(16.18deg, rgba(232, 203, 145, 0.4) 0%, rgba(232, 203, 145, 0) 100%);
  border: 0.88px solid;
  border-image: linear-gradient(16.18deg, #E8CB91 0%, rgba(232, 203, 145, 0) 100%) 1;
  border-radius: 4.09px;
}

.badge-content {
  width: 76.39px;
  height: 16.37px;
  background: #24293B;
  border-radius: 4.09px;
}

.account-info {
  position: absolute;
  left: 16px;
  top: 24px;
  width: 172px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.account-text,
.validity-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.5em;
  color: #FFFFFF;
}

.card-border {
  position: absolute;
  left: 4px;
  top: 4px;
  width: 275px;
  height: 204px;
  border: 1px solid #E8CB91;
  border-radius: 6px;
}

.bottom-card {
  position: absolute;
  left: 17.5px;
  top: 128px;
  width: 311px;
  height: 148px;
  position: relative;
}

.bottom-card-bg {
  width: 311px;
  height: 139.45px;
  background: radial-gradient(50% 50% at 50% 50%, #39425B 0%, #252B3D 100%);
  border-radius: 8px;
  margin-top: 5.55px;
}

.side-blur {
  position: absolute;
  width: 6px;
  height: 145px;
  background: #30384D;
  filter: blur(8px);
  top: 3px;
}

.left-blur {
  left: 0;
}

.right-blur {
  right: 0;
}

.bottom-card-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 311px;
  height: 145px;
  background: linear-gradient(180deg, #485166 0%, #212637 100%);
}

.bottom-card-border {
  position: absolute;
  left: 12px;
  top: 8px;
  width: 287px;
  height: 133px;
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(213, 184, 127, 0) 0%, #D5B87F 100%) 1;
}

.bottom-shadow {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 311px;
  height: 6px;
  background: #212638;
}

.decoration-left {
  position: absolute;
  left: 16px;
  top: 246.5px;
  width: 43.83px;
  height: 41.5px;
}

.decoration-right {
  position: absolute;
  right: 14px;
  top: 170.89px;
  width: 97.5px;
  height: 124.61px;
}

.decoration-bg {
  width: 100%;
  height: 100%;
  background: #D9D9D9;
}

.decoration-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
}

/* Features Section */
.features-section {
  margin-top: 20px;
  padding: 0 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.features-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.star-decoration {
  width: 20px;
  height: 12px;
  background: #D5B87F;
}

.features-title {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 18px;
  line-height: 1.44em;
  color: #D5B87F;
  text-align: center;
}

.features-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 21px;
  justify-content: center;
  width: 100%;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  width: 66px;
}

.feature-icon {
  width: 40px;
  height: 40px;
  position: relative;
}

.feature-icon-bg {
  width: 40px;
  height: 40px;
  background: #333949;
  border: 1px solid #5B6171;
  border-radius: 50%;
}

.feature-icon-content {
  position: absolute;
  top: 8px;
  left: 8px;
  width: 24px;
  height: 24px;
  background: linear-gradient(180deg, #FFF1CE 0%, #D5B87F 100%);
}

.feature-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.33em;
  color: #D5B87F;
  text-align: center;
  width: 66px;
}

/* Success Badge */
.success-badge {
  position: absolute;
  left: 73.5px;
  top: 160px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.success-icon {
  width: 28px;
  height: 28px;
  background: linear-gradient(180deg, #F6E2A7 0%, #CCAD74 100%);
  border-radius: 50%;
  position: relative;
}

.success-check {
  position: absolute;
  top: 5.25px;
  left: 5.25px;
  width: 21px;
  height: 15.75px;
  background: #FFFFFF;
}

.success-text {
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 700;
  font-size: 24px;
  line-height: 1.5em;
  color: #D5B87F;
}

/* Bottom Text */
.bottom-text {
  position: absolute;
  left: 47px;
  top: 208px;
  width: 281px;
  height: 24px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.5em;
  color: #D5B87F;
  text-align: center;
}
</style>