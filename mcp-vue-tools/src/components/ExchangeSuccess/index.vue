<template>
  <div class="exchange-success">
    <!-- 状态栏 -->
    <div class="status-bar">
      <!-- 左侧时间 -->
      <div class="time">9:41</div>
      <!-- 右侧信号/电池 -->
      <div class="status-icons">
        <div class="signal"></div>
        <div class="wifi"></div>
        <div class="battery"></div>
      </div>
    </div>

    <!-- Logo区域 -->
    <div class="logo-section">
      <img src="./images/logo.svg" alt="扫描全能王" class="logo" />
    </div>

    <!-- 成功提示区域 -->
    <div class="success-section">
      <div class="success-icon-container">
        <img src="./images/success_icon.svg" alt="成功" class="success-icon" />
      </div>
      <h1 class="success-title">高级会员兑换成功</h1>
    </div>

    <!-- 会员卡片区域 -->
    <div class="card-container">
      <!-- 卡片背景和装饰 -->
      <div class="card-background">
        <img src="./images/card_decoration.svg" alt="装饰" class="card-decoration" />

        <!-- 卡片内容 -->
        <div class="card-content">
          <!-- 账户信息 -->
          <div class="account-info">
            <div class="account-text">
              帐户：<br/>
              <EMAIL>
            </div>
            <div class="validity-text">
              有效期至：<br/>
              2026-09-06
            </div>
          </div>
        </div>

        <!-- 卡片边框 -->
        <div class="card-border"></div>
      </div>

      <!-- 卡片阴影效果 -->
      <div class="card-shadow"></div>
    </div>

    <!-- 特权标题 -->
    <div class="privilege-title">
      <div class="star-left"></div>
      <span class="title-text">尊享 20+ 高级会员特权</span>
      <div class="star-right"></div>
    </div>

    <!-- 特权功能图标 -->
    <div class="privilege-icons">
      <div class="privilege-item">
        <div class="icon-container">
          <img src="./images/scan_id_icon.svg" alt="证件扫描" class="privilege-icon" />
        </div>
        <span class="icon-label">证件扫描</span>
      </div>

      <div class="privilege-item">
        <div class="icon-container">
          <img src="./images/pdf_word_icon.svg" alt="PDF转Word" class="privilege-icon" />
        </div>
        <span class="icon-label">PDF转Word</span>
      </div>

      <div class="privilege-item">
        <div class="icon-container">
          <img src="./images/scan_excel_icon.svg" alt="表格识别" class="privilege-icon" />
        </div>
        <span class="icon-label">表格识别</span>
      </div>

      <div class="privilege-item">
        <div class="icon-container">
          <img src="./images/remove_ad_icon.svg" alt="去广告" class="privilege-icon" />
        </div>
        <span class="icon-label">去广告</span>
      </div>

      <div class="privilege-item">
        <div class="icon-container">
          <img src="./images/folder_icon.svg" alt="文档管理" class="privilege-icon" />
        </div>
        <span class="icon-label">文档管理</span>
      </div>
    </div>

    <!-- 返回按钮 -->
    <div class="back-button">
      <img src="./images/back_icon.svg" alt="返回" class="back-icon" />
    </div>

    <!-- Home Indicator -->
    <div class="home-indicator"></div>
  </div>
</template>

<script>
export default {
  name: 'ExchangeSuccess',
  props: {
    accountEmail: {
      type: String,
      default: '<EMAIL>'
    },
    validityDate: {
      type: String,
      default: '2026-09-06'
    }
  },
  emits: ['back', 'continue'],
  methods: {
    handleBack() {
      this.$emit('back')
    },
    handleContinue() {
      this.$emit('continue')
    }
  }
}
</script>

<style scoped>
/* 基础容器 - 精确匹配Figma尺寸 */
.exchange-success {
  position: relative;
  width: 375px;
  height: 812px;
  background: #1E2334;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  overflow: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 状态栏 - 基于boundingBox (0,0,375,44) */
.status-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 375px;
  height: 44px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 21px;
  background: #1E2334;
}

.time {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  font-size: 14px;
  color: #FFFFFF;
}

.status-icons {
  display: flex;
  align-items: center;
  gap: 5px;
}

.signal, .wifi, .battery {
  width: 17px;
  height: 11px;
  background: #FFFFFF;
  opacity: 0.8;
  border-radius: 1px;
}

/* Logo区域 - 基于boundingBox (120,104,134,32) */
.logo-section {
  position: absolute;
  top: 104px;
  left: 120px;
  width: 134px;
  height: 32px;
}

.logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 成功提示区域 - 基于boundingBox (73.5,160,228,28) */
.success-section {
  position: absolute;
  top: 160px;
  left: 73.5px;
  width: 228px;
  height: 28px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.success-icon-container {
  width: 28px;
  height: 28px;
  background: linear-gradient(180deg, #F6E2A7 0%, #CCAD74 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.success-icon {
  width: 21px;
  height: 15.75px;
  object-fit: contain;
}

.success-title {
  font-family: 'HarmonyOS Sans SC', 'PingFang SC', sans-serif;
  font-weight: 700;
  font-size: 24px;
  line-height: 1.5em;
  background: linear-gradient(180deg, #F6E2A7 0%, #CCAD74 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  margin: 0;
}

/* 会员卡片区域 - 基于boundingBox (14,256,347,295) */
.card-container {
  position: absolute;
  top: 256px;
  left: 14px;
  width: 347px;
  height: 295px;
}

.card-background {
  position: relative;
  width: 283px;
  height: 212px;
  margin: 31.5px auto 0;
  background: linear-gradient(180deg, #FFF3CE 0%, #F6E1A7 100%);
  border-radius: 8px;
  overflow: hidden;
}

.card-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 208px;
  height: 151px;
  opacity: 0.6;
  object-fit: cover;
}

.card-content {
  position: relative;
  z-index: 2;
  padding: 24px 16px;
}

.account-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 172px;
}

.account-text, .validity-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.5em;
  color: #1E2334;
}

.card-border {
  position: absolute;
  top: 4px;
  left: 4px;
  right: 4px;
  bottom: 4px;
  border: 1px solid #E8CB91;
  border-radius: 6px;
  pointer-events: none;
}

.card-shadow {
  position: absolute;
  top: 53px;
  left: 17.5px;
  width: 311px;
  height: 220px;
  background: #30384E;
  border-radius: 8px;
  z-index: -1;
}

/* 特权标题 - 基于layout数据 */
.privilege-title {
  position: absolute;
  top: 573px;
  left: 24px;
  width: 327px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 20px;
}

.star-left, .star-right {
  width: 20px;
  height: 12px;
  background: #D5B87F;
  /* 星星图案通过CSS实现 */
}

.title-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 18px;
  line-height: 1.4444444444444444em;
  color: #D5B87F;
  text-align: center;
}

/* 特权功能图标 - 基于layout数据 */
.privilege-icons {
  position: absolute;
  top: 613px;
  left: 24px;
  width: 327px;
  display: flex;
  flex-wrap: wrap;
  gap: 21px;
  align-items: flex-start;
}

.privilege-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  width: 66px;
}

.icon-container {
  width: 40px;
  height: 40px;
  background: #333949;
  border: 1px solid #5B6171;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.privilege-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.icon-label {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.3333333333333333em;
  color: #D5B87F;
  text-align: center;
  width: 66px;
}

/* 返回按钮 - 基于boundingBox (16,778,24,24) */
.back-button {
  position: absolute;
  top: 778px;
  left: 16px;
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.back-icon {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Home Indicator - 基于boundingBox (121,799,134,5) */
.home-indicator {
  position: absolute;
  top: 799px;
  left: 121px;
  width: 134px;
  height: 5px;
  background: #FFFFFF;
  border-radius: 2.5px;
  opacity: 0.3;
}

/* 动画效果 */
.exchange-success {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.success-section {
  animation: slideInDown 0.8s ease-out 0.2s both;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card-container {
  animation: scaleIn 0.8s ease-out 0.4s both;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.privilege-icons {
  animation: slideInUp 0.8s ease-out 0.6s both;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 交互效果 */
.back-button:hover {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

.privilege-item:hover .icon-container {
  background: #3D4556;
  transform: scale(1.05);
  transition: all 0.2s ease;
}

.card-background:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .exchange-success,
  .success-section,
  .card-container,
  .privilege-icons {
    animation: none;
  }

  .back-button:hover,
  .privilege-item:hover .icon-container,
  .card-background:hover {
    transform: none;
  }
}

/* 响应式适配 */
@media (max-width: 375px) {
  .exchange-success {
    width: 100vw;
    height: 100vh;
  }
}
</style>