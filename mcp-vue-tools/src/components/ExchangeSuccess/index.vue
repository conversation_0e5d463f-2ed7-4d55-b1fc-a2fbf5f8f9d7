<template>
  <div class="exchange-success">
    <!-- 状态栏 - 精确匹配Figma Status Bar -->
    <div class="status-bar">
      <div class="time">9:41</div>
      <div class="status-icons">
        <div class="signal-bars">
          <div class="bar bar1"></div>
          <div class="bar bar2"></div>
          <div class="bar bar3"></div>
          <div class="bar bar4"></div>
        </div>
        <div class="wifi-icon">
          <div class="wifi-arc wifi-arc1"></div>
          <div class="wifi-arc wifi-arc2"></div>
          <div class="wifi-arc wifi-arc3"></div>
        </div>
        <div class="battery">
          <div class="battery-body"></div>
          <div class="battery-tip"></div>
          <div class="battery-level"></div>
        </div>
      </div>
    </div>

    <!-- Logo区域 - 精确位置 (120,104,134,32) -->
    <div class="logo-section">
      <img src="./images/logo.svg" alt="扫描全能王" class="logo" />
    </div>

    <!-- 成功提示区域 - 精确位置 (73.5,160,228,28) -->
    <div class="success-section">
      <div class="success-icon-container">
        <img src="./images/success_icon.svg" alt="成功" class="success-icon" />
      </div>
      <h1 class="success-title">高级会员兑换成功</h1>
    </div>

    <!-- 会员卡片区域 - 精确位置 (14,256,347,295) -->
    <div class="card-container">
      <!-- 卡片阴影 -->
      <div class="card-shadow"></div>

      <!-- 主卡片 -->
      <div class="card-main">
        <!-- 卡片装饰图 -->
        <img src="./images/card_decoration.svg" alt="装饰" class="card-decoration" />

        <!-- 卡片内容 -->
        <div class="card-content">
          <div class="account-text">帐户：<br/><EMAIL></div>
          <div class="validity-text">有效期至：<br/>2026-09-06</div>
        </div>

        <!-- 卡片边框 -->
        <div class="card-border"></div>
      </div>
    </div>

    <!-- 特权标题区域 -->
    <div class="privilege-header">
      <div class="star-decoration star-left">
        <div class="star-shape"></div>
      </div>
      <span class="privilege-title">尊享 20+ 高级会员特权</span>
      <div class="star-decoration star-right">
        <div class="star-shape"></div>
      </div>
    </div>

    <!-- 特权功能图标区域 - 精确位置和布局 -->
    <div class="privilege-icons">
      <div class="privilege-item">
        <div class="icon-circle">
          <img src="./images/scan_id_icon.svg" alt="证件扫描" class="privilege-icon" />
        </div>
        <span class="icon-label">证件扫描</span>
      </div>

      <div class="privilege-item">
        <div class="icon-circle">
          <img src="./images/pdf_word_icon.svg" alt="PDF转Word" class="privilege-icon" />
        </div>
        <span class="icon-label">PDF转Word</span>
      </div>

      <div class="privilege-item">
        <div class="icon-circle">
          <img src="./images/scan_excel_icon.svg" alt="表格识别" class="privilege-icon" />
        </div>
        <span class="icon-label">表格识别</span>
      </div>

      <div class="privilege-item">
        <div class="icon-circle">
          <img src="./images/remove_ad_icon.svg" alt="去广告" class="privilege-icon" />
        </div>
        <span class="icon-label">去广告</span>
      </div>

      <div class="privilege-item">
        <div class="icon-circle">
          <img src="./images/folder_icon.svg" alt="文档管理" class="privilege-icon" />
        </div>
        <span class="icon-label">文档管理</span>
      </div>
    </div>

    <!-- 返回按钮 - 精确位置 (16,778,24,24) -->
    <div class="back-button">
      <img src="./images/back_icon.svg" alt="返回" class="back-icon" />
    </div>

    <!-- Home Indicator - 精确位置 (121,799,134,5) -->
    <div class="home-indicator"></div>
  </div>
</template>

<script>
export default {
  name: 'ExchangeSuccess',
  props: {
    accountEmail: {
      type: String,
      default: '<EMAIL>'
    },
    validityDate: {
      type: String,
      default: '2026-09-06'
    }
  },
  emits: ['back', 'continue'],
  methods: {
    handleBack() {
      this.$emit('back')
    },
    handleContinue() {
      this.$emit('continue')
    }
  }
}
</script>

<style scoped>
/* 基础容器 - 精确匹配Figma Frame (0,0,375,812) */
.exchange-success {
  position: relative;
  width: 375px;
  height: 812px;
  background: #1E2334;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  overflow: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
}

/* 状态栏 - 精确匹配Figma Status Bar (0,0,375,44) */
.status-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 375px;
  height: 44px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 21px;
  background: #1E2334;
}

/* 时间显示 - 基于Figma Time组件 (21,12,54,21) */
.time {
  position: absolute;
  left: 21px;
  top: 12px;
  width: 54px;
  height: 21px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  font-size: 14px;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 右侧状态图标区域 - 基于Figma Right Side (293.67,17.33,66.66,11.34) */
.status-icons {
  position: absolute;
  right: 15px;
  top: 17px;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* 信号强度 - 基于Figma Mobile Signal */
.signal-bars {
  width: 17px;
  height: 11px;
  display: flex;
  align-items: flex-end;
  gap: 1px;
}

.bar {
  background: #FFFFFF;
  border-radius: 0.5px;
}

.bar1 { width: 3px; height: 3px; }
.bar2 { width: 3px; height: 5px; }
.bar3 { width: 3px; height: 7px; }
.bar4 { width: 3px; height: 9px; }

/* WiFi图标 - 基于Figma Wifi */
.wifi-icon {
  width: 15px;
  height: 11px;
  position: relative;
}

.wifi-arc {
  position: absolute;
  border: 1px solid #FFFFFF;
  border-bottom: none;
  border-radius: 50% 50% 0 0;
}

.wifi-arc1 { width: 6px; height: 3px; bottom: 0; left: 4.5px; }
.wifi-arc2 { width: 9px; height: 4.5px; bottom: 0; left: 3px; }
.wifi-arc3 { width: 12px; height: 6px; bottom: 0; left: 1.5px; }

/* 电池 - 基于Figma Battery */
.battery {
  width: 24px;
  height: 11px;
  position: relative;
}

.battery-body {
  width: 22px;
  height: 11px;
  border: 1px solid #FFFFFF;
  border-radius: 2px;
  opacity: 0.35;
}

.battery-tip {
  position: absolute;
  right: -1px;
  top: 3.5px;
  width: 1px;
  height: 4px;
  background: #FFFFFF;
  border-radius: 0 1px 1px 0;
  opacity: 0.4;
}

.battery-level {
  position: absolute;
  left: 2px;
  top: 2px;
  width: 18px;
  height: 7px;
  background: #FFFFFF;
  border-radius: 1px;
}

/* Logo区域 - 精确匹配Figma LOGO 组合 (120,104,134,32) */
.logo-section {
  position: absolute;
  top: 104px;
  left: 120px;
  width: 134px;
  height: 32px;
}

.logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 成功提示区域 - 精确匹配Figma layout_R3PVPS (73.5,160,228,28) */
.success-section {
  position: absolute;
  top: 160px;
  left: 73.5px;
  width: 228px;
  height: 28px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 成功图标容器 - 基于Figma layout_UTLUYM (28x28) */
.success-icon-container {
  width: 28px;
  height: 28px;
  background: linear-gradient(180deg, #F6E2A7 0%, #CCAD74 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* 成功图标 - 基于Figma layout_0MVVGG (21x15.75) */
.success-icon {
  width: 21px;
  height: 15.75px;
  object-fit: contain;
}

/* 成功标题 - 基于Figma style_1LDQVS */
.success-title {
  font-family: 'HarmonyOS Sans SC', 'PingFang SC', sans-serif;
  font-weight: 700;
  font-size: 24px;
  line-height: 1.5em;
  background: linear-gradient(180deg, #F6E2A7 0%, #CCAD74 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  margin: 0;
  white-space: nowrap;
}

/* 会员卡片区域 - 精确匹配Figma layout_IARWWT (14,256,347,295) */
.card-container {
  position: absolute;
  top: 256px;
  left: 14px;
  width: 347px;
  height: 295px;
}

/* 卡片阴影 - 基于Figma layout_IARWWT阴影效果 */
.card-shadow {
  position: absolute;
  top: 53px;
  left: 17.5px;
  width: 311px;
  height: 220px;
  background: #30384E;
  border-radius: 8px;
  z-index: 1;
}

/* 主卡片 - 基于Figma layout_IARWWT主体 */
.card-main {
  position: absolute;
  top: 31.5px;
  left: 32px;
  width: 283px;
  height: 212px;
  background: linear-gradient(180deg, #FFF3CE 0%, #F6E1A7 100%);
  border-radius: 8px;
  z-index: 2;
  overflow: hidden;
}

/* 卡片装饰图 - 基于Figma layout_IARWWT装饰 */
.card-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 208px;
  height: 151px;
  opacity: 0.6;
  object-fit: cover;
  z-index: 1;
}

/* 卡片内容 - 基于Figma文字布局 */
.card-content {
  position: relative;
  z-index: 3;
  padding: 24px 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 账户文字 - 基于Figma style_QXZQRD */
.account-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.5em;
  color: #1E2334;
  width: 172px;
}

/* 有效期文字 - 基于Figma style_QXZQRD */
.validity-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.5em;
  color: #1E2334;
  width: 172px;
}

/* 卡片边框 - 基于Figma边框效果 */
.card-border {
  position: absolute;
  top: 4px;
  left: 4px;
  right: 4px;
  bottom: 4px;
  border: 1px solid #E8CB91;
  border-radius: 6px;
  pointer-events: none;
  z-index: 4;
}

/* 特权标题区域 - 基于Figma layout_RPMZ4E */
.privilege-header {
  position: absolute;
  top: 573px;
  left: 24px;
  width: 327px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* 星星装饰 - 基于Figma星星组件 */
.star-decoration {
  width: 20px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.star-shape {
  width: 12px;
  height: 12px;
  background: #D5B87F;
  clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
}

/* 特权标题文字 - 基于Figma style_1LDQVS */
.privilege-title {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 18px;
  line-height: 1.4444444444444444em;
  color: #D5B87F;
  text-align: center;
  margin: 0;
}

/* 特权功能图标区域 - 基于Figma layout_RPMZ4E (24,613,327,86) */
.privilege-icons {
  position: absolute;
  top: 613px;
  left: 24px;
  width: 327px;
  height: 86px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

/* 特权项目 - 基于Figma layout_RPMZ4E子项 */
.privilege-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  width: 56px;
}

/* 图标圆圈 - 基于Figma layout_UTLUYM */
.icon-circle {
  width: 40px;
  height: 40px;
  background: #333949;
  border: 1px solid #5B6171;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 特权图标 - 基于Figma图标尺寸 */
.privilege-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

/* 图标标签 - 基于Figma style_QXZQRD */
.icon-label {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.3333333333333333em;
  color: #D5B87F;
  text-align: center;
  width: 56px;
}

/* 返回按钮 - 精确匹配Figma layout_UTLUYM (16,778,24,24) */
.back-button {
  position: absolute;
  top: 778px;
  left: 16px;
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.back-icon {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Home Indicator - 精确匹配Figma Home Indicator (121,799,134,5) */
.home-indicator {
  position: absolute;
  top: 799px;
  left: 121px;
  width: 134px;
  height: 5px;
  background: #FFFFFF;
  border-radius: 2.5px;
  opacity: 0.3;
}

/* 确保所有元素精确定位，无动画干扰 */
* {
  box-sizing: border-box;
}

/* 禁用所有过渡和动画以确保截图一致性 */
*, *::before, *::after {
  transition: none !important;
  animation: none !important;
}
</style>