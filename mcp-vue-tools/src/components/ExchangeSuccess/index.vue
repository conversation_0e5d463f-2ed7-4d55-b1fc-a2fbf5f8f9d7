<template>
  <div class="exchange-success">
    <!-- 状态栏 - 精确匹配Figma Status Bar -->
    <div class="status-bar">
      <div class="time">9:41</div>
      <div class="status-icons">
        <div class="signal-bars">
          <div class="bar bar1"></div>
          <div class="bar bar2"></div>
          <div class="bar bar3"></div>
          <div class="bar bar4"></div>
        </div>
        <div class="wifi-icon">
          <div class="wifi-arc wifi-arc1"></div>
          <div class="wifi-arc wifi-arc2"></div>
          <div class="wifi-arc wifi-arc3"></div>
        </div>
        <div class="battery">
          <div class="battery-body"></div>
          <div class="battery-tip"></div>
          <div class="battery-level"></div>
        </div>
      </div>
    </div>

    <!-- Group 46 - 背景区域 (0,44,375,44) -->
    <div class="header-background">
      <div class="header-rect"></div>
      <img src="./images/back_icon.svg" alt="返回" class="header-back-icon" />
    </div>

    <!-- Logo区域 - 精确位置 (120,104,134,32) -->
    <div class="logo-section">
      <img src="./images/logo.svg" alt="扫描全能王" class="logo" />
    </div>

    <!-- Frame 427318326 - 成功提示容器 (73.5,160) -->
    <div class="success-container">
      <div class="success-icon-container">
        <img src="./images/success_icon.svg" alt="成功" class="success-icon" />
      </div>
      <h1 class="success-title">高级会员兑换成功</h1>
    </div>

    <!-- 下载提示文字 (47,208,281,24) -->
    <div class="download-hint">下载扫描全能王 App 查看高级会员特权</div>

    <!-- 会员卡片区域 - 基于正确的Figma切图 (14,256,347,295) -->
    <div class="card-container">
      <!-- Frame 2 - 阴影背景 (17.5,53,311,220) -->
      <div class="card-shadow-frame"></div>

      <!-- Group 427318323 - 复杂渐变背景 (17.5,128,311,148) -->
      <div class="card-gradient-bg">
        <!-- 渐变背景矩形 -->
        <div class="gradient-rect"></div>
        <!-- 左右模糊椭圆 -->
        <div class="blur-ellipse blur-left"></div>
        <div class="blur-ellipse blur-right"></div>
        <!-- 底部椭圆 -->
        <div class="bottom-ellipse"></div>
      </div>

      <!-- 主卡片 Group ********* (31.5,0,283,212) -->
      <div class="card-main">
        <!-- 卡片装饰图 Vector 117 (31.5,0,208,151) -->
        <img src="./images/card_decoration.svg" alt="装饰" class="card-decoration" />

        <!-- Union装饰元素 (218.5,77.16,94.26,89.84) -->
        <div class="card-union-decoration"></div>

        <!-- Frame ********** - 账户信息容器 (16,24,172,auto) -->
        <div class="card-account-info">
          <div class="account-text">帐户：<br/><EMAIL></div>
          <div class="validity-text">有效期至：<br/>2026-09-06</div>
        </div>

        <!-- 卡片边框 Rectangle ******** (35.5,4,275,204) -->
        <div class="card-border"></div>
      </div>

      <!-- Group ********* - 左侧装饰 (16,246.5,43.83,41.5) -->
      <div class="card-decoration-left">
        <img src="./images/card_image_left.png" alt="左装饰" class="decoration-image-left" />
      </div>

      <!-- Group ********* - 右侧装饰 (252.5,170.89,97.5,124.61) -->
      <div class="card-decoration-right">
        <img src="./images/card_image_right.png" alt="右装饰" class="decoration-image-right" />
      </div>
    </div>

    <!-- 特权标题区域 -->
    <div class="privilege-header">
      <div class="star-decoration star-left">
        <div class="star-shape"></div>
      </div>
      <span class="privilege-title">尊享 20+ 高级会员特权</span>
      <div class="star-decoration star-right">
        <div class="star-shape"></div>
      </div>
    </div>

    <!-- 特权功能图标区域 - flex两行布局 -->
    <div class="privilege-icons">
      <!-- 第一行 -->
      <div class="privilege-row">
        <div class="privilege-item">
          <div class="icon-circle">
            <img src="./images/scan_id_icon.svg" alt="证件扫描" class="privilege-icon" />
          </div>
          <span class="icon-label">证件扫描</span>
        </div>

        <div class="privilege-item">
          <div class="icon-circle">
            <img src="./images/pdf_word_icon.svg" alt="PDF功能" class="privilege-icon" />
          </div>
          <span class="icon-label">PDF功能</span>
        </div>

        <div class="privilege-item">
          <div class="icon-circle">
            <img src="./images/scan_excel_icon.svg" alt="拍图识字" class="privilege-icon" />
          </div>
          <span class="icon-label">拍图识字</span>
        </div>

        <div class="privilege-item">
          <div class="icon-circle">
            <img src="./images/remove_ad_icon.svg" alt="去水印" class="privilege-icon" />
          </div>
          <span class="icon-label">去水印</span>
        </div>
      </div>

      <!-- 第二行 -->
      <div class="privilege-row">
        <div class="privilege-item">
          <div class="icon-circle">
            <img src="./images/pdf_word_icon.svg" alt="PDF转Word" class="privilege-icon" />
          </div>
          <span class="icon-label">PDF转Word</span>
        </div>

        <div class="privilege-item">
          <div class="icon-circle">
            <img src="./images/scan_excel_icon.svg" alt="表格识别" class="privilege-icon" />
          </div>
          <span class="icon-label">表格识别</span>
        </div>

        <div class="privilege-item">
          <div class="icon-circle">
            <img src="./images/folder_icon.svg" alt="文档管理" class="privilege-icon" />
          </div>
          <span class="icon-label">文档管理</span>
        </div>

        <div class="privilege-item">
          <div class="icon-circle">
            <img src="./images/remove_ad_icon.svg" alt="去广告" class="privilege-icon" />
          </div>
          <span class="icon-label">去广告</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ExchangeSuccess',
  props: {
    accountEmail: {
      type: String,
      default: '<EMAIL>'
    },
    validityDate: {
      type: String,
      default: '2026-09-06'
    }
  },
  emits: ['back', 'continue'],
  methods: {
    handleBack() {
      this.$emit('back')
    },
    handleContinue() {
      this.$emit('continue')
    }
  }
}
</script>

<style scoped>
/* 基础容器 - 精确匹配Figma Frame (0,0,375,812) fill_OTCD3J */
.exchange-success {
  position: relative;
  width: 375px;
  height: 812px;
  background: #1E2334; /* 精确匹配Figma fill_OTCD3J */
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  overflow: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 状态栏 - 精确匹配Figma Status Bar (0,0,375,44) */
.status-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 375px;
  height: 44px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 21px;
  background: #1E2334;
}

/* 时间显示 - 基于Figma Time组件 (21,12,54,21) */
.time {
  position: absolute;
  left: 21px;
  top: 12px;
  width: 54px;
  height: 21px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  font-size: 14px;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 右侧状态图标区域 - 基于Figma Right Side (293.67,17.33,66.66,11.34) */
.status-icons {
  position: absolute;
  right: 15px;
  top: 17px;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* 信号强度 - 基于Figma Mobile Signal */
.signal-bars {
  width: 17px;
  height: 11px;
  display: flex;
  align-items: flex-end;
  gap: 1px;
}

.bar {
  background: #FFFFFF;
  border-radius: 0.5px;
}

.bar1 { width: 3px; height: 3px; }
.bar2 { width: 3px; height: 5px; }
.bar3 { width: 3px; height: 7px; }
.bar4 { width: 3px; height: 9px; }

/* WiFi图标 - 基于Figma Wifi */
.wifi-icon {
  width: 15px;
  height: 11px;
  position: relative;
}

.wifi-arc {
  position: absolute;
  border: 1px solid #FFFFFF;
  border-bottom: none;
  border-radius: 50% 50% 0 0;
}

.wifi-arc1 { width: 6px; height: 3px; bottom: 0; left: 4.5px; }
.wifi-arc2 { width: 9px; height: 4.5px; bottom: 0; left: 3px; }
.wifi-arc3 { width: 12px; height: 6px; bottom: 0; left: 1.5px; }

/* 电池 - 基于Figma Battery */
.battery {
  width: 24px;
  height: 11px;
  position: relative;
}

.battery-body {
  width: 22px;
  height: 11px;
  border: 1px solid #FFFFFF;
  border-radius: 2px;
  opacity: 0.35;
}

.battery-tip {
  position: absolute;
  right: -1px;
  top: 3.5px;
  width: 1px;
  height: 4px;
  background: #FFFFFF;
  border-radius: 0 1px 1px 0;
  opacity: 0.4;
}

.battery-level {
  position: absolute;
  left: 2px;
  top: 2px;
  width: 18px;
  height: 7px;
  background: #FFFFFF;
  border-radius: 1px;
}

/* Group 46 - 背景区域 (0,44,375,44) */
.header-background {
  position: absolute;
  top: 44px;
  left: 0;
  width: 375px;
  height: 44px;
}

.header-rect {
  position: absolute;
  top: 0;
  left: 0;
  width: 375px;
  height: 44px;
  background: #1E2334; /* 与主背景相同 */
}

.header-back-icon {
  position: absolute;
  top: 10px;
  left: 16px;
  width: 24px;
  height: 24px;
}

/* Logo区域 - 精确匹配Figma LOGO 组合 (120,104,134,32) */
.logo-section {
  position: absolute;
  top: 104px;
  left: 120px;
  width: 134px;
  height: 32px;
}

.logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Frame 427318326 - 成功提示容器 (73.5,160) */
.success-container {
  position: absolute;
  top: 160px;
  left: 73.5px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 下载提示文字 (47,208,281,24) */
.download-hint {
  position: absolute;
  top: 208px;
  left: 47px;
  width: 281px;
  height: 24px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.5em;
  color: #D5B87F;
  text-align: center;
}

/* 成功图标容器 - 基于Figma layout_UTLUYM (28x28) */
.success-icon-container {
  width: 28px;
  height: 28px;
  background: linear-gradient(180deg, #F6E2A7 0%, #CCAD74 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* 成功图标 - 基于Figma layout_0MVVGG (21x15.75) */
.success-icon {
  width: 21px;
  height: 15.75px;
  object-fit: contain;
}

/* 成功标题 - 基于Figma style_1LDQVS */
.success-title {
  font-family: 'HarmonyOS Sans SC', 'PingFang SC', sans-serif;
  font-weight: 700;
  font-size: 24px;
  line-height: 1.5em;
  background: linear-gradient(180deg, #F6E2A7 0%, #CCAD74 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  margin: 0;
  white-space: nowrap;
}

/* 会员卡片区域 - 精确匹配Figma Frame 1000005816 (14,256,347,295) */
.card-container {
  position: absolute;
  top: 256px;
  left: 14px;
  width: 347px;
  height: 295px;
}

/* Frame 2 - 阴影背景 (17.5,53,311,220) */
.card-shadow-frame {
  position: absolute;
  top: 53px;
  left: 17.5px;
  width: 311px;
  height: 220px;
  background: #30384E;
  border-radius: 8px;
  z-index: 1;
}

/* Group 427318323 - 复杂渐变背景 (17.5,128,311,148) */
.card-gradient-bg {
  position: absolute;
  top: 128px;
  left: 17.5px;
  width: 311px;
  height: 148px;
  z-index: 2;
}

/* 渐变背景矩形 */
.gradient-rect {
  position: absolute;
  top: 5.55px;
  left: 0;
  width: 311px;
  height: 139.45px;
  background: radial-gradient(circle at 50% 50%, #39425B 0%, #252B3D 100%);
  border-radius: 8px;
}

/* 模糊椭圆效果 */
.blur-ellipse {
  position: absolute;
  top: 3px;
  width: 6px;
  height: 145px;
  background: #30384D;
  filter: blur(8px);
}

.blur-left {
  left: 0;
}

.blur-right {
  right: 0;
}

/* 底部椭圆 */
.bottom-ellipse {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 311px;
  height: 6px;
  background: #212638;
  filter: blur(8px);
}

/* 主卡片 - 精确匹配Figma fill_5ADPAK渐变 */
.card-main {
  position: absolute;
  top: 31.5px;
  left: 32px;
  width: 283px;
  height: 212px;
  background: linear-gradient(180deg, #FFF3CE 0%, #F6E1A7 100%);
  border-radius: 8px;
  z-index: 2;
  overflow: hidden;
}

/* 卡片装饰图 - 精确匹配Figma坐标 (45.5,256,208,151) */
.card-decoration {
  position: absolute;
  top: -31.5px; /* 256 - 287.5 = -31.5 (相对于card-main的top: 287.5px) */
  left: 13.5px; /* 45.5 - 32 = 13.5 (相对于card-main的left: 32px) */
  width: 208px;
  height: 151px;
  opacity: 0.6;
  object-fit: cover;
  z-index: 1;
}

/* Union装饰元素 - 基于Figma Union (218.5,77.16,94.26,89.84) */
.card-union-decoration {
  position: absolute;
  top: 77.16px;
  left: 187px; /* 218.5 - 31.5 = 187 (相对于card-main) */
  width: 94.26px;
  height: 89.84px;
  background: linear-gradient(135deg, rgba(232, 203, 145, 0.4) 0%, rgba(232, 203, 145, 0) 100%);
  border: 0.88px solid;
  border-image: linear-gradient(135deg, #E8CB91 0%, rgba(232, 203, 145, 0) 100%) 1;
  border-radius: 4px;
  z-index: 2;
}

/* Frame ********** - 账户信息容器 (16,24,172,auto) */
.card-account-info {
  position: absolute;
  top: 24px;
  left: 16px;
  width: 172px;
  z-index: 3;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 账户文字 - 基于Figma文字样式 */
.account-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.5em;
  color: #1E2334;
}

/* 有效期文字 - 基于Figma文字样式 */
.validity-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.5em;
  color: #1E2334;
}

/* 卡片边框 - Rectangle ******** (35.5,4,275,204) */
.card-border {
  position: absolute;
  top: 4px;
  left: 4px; /* 35.5 - 31.5 = 4 (相对于card-main) */
  width: 275px;
  height: 204px;
  border: 1px solid #E8CB91;
  border-radius: 6px;
  pointer-events: none;
  z-index: 4;
}

/* Group ********* - 左侧装饰 (16,246.5,43.83,41.5) */
.card-decoration-left {
  position: absolute;
  top: 246.5px;
  left: 16px;
  width: 43.83px;
  height: 41.5px;
  z-index: 5;
}

.decoration-image-left {
  position: absolute;
  top: -10.04px;
  left: -18.2px;
  width: 62.6px;
  height: 60.57px;
  object-fit: cover;
}

/* Group ********* - 右侧装饰 (252.5,170.89,97.5,124.61) */
.card-decoration-right {
  position: absolute;
  top: 170.89px;
  left: 252.5px;
  width: 97.5px;
  height: 124.61px;
  z-index: 5;
}

.decoration-image-right {
  position: absolute;
  top: -39.59px;
  left: -50.79px;
  width: 172.06px;
  height: 196.1px;
  object-fit: cover;
}

/* 特权标题区域 - 基于Figma layout_RPMZ4E */
.privilege-header {
  position: absolute;
  top: 573px;
  left: 24px;
  width: 327px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* 星星装饰 - 基于Figma星星组件 */
.star-decoration {
  width: 20px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.star-shape {
  width: 12px;
  height: 12px;
  background: #D5B87F;
  clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
}

/* 特权标题文字 - 基于Figma style_1LDQVS */
.privilege-title {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 18px;
  line-height: 1.4444444444444444em;
  color: #D5B87F;
  text-align: center;
  margin: 0;
}

/* 特权功能图标区域 - flex两行布局 */
.privilege-icons {
  position: absolute;
  top: 613px; /* 基于Figma Frame 1000005811的位置 */
  left: 24px;
  width: 327px;
  display: flex;
  flex-direction: column;
  gap: 20px; /* 两行之间的间距 */
}

/* 特权行 - flex水平布局 */
.privilege-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

/* 特权项目 - flex垂直布局 */
.privilege-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  width: 66px;
}

/* 图标圆圈 - 基于Figma layout_UTLUYM */
.icon-circle {
  width: 40px;
  height: 40px;
  background: #333949;
  border: 1px solid #5B6171;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 特权图标 - 基于Figma图标尺寸 */
.privilege-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

/* 图标标签 - 基于Figma style_QXZQRD */
.icon-label {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.3333333333333333em;
  color: #D5B87F;
  text-align: center;
  width: 56px;
}

/* 返回按钮 - 精确匹配Figma layout_UTLUYM (16,778,24,24) */
.back-button {
  position: absolute;
  top: 778px;
  left: 16px;
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.back-icon {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Home Indicator - 精确匹配Figma Home Indicator (121,799,134,5) */
.home-indicator {
  position: absolute;
  top: 799px;
  left: 121px;
  width: 134px;
  height: 5px;
  background: #FFFFFF;
  border-radius: 2.5px;
  opacity: 0.3;
}

/* 确保所有元素精确定位，无动画干扰 */
* {
  box-sizing: border-box;
}

/* 禁用所有过渡和动画以确保截图一致性 */
*, *::before, *::after {
  transition: none !important;
  animation: none !important;
}
</style>