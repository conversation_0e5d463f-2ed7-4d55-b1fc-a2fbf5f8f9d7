<template>
  <div class="exchange-success">
    <!-- 状态栏 - 精确位置 (0,0,375,44) -->
    <div class="status-bar">
      <div class="status-content">
        <div class="time">9:41</div>
        <div class="status-icons">
          <div class="signal-bars">
            <div class="bar bar-1"></div>
            <div class="bar bar-2"></div>
            <div class="bar bar-3"></div>
            <div class="bar bar-4"></div>
          </div>
          <div class="wifi-icon">
            <svg width="15" height="11" viewBox="0 0 15 11" fill="none">
              <path d="M1 8.5C3.5 6 6.5 6 9 8.5M3 10.5C4.5 9 5.5 9 7 10.5M11 6.5C12.5 5 13.5 5 15 6.5" stroke="white" stroke-width="1"/>
            </svg>
          </div>
          <div class="battery">
            <div class="battery-body"></div>
            <div class="battery-tip"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Logo区域 - 精确位置 (120,104,134,32) -->
    <div class="logo-section">
      <img src="./images/logo.svg" alt="扫描全能王" class="logo" />
    </div>

    <!-- 成功提示区域 - 精确位置 (73.5,160,228,28) -->
    <div class="success-section">
      <div class="success-indicator">
        <div class="success-icon-circle">
          <img src="./images/success_icon.svg" alt="成功" class="success-icon" />
        </div>
        <h1 class="success-title">高级会员兑换成功</h1>
      </div>
      <p class="success-subtitle">下载扫描全能王 App 查看高级会员特权</p>
    </div>

    <!-- 会员卡片区域 - 精确位置 (14,256,347,295) -->
    <div class="member-card-area">
      <!-- 卡片阴影 - 精确位置 (31.5,309,311,220) -->
      <div class="card-shadow"></div>
      
      <!-- 主卡片 - 精确位置 (45.5,256,283,212) -->
      <div class="member-card">
        <!-- 卡片背景装饰 - 精确位置 (45.5,256,208,151) -->
        <div class="card-background">
          <img src="./images/card_decoration.svg" alt="装饰" class="decoration" />
        </div>
        
        <!-- 卡片边框 - 精确位置 (49.5,260,275,204) -->
        <div class="card-border"></div>
        
        <!-- 卡片内容 -->
        <div class="card-content">
          <!-- 账户信息 -->
          <div class="account-info">
            <div class="account-text">
              帐户：<br>
              <EMAIL>
            </div>
            <div class="validity-text">
              有效期至：<br>
              2026-09-06
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 特权功能区域 - 精确位置 (24,573,327,86) -->
    <div class="privileges-area">
      <div class="privileges-header">
        <div class="star-left">
          <svg width="20" height="12" viewBox="0 0 20 12" fill="none">
            <path d="M6 0L7.5 4.5H12L8.5 7.5L10 12L6 9L2 12L3.5 7.5L0 4.5H4.5L6 0Z" fill="#FBDEBC"/>
            <path d="M14 2L15 5H18L15.5 7L16.5 10L14 8L11.5 10L12.5 7L10 5H13L14 2Z" fill="#FBDEBC"/>
          </svg>
        </div>
        <span class="privileges-title">尊享 20+ 高级会员特权</span>
        <div class="star-right">
          <svg width="20" height="12" viewBox="0 0 20 12" fill="none">
            <path d="M6 0L7.5 4.5H12L8.5 7.5L10 12L6 9L2 12L3.5 7.5L0 4.5H4.5L6 0Z" fill="#FBDEBC"/>
            <path d="M14 2L15 5H18L15.5 7L16.5 10L14 8L11.5 10L12.5 7L10 5H13L14 2Z" fill="#FBDEBC"/>
          </svg>
        </div>
      </div>

      <!-- 功能网格 -->
      <div class="privileges-grid">
        <!-- 第一行 -->
        <div class="privilege-row">
          <div class="privilege-item">
            <div class="privilege-icon">
              <img src="./images/scan_id_icon.svg" alt="证件扫描" />
            </div>
            <span class="privilege-label">证件扫描</span>
          </div>
          <div class="privilege-item">
            <div class="privilege-icon">
              <img src="./images/pdf_word_icon.svg" alt="PDF转Word" />
            </div>
            <span class="privilege-label">PDF转Word</span>
          </div>
          <div class="privilege-item">
            <div class="privilege-icon">
              <img src="./images/table_recognition_icon.svg" alt="表格识别" />
            </div>
            <span class="privilege-label">表格识别</span>
          </div>
          <div class="privilege-item">
            <div class="privilege-icon">
              <img src="./images/remove_ad_icon.svg" alt="去广告" />
            </div>
            <span class="privilege-label">去广告</span>
          </div>
        </div>

        <!-- 第二行 -->
        <div class="privilege-row">
          <div class="privilege-item">
            <div class="privilege-icon">
              <img src="./images/document_manage_icon.svg" alt="文档管理" />
            </div>
            <span class="privilege-label">文档管理</span>
          </div>
          <div class="privilege-item">
            <div class="privilege-icon plus">
              <span>+</span>
            </div>
            <span class="privilege-label">更多特权</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮区域 -->
    <div class="bottom-actions">
      <button class="download-button" @click="handleDownload">
        立即下载 App
      </button>
      <button class="back-button" @click="handleBack">
        <img src="./images/back_icon.svg" alt="返回" class="back-icon" />
        返回
      </button>
    </div>

    <!-- Home指示器 - 精确位置 (121,799,134,5) -->
    <div class="home-indicator"></div>
  </div>
</template>

<script>
export default {
  name: 'ExchangeSuccess',
  emits: ['download', 'back'],
  methods: {
    handleDownload() {
      this.$emit('download')
    },
    handleBack() {
      this.$emit('back')
    }
  }
}
</script>

<style scoped>
/* 基础容器 - 精确匹配Figma (375×812) */
.exchange-success {
  width: 375px;
  height: 812px;
  position: relative;
  background: #1E2334;
  font-family: 'HarmonyOS Sans SC', 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  overflow: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 状态栏 - 精确位置 (0,0,375,44) */
.status-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 375px;
  height: 44px;
  z-index: 100;
}

.status-content {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 21px;
  box-sizing: border-box;
}

.time {
  font-size: 17px;
  font-weight: 600;
  color: #FFFFFF;
  line-height: 22px;
  letter-spacing: -0.43px;
}

.status-icons {
  display: flex;
  align-items: center;
  gap: 5px;
}

.signal-bars {
  display: flex;
  align-items: flex-end;
  gap: 2px;
  width: 18px;
  height: 12px;
}

.bar {
  background: #FFFFFF;
  border-radius: 1px;
}

.bar-1 { width: 3px; height: 4px; }
.bar-2 { width: 3px; height: 6px; }
.bar-3 { width: 3px; height: 8px; }
.bar-4 { width: 3px; height: 10px; }

.wifi-icon {
  width: 15px;
  height: 11px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.battery {
  width: 27px;
  height: 13px;
  position: relative;
  display: flex;
  align-items: center;
}

.battery-body {
  width: 22px;
  height: 11px;
  border: 1px solid #FFFFFF;
  border-radius: 2px;
  background: #FFFFFF;
}

.battery-tip {
  width: 1px;
  height: 4px;
  background: #FFFFFF;
  border-radius: 0 1px 1px 0;
  margin-left: 1px;
}

/* Logo区域 - 精确位置 (120,104,134,32) */
.logo-section {
  position: absolute;
  top: 104px;
  left: 120px;
  width: 134px;
  height: 32px;
}

.logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 成功提示区域 - 精确位置 (73.5,160,228,28) */
.success-section {
  position: absolute;
  top: 160px;
  left: 73.5px;
  width: 228px;
  height: 28px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.success-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.success-icon-circle {
  width: 28px;
  height: 28px;
  background: linear-gradient(180deg, #F6E2A7 0%, #CCAD74 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.success-icon {
  width: 21px;
  height: 15.75px;
  object-fit: contain;
}

.success-title {
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(180deg, #F6E2A7 0%, #CCAD74 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  line-height: 36px;
  margin: 0;
  text-align: center;
}

.success-subtitle {
  position: absolute;
  top: 48px;
  left: -26.5px;
  width: 281px;
  height: 24px;
  font-family: 'PingFang SC', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #D5B87F;
  line-height: 24px;
  text-align: center;
  margin: 0;
}

/* 会员卡片区域 - 精确位置 (14,256,347,295) */
.member-card-area {
  position: absolute;
  top: 256px;
  left: 14px;
  width: 347px;
  height: 295px;
}

.card-shadow {
  position: absolute;
  top: 53px;
  left: 17.5px;
  width: 311px;
  height: 220px;
  background: #30384E;
  border-radius: 8px;
}

.member-card {
  position: absolute;
  top: 0;
  left: 31.5px;
  width: 283px;
  height: 212px;
  border-radius: 8px;
  overflow: hidden;
}

.card-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, #FFF3CE 0%, #F6E1A7 85.85%);
  border-radius: 8px;
}

.decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 208px;
  height: 151px;
  object-fit: cover;
  opacity: 0.6;
}

.card-border {
  position: absolute;
  top: 4px;
  left: 4px;
  width: 275px;
  height: 204px;
  border: 1px solid #E8CB91;
  border-radius: 6px;
  pointer-events: none;
}

.card-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 24px 16px;
  box-sizing: border-box;
  z-index: 2;
}

.account-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.account-text,
.validity-text {
  font-family: 'PingFang SC', sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: #1E2334;
  line-height: 24px;
}

/* 特权功能区域 - 精确位置 (24,573,327,86) */
.privileges-area {
  position: absolute;
  top: 573px;
  left: 24px;
  width: 327px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.privileges-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.star-left,
.star-right {
  width: 20px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.privileges-title {
  font-family: 'PingFang SC', sans-serif;
  font-size: 18px;
  font-weight: 500;
  color: #D5B87F;
  line-height: 26px;
  text-align: center;
}

.privileges-grid {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 21px;
}

.privilege-row {
  display: flex;
  justify-content: space-around;
  gap: 21px;
}

.privilege-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  flex: 1;
}

.privilege-icon {
  width: 40px;
  height: 40px;
  background: #333949;
  border: 1px solid #5B6171;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.privilege-icon img {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.privilege-icon.plus {
  background: #333949;
}

.privilege-icon.plus span {
  font-size: 20px;
  font-weight: 300;
  color: #D5B87F;
  line-height: 20px;
}

.privilege-label {
  font-family: 'PingFang SC', sans-serif;
  font-size: 12px;
  font-weight: 400;
  color: #D5B87F;
  line-height: 16px;
  text-align: center;
  width: 66px;
}

/* 底部按钮区域 */
.bottom-actions {
  position: absolute;
  bottom: 44px;
  left: 16px;
  width: 343px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.download-button {
  width: 100%;
  height: 48px;
  background: linear-gradient(180deg, #F6E2A7 0%, #CCAD74 100%);
  border: none;
  border-radius: 24px;
  font-family: 'PingFang SC', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #1E2334;
  line-height: 22px;
  cursor: pointer;
}

.back-button {
  width: 100%;
  height: 48px;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-family: 'PingFang SC', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 22px;
  cursor: pointer;
}

.back-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

/* Home指示器 - 精确位置 (121,799,134,5) */
.home-indicator {
  position: absolute;
  bottom: 8px;
  left: 121px;
  width: 134px;
  height: 5px;
  background: #FFFFFF;
  border-radius: 2.5px;
  opacity: 0.3;
}

/* 交互效果 */
.download-button:hover,
.back-button:hover {
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.download-button:active,
.back-button:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

.privilege-item:hover .privilege-icon {
  background: #3d4454;
  transition: background 0.2s ease;
}
</style>
