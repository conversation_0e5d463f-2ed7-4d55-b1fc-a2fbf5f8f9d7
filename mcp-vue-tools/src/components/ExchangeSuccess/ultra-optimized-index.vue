<template>
  <div class="exchange-success-ultra">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-content">
        <div class="time">9:41</div>
        <div class="status-icons">
          <div class="signal-bars">
            <div class="bar bar1"></div>
            <div class="bar bar2"></div>
            <div class="bar bar3"></div>
            <div class="bar bar4"></div>
          </div>
          <div class="wifi-icon">
            <svg width="15" height="11" viewBox="0 0 15 11" fill="none">
              <path d="M1 8.5C3.5 6 6.5 6 9 8.5M3 10.5C4.5 9 5.5 9 7 10.5M11 6.5C12.5 5 13.5 5 15 6.5" stroke="white" stroke-width="1"/>
            </svg>
          </div>
          <div class="battery-icon">
            <div class="battery-body"></div>
            <div class="battery-tip"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-container">
      <!-- Logo区域 -->
      <div class="logo-area">
        <img src="./images/logo.svg" alt="扫描全能王" class="brand-logo" />
      </div>

      <!-- 成功提示区域 -->
      <div class="success-area">
        <div class="success-row">
          <div class="success-badge">
            <img src="./images/success_icon.svg" alt="成功" class="check-icon" />
          </div>
          <h1 class="success-text">高级会员兑换成功</h1>
        </div>
        <p class="success-desc">下载扫描全能王 App 查看高级会员特权</p>
      </div>

      <!-- 会员卡区域 -->
      <div class="card-area">
        <!-- 卡片阴影 -->
        <div class="card-shadow-bg"></div>
        
        <!-- 主卡片 -->
        <div class="member-card-main">
          <!-- 卡片背景 -->
          <div class="card-bg-layer">
            <img src="./images/card_decoration.svg" alt="装饰" class="bg-decoration" />
          </div>
          
          <!-- 卡片内容层 -->
          <div class="card-overlay">
            <!-- 顶部标识 -->
            <div class="card-header">
              <div class="vip-badge">
                <span class="vip-text">高级会员</span>
              </div>
            </div>

            <!-- 用户信息 -->
            <div class="user-section">
              <div class="user-avatar">
                <span class="avatar-initial">王</span>
              </div>
              <div class="user-info">
                <div class="user-name">王小明</div>
                <div class="user-id">ID: 123456789</div>
              </div>
            </div>

            <!-- 有效期信息 -->
            <div class="expiry-section">
              <div class="expiry-label">有效期至</div>
              <div class="expiry-date">2024.12.31</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 特权功能区域 -->
      <div class="privileges-area">
        <div class="privileges-title">高级会员特权</div>
        <div class="privileges-grid">
          <!-- 第一行 -->
          <div class="privilege-row">
            <div class="privilege-item">
              <div class="privilege-icon">
                <img src="./images/pdf_word_icon.svg" alt="PDF转Word" />
              </div>
              <span class="privilege-label">PDF转Word</span>
            </div>
            <div class="privilege-item">
              <div class="privilege-icon">
                <img src="./images/table_recognition_icon.svg" alt="表格识别" />
              </div>
              <span class="privilege-label">表格识别</span>
            </div>
            <div class="privilege-item">
              <div class="privilege-icon">
                <img src="./images/remove_ad_icon.svg" alt="去广告" />
              </div>
              <span class="privilege-label">去广告</span>
            </div>
          </div>

          <!-- 第二行 -->
          <div class="privilege-row">
            <div class="privilege-item">
              <div class="privilege-icon">
                <img src="./images/scan_id_icon.svg" alt="证件扫描" />
              </div>
              <span class="privilege-label">证件扫描</span>
            </div>
            <div class="privilege-item">
              <div class="privilege-icon">
                <img src="./images/document_manage_icon.svg" alt="文档管理" />
              </div>
              <span class="privilege-label">文档管理</span>
            </div>
            <div class="privilege-item">
              <div class="privilege-icon plus-icon">
                <span class="plus-symbol">+</span>
              </div>
              <span class="privilege-label">更多特权</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部按钮区域 -->
      <div class="buttons-area">
        <button class="primary-button" @click="handleDownload">
          <span class="button-label">立即下载 App</span>
        </button>
        <button class="secondary-button" @click="handleBack">
          <img src="./images/back_icon.svg" alt="返回" class="button-icon" />
          <span class="button-label">返回</span>
        </button>
      </div>
    </div>

    <!-- 底部指示器 -->
    <div class="home-indicator-area">
      <div class="home-indicator-bar"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ExchangeSuccessUltraOptimized',
  emits: ['back', 'download'],
  methods: {
    handleBack() {
      this.$emit('back')
    },
    handleDownload() {
      this.$emit('download')
    }
  }
}
</script>

<style scoped>
/* 基础容器 - 精确匹配Figma */
.exchange-success-ultra {
  width: 375px;
  height: 812px;
  position: relative;
  background: #1E2334;
  font-family: 'HarmonyOS Sans SC', 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  overflow: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 状态栏 - 精确定位 */
.status-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 375px;
  height: 44px;
  z-index: 100;
}

.status-content {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 21px 0 21px;
  box-sizing: border-box;
}

.time {
  font-size: 17px;
  font-weight: 600;
  color: #FFFFFF;
  line-height: 22px;
  letter-spacing: -0.43px;
}

.status-icons {
  display: flex;
  align-items: center;
  gap: 5px;
}

.signal-bars {
  display: flex;
  align-items: flex-end;
  gap: 2px;
  width: 18px;
  height: 12px;
}

.bar {
  background: #FFFFFF;
  border-radius: 1px;
}

.bar1 { width: 3px; height: 4px; }
.bar2 { width: 3px; height: 6px; }
.bar3 { width: 3px; height: 8px; }
.bar4 { width: 3px; height: 10px; }

.wifi-icon {
  width: 15px;
  height: 11px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.battery-icon {
  width: 27px;
  height: 13px;
  position: relative;
  display: flex;
  align-items: center;
}

.battery-body {
  width: 22px;
  height: 11px;
  border: 1px solid #FFFFFF;
  border-radius: 2px;
  background: #FFFFFF;
}

.battery-tip {
  width: 1px;
  height: 4px;
  background: #FFFFFF;
  border-radius: 0 1px 1px 0;
  margin-left: 1px;
}

/* 主容器 */
.main-container {
  position: absolute;
  top: 44px;
  left: 0;
  width: 375px;
  height: 734px;
  padding: 60px 14px 34px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Logo区域 */
.logo-area {
  margin-bottom: 60px;
}

.brand-logo {
  width: 134px;
  height: 32px;
  object-fit: contain;
}

/* 成功区域 */
.success-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px;
}

.success-row {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.success-badge {
  width: 28px;
  height: 28px;
  background: linear-gradient(180deg, #F6E2A7 0%, #CCAD74 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-icon {
  width: 21px;
  height: 15.75px;
  object-fit: contain;
}

.success-text {
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(180deg, #F6E2A7 0%, #CCAD74 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  line-height: 36px;
  margin: 0;
}

.success-desc {
  font-family: 'PingFang SC', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #D5B87F;
  line-height: 24px;
  text-align: center;
  margin: 0;
}

/* 会员卡区域 */
.card-area {
  width: 347px;
  height: 295px;
  margin-bottom: 40px;
  position: relative;
}

.card-shadow-bg {
  position: absolute;
  top: 53px;
  left: 17.5px;
  width: 311px;
  height: 220px;
  background: #30384E;
  border-radius: 8px;
}

.member-card-main {
  position: absolute;
  top: 0;
  left: 31.5px;
  width: 283px;
  height: 212px;
  border-radius: 8px;
  overflow: hidden;
}

.card-bg-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, #FFF3CE 0%, #F6E1A7 85.85%);
  border-radius: 8px;
}

.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 208px;
  height: 151px;
  object-fit: cover;
  opacity: 0.3;
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 16px 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 2;
}

.card-header {
  display: flex;
  justify-content: flex-start;
}

.vip-badge {
  background: #30384E;
  border-radius: 12px;
  padding: 4px 12px;
}

.vip-text {
  font-size: 12px;
  font-weight: 600;
  color: #F6E2A7;
  line-height: 16px;
}

.user-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 36px;
  height: 36px;
  background: rgba(30, 35, 52, 0.15);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-initial {
  font-size: 14px;
  font-weight: 600;
  color: #1E2334;
  line-height: 18px;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: #1E2334;
  line-height: 22px;
  margin-bottom: 2px;
}

.user-id {
  font-size: 10px;
  font-weight: 400;
  color: rgba(30, 35, 52, 0.6);
  line-height: 14px;
}

.expiry-section {
  text-align: center;
}

.expiry-label {
  font-size: 10px;
  font-weight: 400;
  color: rgba(30, 35, 52, 0.6);
  line-height: 14px;
  margin-bottom: 2px;
}

.expiry-date {
  font-size: 16px;
  font-weight: 700;
  color: #1E2334;
  line-height: 22px;
}

/* 特权区域 */
.privileges-area {
  width: 100%;
  margin-bottom: 40px;
}

.privileges-title {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  line-height: 24px;
  text-align: center;
  margin-bottom: 24px;
}

.privileges-grid {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.privilege-row {
  display: flex;
  justify-content: space-around;
  gap: 16px;
}

.privilege-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.privilege-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.privilege-icon img {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.privilege-icon.plus-icon {
  background: rgba(255, 255, 255, 0.08);
}

.plus-symbol {
  font-size: 20px;
  font-weight: 300;
  color: #FFFFFF;
  line-height: 20px;
}

.privilege-label {
  font-size: 12px;
  font-weight: 400;
  color: #D5B87F;
  line-height: 16px;
  text-align: center;
}

/* 按钮区域 */
.buttons-area {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: auto;
}

.primary-button {
  width: 100%;
  height: 48px;
  background: linear-gradient(180deg, #F6E2A7 0%, #CCAD74 100%);
  border: none;
  border-radius: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.primary-button .button-label {
  font-size: 16px;
  font-weight: 600;
  color: #1E2334;
  line-height: 22px;
}

.secondary-button {
  width: 100%;
  height: 48px;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.button-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

.secondary-button .button-label {
  font-size: 16px;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 22px;
}

/* 底部指示器 */
.home-indicator-area {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
}

.home-indicator-bar {
  width: 134px;
  height: 5px;
  background: #FFFFFF;
  border-radius: 2.5px;
  opacity: 0.3;
}

/* 交互效果 */
.primary-button:hover,
.secondary-button:hover {
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.primary-button:active,
.secondary-button:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

.privilege-item:hover .privilege-icon {
  background: rgba(255, 255, 255, 0.15);
  transition: background 0.2s ease;
}
</style>
