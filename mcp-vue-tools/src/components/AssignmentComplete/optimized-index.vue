<template>
  <div class="assignment-complete-page">
    <!-- Background -->
    <div class="page-background"></div>
    
    <!-- Top Navigation -->
    <div class="top-navigation">
      <div class="nav-left">
        <div class="back-button">
          <div class="back-icon">
            <svg width="14" height="25" viewBox="0 0 14 25" fill="none">
              <path d="M12.5 2L2 12.5L12.5 23" stroke="#262626" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <span class="back-text">返回</span>
        </div>
      </div>
      <div class="nav-center">
        <h1 class="page-title">2024数学作业第三次作业</h1>
      </div>
      <div class="nav-right">
        <div class="nav-actions">
          <div class="action-button">
            <div class="printer-icon">
              <svg width="28" height="28" viewBox="0 0 28 28" fill="none">
                <rect x="6" y="4" width="16" height="4" fill="#262626"/>
                <rect x="4" y="8" width="20" height="12" rx="2" fill="none" stroke="#262626" stroke-width="2"/>
                <rect x="6" y="16" width="16" height="8" fill="#262626"/>
              </svg>
            </div>
            <span class="action-text">78.3 MB/s</span>
          </div>
          <div class="divider"></div>
          <div class="action-button">
            <div class="user-icon">
              <svg width="28" height="28" viewBox="0 0 28 28" fill="none">
                <circle cx="14" cy="8" r="4" fill="#262626"/>
                <path d="M6 24c0-4.4 3.6-8 8-8s8 3.6 8 8" fill="#262626"/>
              </svg>
            </div>
            <span class="action-text">王小明</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Progress Steps -->
    <div class="progress-steps">
      <div class="step-container">
        <!-- Step 1 - Active -->
        <div class="step-item">
          <div class="step-number active">1</div>
          <div class="step-label active">布置作业</div>
        </div>
        
        <div class="step-connector">
          <div class="connector-line dashed"></div>
          <div class="connector-arrow"></div>
        </div>

        <!-- Step 2 -->
        <div class="step-item">
          <div class="step-number inactive">2</div>
          <div class="step-label inactive">扫描作业</div>
        </div>

        <div class="step-connector">
          <div class="connector-line dashed"></div>
          <div class="connector-arrow"></div>
        </div>

        <!-- Step 3 -->
        <div class="step-item">
          <div class="step-number inactive">3</div>
          <div class="step-label inactive">AI 批改</div>
        </div>

        <div class="step-connector">
          <div class="connector-line dashed"></div>
          <div class="connector-arrow"></div>
        </div>

        <!-- Step 4 -->
        <div class="step-item">
          <div class="step-number inactive">4</div>
          <div class="step-label inactive">打印留痕</div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
      <!-- Left Panel -->
      <div class="left-panel">
        <div class="panel-background">
          <!-- Status Indicators -->
          <div class="status-indicators">
            <div class="status-item active">
              <div class="status-bar"></div>
              <span class="status-text">请编辑作业信息</span>
            </div>
            <div class="status-item completed">
              <div class="status-bar"></div>
              <span class="status-text">已传作业样卷</span>
            </div>
          </div>

          <!-- Preview Images -->
          <div class="preview-images">
            <div class="preview-item">
              <div class="preview-background">
                <div class="preview-image">
                  <div class="image-placeholder"></div>
                  <div class="fullscreen-icon">
                    <svg width="26" height="26" viewBox="0 0 26 26" fill="none">
                      <path d="M8 3H5C3.9 3 3 3.9 3 5V8M21 8V5C21 3.9 20.1 3 19 3H16M16 21H19C20.1 21 21 20.1 21 19V16M3 16V19C3 20.1 3.9 21 5 21H8" stroke="white" stroke-width="2"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
            <div class="preview-item">
              <div class="preview-background dashed-border">
                <div class="preview-image">
                  <div class="image-placeholder light"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- Status Tags -->
          <div class="status-tags">
            <div class="status-tag">
              <span class="tag-text">作业样卷 99张</span>
            </div>
            <div class="status-tag">
              <span class="tag-text">答案卷 1张</span>
            </div>
          </div>

          <!-- Edit Button -->
          <div class="edit-button">
            <span class="edit-text">编辑作业样卷</span>
          </div>
        </div>
      </div>

      <!-- Right Panel -->
      <div class="right-panel">
        <div class="panel-background">
          <!-- Form Fields -->
          <div class="form-fields">
            <!-- Subject Field -->
            <div class="form-field">
              <div class="field-label">
                <span class="required">*</span>
                <span class="label-text">所属学科：</span>
              </div>
              <div class="field-input">
                <div class="select-box">
                  <span class="select-text">语文</span>
                  <div class="select-arrow">
                    <svg width="25" height="17" viewBox="0 0 25 17" fill="none">
                      <path d="M2 2L12.5 12.5L23 2" stroke="rgba(0,0,0,0.25)" stroke-width="2"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            <!-- Grade/Class Field -->
            <div class="form-field">
              <div class="field-label">
                <span class="required">*</span>
                <span class="label-text">年级/班级：</span>
              </div>
              <div class="field-input-group">
                <div class="select-box">
                  <span class="select-text">一年级</span>
                  <div class="select-arrow">
                    <svg width="25" height="17" viewBox="0 0 25 17" fill="none">
                      <path d="M2 2L12.5 12.5L23 2" stroke="rgba(0,0,0,0.25)" stroke-width="2"/>
                    </svg>
                  </div>
                </div>
                <div class="select-box empty">
                  <div class="select-arrow">
                    <svg width="25" height="17" viewBox="0 0 25 17" fill="none">
                      <path d="M2 2L12.5 12.5L23 2" stroke="rgba(0,0,0,0.25)" stroke-width="2"/>
                    </svg>
                  </div>
                </div>
              </div>
              <div class="selected-tags">
                <div class="tag">
                  <span class="tag-text">一年级01..</span>
                </div>
                <div class="tag">
                  <span class="tag-text">+2</span>
                </div>
              </div>
            </div>

            <!-- Assignment Name Field -->
            <div class="form-field">
              <div class="field-label">
                <span class="required">*</span>
                <span class="label-text">作业名称：</span>
              </div>
              <div class="field-input">
                <div class="select-box large">
                  <span class="select-text">化学作业05月21日(周三)</span>
                </div>
              </div>
            </div>

            <!-- AI Correction Field -->
            <div class="form-field">
              <div class="field-label">
                <span class="required">*</span>
                <span class="label-text">智能批改：</span>
              </div>
              <div class="ai-options">
                <div class="ai-option selected">
                  <div class="option-icon">
                    <svg width="26" height="26" viewBox="0 0 26 26" fill="none">
                      <circle cx="13" cy="13" r="13" fill="#04AA65"/>
                      <path d="M8 13L11 16L18 9" stroke="white" stroke-width="2"/>
                    </svg>
                  </div>
                  <span class="option-text">全能王</span>
                </div>
                <div class="ai-option">
                  <span class="option-text">极速版</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Confirm Button -->
    <div class="confirm-button">
      <span class="button-text">确认布置作业</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AssignmentCompleteOptimized',
  data() {
    return {
      selectedSubject: '语文',
      selectedGrade: '一年级',
      assignmentName: '化学作业05月21日(周三)',
      aiMode: 'premium'
    }
  },
  methods: {
    handleConfirm() {
      this.$emit('confirm', {
        subject: this.selectedSubject,
        grade: this.selectedGrade,
        assignmentName: this.assignmentName,
        aiMode: this.aiMode
      })
    },
    handleBack() {
      this.$emit('back')
    }
  }
}
</script>

<style scoped>
/* 基础布局 */
.assignment-complete-page {
  width: 1920px;
  height: 1080px;
  position: relative;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  background: #EEEEEE;
  overflow: hidden;
}

/* 顶部导航 */
.top-navigation {
  position: absolute;
  top: 0;
  left: 0;
  width: 1920px;
  height: 90px;
  background: #FFFFFF;
  border-bottom: 1px solid #E8E8E8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40px 0 16px;
  box-sizing: border-box;
}

.nav-left {
  display: flex;
  align-items: center;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 32px;
  border-radius: 8px;
  cursor: pointer;
  height: 64px;
}

.back-icon {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-text {
  font-weight: 500;
  font-size: 30px;
  line-height: 1.333;
  color: #262626;
  text-align: center;
}

.nav-center {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  height: 80px;
}

.page-title {
  font-weight: 500;
  font-size: 30px;
  line-height: 1.333;
  color: #262626;
  text-align: center;
  margin: 0;
}

.nav-right {
  display: flex;
  align-items: center;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 24px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 32px;
  height: 58px;
  background: #FFFFFF;
  border: 2px solid #D9D9D9;
  border-radius: 100px;
}

.printer-icon,
.user-icon {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-text {
  font-weight: 500;
  font-size: 24px;
  line-height: 1.417;
  color: #262626;
}

.divider {
  width: 0;
  height: 22px;
  border-left: 2px solid rgba(38, 38, 38, 0.3);
}

/* 进度步骤 */
.progress-steps {
  position: absolute;
  top: 90px;
  left: 0;
  width: 1920px;
  height: 128px;
  background: #FFFFFF;
  border-bottom: 1px solid #D9D9D9;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.step-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 34px;
}

.step-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.step-number {
  width: 48px;
  height: 48px;
  border-radius: 90px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 600;
  font-size: 24px;
  line-height: 1.4;
  color: #FFFFFF;
}

.step-number.active {
  background: #04AA65;
}

.step-number.inactive {
  background: #BFBFBF;
}

.step-label {
  font-weight: 400;
  font-size: 30px;
  line-height: 1.333;
}

.step-label.active {
  color: #04AA65;
}

.step-label.inactive {
  color: #8C8C8C;
}

.step-connector {
  display: flex;
  align-items: center;
  gap: 1px;
}

.connector-line {
  width: 60px;
  height: 0;
  border-top: 2px dashed #8C8C8C;
}

.connector-arrow {
  width: 4px;
  height: 8px;
  border-top: 2px solid #8C8C8C;
  border-right: 2px solid #8C8C8C;
  transform: rotate(45deg);
}

/* 主要内容区域 */
.main-content {
  position: absolute;
  top: 218px;
  left: 64px;
  display: flex;
  gap: 32px;
}

/* 左侧面板 */
.left-panel {
  width: 912px;
  height: 702px;
}

.panel-background {
  width: 100%;
  height: 100%;
  background: #FFFFFF;
  border: 1px solid #D9D9D9;
  border-radius: 16px;
  position: relative;
}

/* 状态指示器 */
.status-indicators {
  position: absolute;
  top: 24px;
  left: 64px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 20px 0;
}

.status-bar {
  width: 6px;
  height: 32px;
  background: #04AA65;
}

.status-text {
  font-weight: 500;
  font-size: 32px;
  line-height: 1;
  color: #262626;
}

/* 预览图片 */
.preview-images {
  position: absolute;
  top: 106px;
  left: 64px;
  display: flex;
  gap: 23px;
}

.preview-item {
  width: 417px;
  height: 474px;
}

.preview-background {
  width: 100%;
  height: 100%;
  background: #F7F7F7;
  border: 1px solid #ECECEC;
  border-radius: 16px;
  position: relative;
}

.preview-background.dashed-border {
  border: 1px dashed #D9D9D9;
}

.preview-image {
  position: absolute;
  top: 16px;
  left: 16.5px;
  width: 385px;
  height: 442px;
  background: #F5F5F5;
  border-radius: 4px;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
  border-radius: 4px;
}

.image-placeholder.light {
  background: #F7F7F7;
}

.fullscreen-icon {
  position: absolute;
  bottom: 32px;
  right: 32px;
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.8;
}

/* 状态标签 */
.status-tags {
  position: absolute;
  bottom: 166px;
  left: 147.5px;
  display: flex;
  gap: 190px;
}

.status-tag {
  padding: 10px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 100px;
  width: 250px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.tag-text {
  font-weight: 500;
  font-size: 30px;
  line-height: 1;
  color: #FFFFFF;
  text-align: center;
}

/* 编辑按钮 */
.edit-button {
  position: absolute;
  bottom: 86px;
  left: 338px;
  width: 300px;
  height: 80px;
  background: #FFFFFF;
  border: 2px solid #D9D9D9;
  border-radius: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  padding: 12px 32px;
  cursor: pointer;
}

.edit-text {
  font-weight: 400;
  font-size: 32px;
  line-height: 1.125;
  color: #262626;
}

/* 右侧面板 */
.right-panel {
  width: 912px;
  height: 702px;
}

/* 表单字段 */
.form-fields {
  padding: 128px 32px 32px;
  display: flex;
  flex-direction: column;
  gap: 112px;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.field-label {
  display: flex;
  align-items: center;
  gap: 24px;
  height: 40px;
}

.required {
  font-weight: 400;
  font-size: 32px;
  line-height: 1;
  color: #F44444;
}

.label-text {
  font-weight: 400;
  font-size: 30px;
  line-height: 1.333;
  color: #262626;
}

.field-input {
  width: 663px;
}

.field-input-group {
  display: flex;
  gap: 24px;
}

.select-box {
  background: #FFFFFF;
  border: 2.5px solid #D9D9D9;
  border-radius: 16px;
  padding: 10px 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 60px;
}

.select-box.large {
  width: 662.5px;
}

.select-box.empty {
  width: 319px;
}

.select-text {
  font-weight: 400;
  font-size: 14px;
  line-height: 1.571;
  color: rgba(0, 0, 0, 0.85);
}

.select-arrow {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.selected-tags {
  display: flex;
  gap: 24px;
  margin-top: 13px;
}

.tag {
  background: #F5F5F5;
  border: 2.2px solid #D9D9D9;
  border-radius: 4.4px;
  padding: 4.4px 17.7px;
  display: flex;
  align-items: center;
  gap: 8.8px;
}

.tag .tag-text {
  font-weight: 400;
  font-size: 12px;
  line-height: 1.667;
  color: rgba(0, 0, 0, 0.85);
}

/* AI选项 */
.ai-options {
  display: flex;
  gap: 24px;
}

.ai-option {
  width: 319.28px;
  height: 96px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 32px;
  cursor: pointer;
}

.ai-option.selected {
  background: #DEF8E9;
  border: 2.5px solid #04AA65;
}

.ai-option:not(.selected) {
  background: #EFF3F2;
}

.option-icon {
  width: 58.66px;
  height: 58px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.option-text {
  font-weight: 500;
  font-size: 32px;
  line-height: 1;
  color: #04AA65;
}

.ai-option:not(.selected) .option-text {
  color: rgba(0, 0, 0, 0.85);
}

/* 确认按钮 */
.confirm-button {
  position: absolute;
  bottom: 64px;
  left: 775px;
  width: 370px;
  height: 96px;
  background: #04AA65;
  border-radius: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.button-text {
  font-weight: 500;
  font-size: 36px;
  line-height: 1;
  color: #FFFFFF;
}

/* 响应式和交互效果 */
.back-button:hover,
.action-button:hover,
.edit-button:hover,
.ai-option:hover,
.confirm-button:hover {
  opacity: 0.8;
  transition: opacity 0.2s;
}

.confirm-button:active {
  transform: scale(0.98);
  transition: transform 0.1s;
}
</style>
