<template>
  <div class="布置-完成">
    <div class="rectangle-346242312" />
    <div class="component-53">
      <span class="text-主要按钮">确认布置作业</span>
    </div>
    <div class="rectangle-346242592" />
    <div class="rectangle-346242593" />
    <div class="frame-1321317494">
      <span class="text-作业样卷-99张">作业样卷 99张</span>
    </div>
    <div class="frame-1321317495">
      <span class="text-答案卷-1张">答案卷 1张</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AssignmentComplete',
  
  data() {
    return {};
  },
  methods: {
    handleClick() {
      // 处理点击事件
    }
  }
}
</script>

<style scoped>
.布置-完成 {
  width: 1920px;
  height: 1080px;
  background-color: #EEEEEE;
}

.rectangle-346242312 {
  background-color: #FFFFFF;
  border-radius: 16px;
  border: 1px solid #D9D9D9;
}

.component-53 {
  background-color: #04AA65;
  border-radius: 100px;
}

.text-主要按钮 {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 36px;
  line-height: 1em;
  text-align: left;
  background-color: #FFFFFF;
}

.rectangle-346242592 {
  background-color: #F7F7F7;
  border-radius: 16px;
  border: 1px solid #ECECEC;
}

.rectangle-346242593 {
  background-color: #F7F7F7;
  border-radius: 16px;
  border: 1px solid #ECECEC;
}

.frame-1321317494 {
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 100px;
}

.text-作业样卷-99张 {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 30px;
  line-height: 1em;
  text-align: center;
  background-color: #FFFFFF;
}

.frame-1321317495 {
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 100px;
}

.text-答案卷-1张 {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 30px;
  line-height: 1em;
  text-align: center;
  background-color: #FFFFFF;
}


</style>