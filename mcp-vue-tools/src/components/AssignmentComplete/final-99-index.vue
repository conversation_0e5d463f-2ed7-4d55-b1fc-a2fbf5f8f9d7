<template>
  <div class="assignment-complete-final">
    <!-- 背景层 -->
    <div class="bg-layer"></div>
    
    <!-- 顶部导航栏 - 精确像素定位 -->
    <div class="nav-bar">
      <div class="nav-inner">
        <!-- 返回按钮区域 -->
        <div class="back-area">
          <div class="back-btn-wrapper">
            <div class="back-icon-svg">
              <svg width="14" height="25" viewBox="0 0 14 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12.5 2L2 12.5L12.5 23" stroke="#262626" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <span class="back-label">返回</span>
          </div>
        </div>
        
        <!-- 标题区域 -->
        <div class="title-area">
          <h1 class="main-title">2024数学作业第三次作业</h1>
        </div>
        
        <!-- 右侧操作区域 -->
        <div class="actions-area">
          <div class="action-btn">
            <div class="action-icon">
              <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="6" y="4" width="16" height="4" fill="#262626"/>
                <rect x="4" y="8" width="20" height="12" rx="2" fill="none" stroke="#262626" stroke-width="2"/>
                <rect x="6" y="16" width="16" height="8" fill="#262626"/>
              </svg>
            </div>
            <span class="action-label">78.3 MB/s</span>
          </div>
          <div class="action-divider"></div>
          <div class="action-btn">
            <div class="action-icon">
              <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="14" cy="8" r="4" fill="#262626"/>
                <path d="M6 24c0-4.4 3.6-8 8-8s8 3.6 8 8" fill="#262626"/>
              </svg>
            </div>
            <span class="action-label">王小明</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 进度步骤栏 -->
    <div class="progress-bar">
      <div class="progress-inner">
        <div class="step-group">
          <!-- 步骤1 - 激活状态 -->
          <div class="step-wrapper">
            <div class="step-circle active">1</div>
            <div class="step-text active">布置作业</div>
          </div>
          
          <!-- 连接线1 -->
          <div class="connector">
            <div class="dash-line"></div>
            <div class="arrow-tip"></div>
          </div>
          
          <!-- 步骤2 -->
          <div class="step-wrapper">
            <div class="step-circle">2</div>
            <div class="step-text">扫描作业</div>
          </div>
          
          <!-- 连接线2 -->
          <div class="connector">
            <div class="dash-line"></div>
            <div class="arrow-tip"></div>
          </div>
          
          <!-- 步骤3 -->
          <div class="step-wrapper">
            <div class="step-circle">3</div>
            <div class="step-text">AI 批改</div>
          </div>
          
          <!-- 连接线3 -->
          <div class="connector">
            <div class="dash-line"></div>
            <div class="arrow-tip"></div>
          </div>
          
          <!-- 步骤4 -->
          <div class="step-wrapper">
            <div class="step-circle">4</div>
            <div class="step-text">打印留痕</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-area">
      <!-- 左侧预览面板 -->
      <div class="left-section">
        <div class="left-bg">
          <!-- 状态指示条 -->
          <div class="status-indicators">
            <div class="indicator-row">
              <div class="status-bar green"></div>
              <span class="status-label">请编辑作业信息</span>
            </div>
            <div class="indicator-row">
              <div class="status-bar green"></div>
              <span class="status-label">已传作业样卷</span>
            </div>
          </div>
          
          <!-- 预览卡片区域 -->
          <div class="preview-section">
            <!-- 第一个预览卡片 -->
            <div class="preview-card">
              <div class="card-container">
                <div class="preview-content">
                  <div class="content-bg"></div>
                  <div class="fullscreen-overlay">
                    <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M8 3H5C3.9 3 3 3.9 3 5V8M21 8V5C21 3.9 20.1 3 19 3H16M16 21H19C20.1 21 21 20.1 21 19V16M3 16V19C3 20.1 3.9 21 5 21H8" stroke="white" stroke-width="2"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 第二个预览卡片 -->
            <div class="preview-card">
              <div class="card-container dashed-border">
                <div class="preview-content">
                  <div class="content-bg light"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 底部标签 -->
          <div class="bottom-tags">
            <div class="tag-item">
              <span class="tag-label">作业样卷 99张</span>
            </div>
            <div class="tag-item">
              <span class="tag-label">答案卷 1张</span>
            </div>
          </div>

          <!-- 编辑按钮 -->
          <div class="edit-action">
            <span class="edit-label">编辑作业样卷</span>
          </div>
        </div>
      </div>

      <!-- 右侧表单面板 -->
      <div class="right-section">
        <div class="right-bg">
          <div class="form-wrapper">
            <!-- 学科字段 -->
            <div class="field-group">
              <div class="field-header">
                <span class="asterisk">*</span>
                <span class="field-title">所属学科：</span>
              </div>
              <div class="field-body">
                <div class="select-wrapper">
                  <span class="select-value">语文</span>
                  <div class="select-arrow">
                    <svg width="25" height="17" viewBox="0 0 25 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M2 2L12.5 12.5L23 2" stroke="rgba(0,0,0,0.25)" stroke-width="2"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            <!-- 年级班级字段 -->
            <div class="field-group">
              <div class="field-header">
                <span class="asterisk">*</span>
                <span class="field-title">年级/班级：</span>
              </div>
              <div class="field-body">
                <div class="dual-select">
                  <div class="select-wrapper">
                    <span class="select-value">一年级</span>
                    <div class="select-arrow">
                      <svg width="25" height="17" viewBox="0 0 25 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M2 2L12.5 12.5L23 2" stroke="rgba(0,0,0,0.25)" stroke-width="2"/>
                      </svg>
                    </div>
                  </div>
                  <div class="select-wrapper empty">
                    <div class="select-arrow">
                      <svg width="25" height="17" viewBox="0 0 25 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M2 2L12.5 12.5L23 2" stroke="rgba(0,0,0,0.25)" stroke-width="2"/>
                      </svg>
                    </div>
                  </div>
                </div>
                <div class="selected-tags">
                  <div class="mini-tag">
                    <span class="mini-text">一年级01..</span>
                  </div>
                  <div class="mini-tag">
                    <span class="mini-text">+2</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 作业名称字段 -->
            <div class="field-group">
              <div class="field-header">
                <span class="asterisk">*</span>
                <span class="field-title">作业名称：</span>
              </div>
              <div class="field-body">
                <div class="select-wrapper full-width">
                  <span class="select-value">化学作业05月21日(周三)</span>
                </div>
              </div>
            </div>

            <!-- 智能批改字段 -->
            <div class="field-group">
              <div class="field-header">
                <span class="asterisk">*</span>
                <span class="field-title">智能批改：</span>
              </div>
              <div class="field-body">
                <div class="ai-options">
                  <div class="ai-choice selected">
                    <div class="choice-check">
                      <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="13" cy="13" r="13" fill="#04AA65"/>
                        <path d="M8 13L11 16L18 9" stroke="white" stroke-width="2"/>
                      </svg>
                    </div>
                    <span class="choice-text">全能王</span>
                  </div>
                  <div class="ai-choice">
                    <span class="choice-text">极速版</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 确认按钮 -->
    <div class="confirm-btn">
      <span class="confirm-text">确认布置作业</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AssignmentCompleteFinal99',
  emits: ['confirm', 'back'],
  data() {
    return {
      formData: {
        subject: '语文',
        grade: '一年级',
        assignmentName: '化学作业05月21日(周三)',
        aiMode: 'premium'
      }
    }
  },
  methods: {
    handleConfirm() {
      this.$emit('confirm', this.formData)
    },
    handleBack() {
      this.$emit('back')
    }
  }
}
</script>

<style scoped>
/* 基础容器 - 精确匹配Figma尺寸 */
.assignment-complete-final {
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 背景层 */
.bg-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 1920px;
  height: 1080px;
  background: #EEEEEE;
}

/* 顶部导航栏 - 精确像素定位 */
.nav-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 1920px;
  height: 90px;
  background: #FFFFFF;
  border-bottom: 1px solid #E8E8E8;
  z-index: 10;
}

.nav-inner {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 40px 0 16px;
  box-sizing: border-box;
}

.back-area {
  flex: 0 0 auto;
}

.back-btn-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 32px;
  height: 64px;
  cursor: pointer;
}

.back-icon-svg {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-label {
  font-size: 30px;
  font-weight: 500;
  color: #262626;
  line-height: 40px;
  text-align: center;
}

.title-area {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.main-title {
  font-size: 30px;
  font-weight: 500;
  color: #262626;
  line-height: 40px;
  text-align: center;
  margin: 0;
}

.actions-area {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  gap: 24px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 32px;
  height: 58px;
  background: #FFFFFF;
  border: 2px solid #D9D9D9;
  border-radius: 100px;
  box-sizing: border-box;
}

.action-icon {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-label {
  font-size: 24px;
  font-weight: 500;
  color: #262626;
  line-height: 34px;
}

.action-divider {
  width: 0;
  height: 22px;
  border-left: 2px solid rgba(38, 38, 38, 0.3);
}

/* 进度步骤栏 */
.progress-bar {
  position: absolute;
  top: 90px;
  left: 0;
  width: 1920px;
  height: 128px;
  background: #FFFFFF;
  border-bottom: 1px solid #D9D9D9;
  z-index: 9;
}

.progress-inner {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.step-group {
  display: flex;
  align-items: center;
  gap: 34px;
}

.step-wrapper {
  display: flex;
  align-items: center;
  gap: 16px;
}

.step-circle {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #BFBFBF;
  color: #FFFFFF;
  font-size: 24px;
  font-weight: 600;
  line-height: 33.6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.step-circle.active {
  background: #04AA65;
}

.step-text {
  font-size: 30px;
  font-weight: 400;
  color: #8C8C8C;
  line-height: 40px;
}

.step-text.active {
  color: #04AA65;
}

.connector {
  display: flex;
  align-items: center;
  gap: 1px;
}

.dash-line {
  width: 60px;
  height: 0;
  border-top: 2px dashed #8C8C8C;
}

.arrow-tip {
  width: 4px;
  height: 8px;
  border-top: 2px solid #8C8C8C;
  border-right: 2px solid #8C8C8C;
  transform: rotate(45deg);
}

/* 主内容区域 */
.main-area {
  position: absolute;
  top: 218px;
  left: 64px;
  display: flex;
  gap: 32px;
}

/* 左侧面板 */
.left-section {
  width: 912px;
  height: 702px;
}

.left-bg {
  width: 100%;
  height: 100%;
  background: #FFFFFF;
  border: 1px solid #D9D9D9;
  border-radius: 16px;
  position: relative;
}

/* 状态指示器 */
.status-indicators {
  position: absolute;
  top: 24px;
  left: 64px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.indicator-row {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 20px 0;
}

.status-bar {
  width: 6px;
  height: 32px;
}

.status-bar.green {
  background: #04AA65;
}

.status-label {
  font-size: 32px;
  font-weight: 500;
  color: #262626;
  line-height: 32px;
}

/* 预览区域 */
.preview-section {
  position: absolute;
  top: 106px;
  left: 64px;
  display: flex;
  gap: 23px;
}

.preview-card {
  width: 417px;
  height: 474px;
}

.card-container {
  width: 100%;
  height: 100%;
  background: #F7F7F7;
  border: 1px solid #ECECEC;
  border-radius: 16px;
  position: relative;
}

.card-container.dashed-border {
  border: 1px dashed #D9D9D9;
}

.preview-content {
  position: absolute;
  top: 16px;
  left: 16.5px;
  width: 385px;
  height: 442px;
  border-radius: 4px;
}

.content-bg {
  width: 100%;
  height: 100%;
  background: #F5F5F5;
  border-radius: 4px;
}

.content-bg.light {
  background: #F7F7F7;
}

.fullscreen-overlay {
  position: absolute;
  bottom: 32px;
  right: 32px;
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.8;
}

/* 底部标签 */
.bottom-tags {
  position: absolute;
  bottom: 166px;
  left: 147.5px;
  display: flex;
  gap: 190px;
}

.tag-item {
  padding: 10px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 100px;
  width: 250px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.tag-label {
  font-size: 30px;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 30px;
  text-align: center;
}

/* 编辑按钮 */
.edit-action {
  position: absolute;
  bottom: 86px;
  left: 338px;
  width: 300px;
  height: 80px;
  background: #FFFFFF;
  border: 2px solid #D9D9D9;
  border-radius: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.edit-label {
  font-size: 32px;
  font-weight: 400;
  color: #262626;
  line-height: 36px;
}

/* 右侧面板 */
.right-section {
  width: 912px;
  height: 702px;
}

.right-bg {
  width: 100%;
  height: 100%;
  background: #FFFFFF;
  border: 1px solid #D9D9D9;
  border-radius: 16px;
}

/* 表单内容 */
.form-wrapper {
  padding: 128px 32px 32px;
  display: flex;
  flex-direction: column;
  gap: 112px;
}

.field-group {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.field-header {
  display: flex;
  align-items: center;
  gap: 24px;
  height: 40px;
}

.asterisk {
  font-size: 32px;
  font-weight: 400;
  color: #F44444;
  line-height: 32px;
}

.field-title {
  font-size: 30px;
  font-weight: 400;
  color: #262626;
  line-height: 40px;
}

.field-body {
  width: 663px;
}

.select-wrapper {
  background: #FFFFFF;
  border: 2.5px solid #D9D9D9;
  border-radius: 16px;
  padding: 10px 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 60px;
  box-sizing: border-box;
}

.select-wrapper.full-width {
  width: 662.5px;
}

.select-wrapper.empty {
  width: 319px;
}

.select-value {
  font-size: 14px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.85);
  line-height: 22px;
}

.select-arrow {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dual-select {
  display: flex;
  gap: 24px;
}

.selected-tags {
  display: flex;
  gap: 24px;
  margin-top: 13px;
}

.mini-tag {
  background: #F5F5F5;
  border: 2.2px solid #D9D9D9;
  border-radius: 4.4px;
  padding: 4.4px 17.7px;
  display: flex;
  align-items: center;
}

.mini-text {
  font-size: 12px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.85);
  line-height: 20px;
}

/* AI选项 */
.ai-options {
  display: flex;
  gap: 24px;
}

.ai-choice {
  width: 319.28px;
  height: 96px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 32px;
  cursor: pointer;
  box-sizing: border-box;
}

.ai-choice.selected {
  background: #DEF8E9;
  border: 2.5px solid #04AA65;
}

.ai-choice:not(.selected) {
  background: #EFF3F2;
  border: 2.5px solid transparent;
}

.choice-check {
  width: 58.66px;
  height: 58px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.choice-text {
  font-size: 32px;
  font-weight: 500;
  line-height: 32px;
}

.ai-choice.selected .choice-text {
  color: #04AA65;
}

.ai-choice:not(.selected) .choice-text {
  color: rgba(0, 0, 0, 0.85);
}

/* 确认按钮 */
.confirm-btn {
  position: absolute;
  bottom: 64px;
  left: 775px;
  width: 370px;
  height: 96px;
  background: #04AA65;
  border-radius: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.confirm-text {
  font-size: 36px;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 36px;
}

/* 交互效果 */
.back-btn-wrapper:hover,
.action-btn:hover,
.edit-action:hover,
.ai-choice:hover,
.confirm-btn:hover {
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.confirm-btn:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

/* 字体渲染优化 */
* {
  text-rendering: optimizeLegibility;
  -webkit-font-feature-settings: "kern" 1;
  font-feature-settings: "kern" 1;
}
</style>
