<template>
  <div class="assignment-complete-page">
    <!-- Top Navigation -->
    <div class="top-navigation">
      <div class="nav-left">
        <div class="back-button">
          <div class="back-icon"></div>
          <span class="back-text">返回</span>
        </div>
      </div>
      <div class="nav-center">
        <h1 class="page-title">2024数学作业第三次作业</h1>
      </div>
      <div class="nav-right">
        <div class="nav-actions">
          <div class="action-button">
            <div class="printer-icon"></div>
            <span class="action-text">78.3 MB/s</span>
          </div>
          <div class="divider"></div>
          <div class="action-button">
            <div class="user-icon"></div>
            <span class="action-text">王小明</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Progress Steps -->
    <div class="progress-steps">
      <div class="step-container">
        <!-- Step 1 - Active -->
        <div class="step-item active">
          <div class="step-number active">1</div>
          <div class="step-label active">布置作业</div>
        </div>
        
        <div class="step-connector">
          <div class="connector-line dashed"></div>
          <div class="connector-arrow"></div>
        </div>

        <!-- Step 2 -->
        <div class="step-item">
          <div class="step-number">2</div>
          <div class="step-label">扫描作业</div>
        </div>

        <div class="step-connector">
          <div class="connector-line dashed"></div>
          <div class="connector-arrow"></div>
        </div>

        <!-- Step 3 -->
        <div class="step-item">
          <div class="step-number">3</div>
          <div class="step-label">AI 批改</div>
        </div>

        <div class="step-connector">
          <div class="connector-line dashed"></div>
          <div class="connector-arrow"></div>
        </div>

        <!-- Step 4 -->
        <div class="step-item">
          <div class="step-number">4</div>
          <div class="step-label">打印留痕</div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
      <!-- Left Panel -->
      <div class="left-panel">
        <!-- Status Indicators -->
        <div class="status-indicator">
          <div class="status-bar"></div>
          <span class="status-text">请编辑作业信息</span>
        </div>
        <div class="status-indicator">
          <div class="status-bar"></div>
          <span class="status-text">已传作业样卷</span>
        </div>

        <!-- Sample Images -->
        <div class="sample-images">
          <div class="sample-card">
            <div class="sample-image">
              <div class="sample-bg"></div>
              <div class="sample-content"></div>
              <div class="fullscreen-icon">
                <div class="icon-bg"></div>
                <div class="icon-symbol"></div>
              </div>
            </div>
          </div>

          <div class="sample-card dashed">
            <div class="sample-image dashed">
              <div class="sample-bg"></div>
              <div class="sample-content"></div>
            </div>
          </div>
        </div>

        <!-- Edit Button -->
        <div class="edit-button">
          <span>编辑作业样卷</span>
        </div>

        <!-- Sample Count Tags -->
        <div class="sample-tags">
          <div class="sample-tag">作业样卷 99张</div>
          <div class="sample-tag">答案卷 1张</div>
        </div>

        <!-- Fullscreen Icon -->
        <div class="fullscreen-icon-bottom">
          <div class="icon-bg"></div>
          <div class="icon-symbol"></div>
        </div>
      </div>

      <!-- Right Panel -->
      <div class="right-panel">
        <!-- Form Fields -->
        <div class="form-section">
          <!-- Subject Field -->
          <div class="form-group">
            <div class="field-label">
              <span class="required">*</span>
              <span class="label-text">所属学科：</span>
            </div>
            <div class="select-field">
              <span class="select-text">语文</span>
              <div class="select-arrow"></div>
            </div>
          </div>

          <!-- Grade/Class Field -->
          <div class="form-group">
            <div class="field-label">
              <span class="required">*</span>
              <span class="label-text">年级/班级：</span>
            </div>
            <div class="form-row">
              <div class="select-field">
                <span class="select-text">一年级</span>
                <div class="select-arrow"></div>
              </div>
              <div class="select-field empty">
                <div class="select-arrow"></div>
              </div>
            </div>
            <div class="selected-tags">
              <div class="tag">一年级01..</div>
              <div class="tag">+2</div>
            </div>
          </div>

          <!-- Assignment Name Field -->
          <div class="form-group">
            <div class="field-label">
              <span class="required">*</span>
              <span class="label-text">作业名称：</span>
            </div>
            <div class="select-field large">
              <span class="select-text">化学作业05月21日(周三)</span>
            </div>
          </div>

          <!-- AI Grading Field -->
          <div class="form-group">
            <div class="field-label">
              <span class="required">*</span>
              <span class="label-text">智能批改：</span>
            </div>
            <div class="ai-options">
              <div class="ai-option selected">
                <div class="option-icon">
                  <div class="icon-bg"></div>
                  <div class="success-icon"></div>
                </div>
                <span class="option-text">全能王</span>
              </div>
              <div class="ai-option">
                <span class="option-text">极速版</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Confirm Button -->
        <div class="confirm-button">
          <span>确认布置作业</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AssignmentComplete',
  data() {
    return {
      selectedSubject: '语文',
      selectedGrade: '一年级',
      assignmentName: '化学作业05月21日(周三)',
      selectedAI: 'premium'
    }
  },
  methods: {
    goBack() {
      this.$emit('back')
    },
    confirmAssignment() {
      this.$emit('confirm', {
        subject: this.selectedSubject,
        grade: this.selectedGrade,
        name: this.assignmentName,
        aiMode: this.selectedAI
      })
    }
  },
  emits: ['back', 'confirm']
}
</script>

<style scoped>
.assignment-complete-page {
  width: 1920px;
  height: 1080px;
  background: #EEEEEE;
  position: relative;
}

/* Top Navigation */
.top-navigation {
  width: 100%;
  height: 90px;
  background: #FFFFFF;
  border-bottom: 1px solid #E8E8E8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 40px 5px 16px;
  gap: 1240px;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 50px;
}

.back-button {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  padding: 16px 32px;
  height: 64px;
  border-radius: 8px;
  cursor: pointer;
}

.back-icon {
  width: 36px;
  height: 36px;
  background: #262626;
}

.back-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 30px;
  line-height: 1.33em;
  color: #262626;
}

.nav-center {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  height: 80px;
}

.page-title {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 30px;
  line-height: 1.33em;
  color: #262626;
  margin: 0;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 40px;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 24px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 32px;
  height: 58px;
  background: #FFFFFF;
  border: 2px solid #D9D9D9;
  border-radius: 100px;
  cursor: pointer;
}

.printer-icon,
.user-icon {
  width: 28px;
  height: 28px;
  background: rgba(0, 0, 0, 0.85);
}

.action-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 24px;
  line-height: 1.42em;
  color: #262626;
}

.divider {
  width: 0;
  height: 22px;
  border-left: 2px solid rgba(38, 38, 38, 0.3);
}

/* Progress Steps */
.progress-steps {
  width: 100%;
  background: #FFFFFF;
  border-bottom: 1px solid #D9D9D9;
  padding: 24px 10px;
  display: flex;
  justify-content: center;
}

.step-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 34px;
}

.step-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.step-number {
  width: 48px;
  height: 48px;
  background: #BFBFBF;
  border-radius: 90px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  font-size: 24px;
  line-height: 1.4em;
  color: #FFFFFF;
}

.step-number.active {
  background: #04AA65;
}

.step-label {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 30px;
  line-height: 1.33em;
  color: #8C8C8C;
}

.step-label.active {
  color: #04AA65;
}

.step-connector {
  display: flex;
  align-items: center;
  gap: 1px;
}

.connector-line {
  width: 60px;
  height: 0;
  border-top: 2px solid #8C8C8C;
}

.connector-line.dashed {
  border-top: 2px dashed #8C8C8C;
}

.connector-arrow {
  width: 4px;
  height: 8px;
  background: #8C8C8C;
}

/* Main Content */
.main-content {
  display: flex;
  gap: 32px;
  padding: 0 32px;
  margin-top: 0;
}

/* Left Panel */
.left-panel {
  width: 912px;
  height: 702px;
  background: #FFFFFF;
  border: 1px solid #D9D9D9;
  border-radius: 16px;
  padding: 32px;
  position: relative;
}

.status-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 20px 0;
  margin-bottom: 24px;
}

.status-bar {
  width: 6px;
  height: 32px;
  background: #04AA65;
}

.status-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 32px;
  line-height: 1em;
  color: #262626;
}

.sample-images {
  display: flex;
  gap: 23px;
  margin-bottom: 32px;
}

.sample-card {
  width: 417px;
  height: 474px;
  background: #F7F7F7;
  border: 1px solid #ECECEC;
  border-radius: 16px;
  position: relative;
}

.sample-card.dashed {
  border: 1px dashed #D9D9D9;
}

.sample-image {
  width: 385px;
  height: 442px;
  margin: 16.5px auto;
  background: #F5F5F5;
  position: relative;
}

.sample-image.dashed {
  background: #F7F7F7;
  border: 1px dashed #D9D9D9;
  border-radius: 4px;
}

.sample-bg {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
}

.sample-content {
  position: absolute;
  top: -19.75px;
  left: -18.12px;
  width: 437.09px;
  height: 481.51px;
  background-size: fit;
  background-position: center;
}

.fullscreen-icon {
  position: absolute;
  bottom: 32px;
  right: 32px;
  width: 32px;
  height: 32px;
}

.icon-bg {
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  opacity: 0.8;
}

.icon-symbol {
  position: absolute;
  top: 3.2px;
  left: 3.2px;
  width: 25.6px;
  height: 25.6px;
  background: #FFFFFF;
}

.edit-button {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  padding: 12px 32px;
  width: 300px;
  height: 80px;
  background: #FFFFFF;
  border: 2px solid #D9D9D9;
  border-radius: 100px;
  margin: 0 auto 32px;
  cursor: pointer;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 32px;
  line-height: 1.125em;
  color: #262626;
}

.sample-tags {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-bottom: 32px;
}

.sample-tag {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 10px;
  width: 250px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 100px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 30px;
  line-height: 1em;
  color: #FFFFFF;
  text-align: center;
}

.fullscreen-icon-bottom {
  position: absolute;
  bottom: 32px;
  right: 32px;
  width: 32px;
  height: 32px;
}

/* Right Panel */
.right-panel {
  width: 912px;
  height: 702px;
  background: #FFFFFF;
  border: 1px solid #D9D9D9;
  border-radius: 16px;
  padding: 32px;
  position: relative;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 32px;
  margin-bottom: 40px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.field-label {
  display: flex;
  align-items: center;
  gap: 8px;
  height: 40px;
}

.required {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 32px;
  line-height: 1em;
  color: #F44444;
}

.label-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 30px;
  line-height: 1.33em;
  color: #262626;
}

.select-field {
  display: flex;
  align-items: center;
  padding: 10px 30px;
  height: 60px;
  background: #FFFFFF;
  border: 2.5px solid #D9D9D9;
  border-radius: 16px;
  cursor: pointer;
}

.select-field.large {
  width: 662.5px;
  height: 80px;
}

.select-field.empty {
  width: 319px;
}

.form-row {
  display: flex;
  gap: 24px;
}

.select-text {
  flex: 1;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.57em;
  color: rgba(0, 0, 0, 0.85);
}

.select-arrow {
  width: 25.45px;
  height: 17.14px;
  background: rgba(0, 0, 0, 0.25);
}

.selected-tags {
  display: flex;
  gap: 16px;
  margin-top: 8px;
}

.tag {
  display: flex;
  align-items: center;
  gap: 8.83px;
  padding: 4.42px 17.67px;
  background: #F5F5F5;
  border: 2.21px solid #D9D9D9;
  border-radius: 4.42px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.67em;
  color: rgba(0, 0, 0, 0.85);
}

.ai-options {
  display: flex;
  gap: 24px;
}

.ai-option {
  width: 319.28px;
  height: 96px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 0 32px;
  cursor: pointer;
}

.ai-option.selected {
  background: #DEF8E9;
  border: 2.5px solid #04AA65;
}

.ai-option:not(.selected) {
  background: #EFF3F2;
}

.option-icon {
  width: 58.66px;
  height: 58px;
  background: #04AA65;
  position: relative;
}

.success-icon {
  position: absolute;
  top: 4px;
  left: 4.05px;
  width: 26.3px;
  height: 26px;
  background: #FFFFFF;
}

.option-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 32px;
  line-height: 1em;
  color: #04AA65;
}

.confirm-button {
  position: absolute;
  bottom: 24px;
  right: 32px;
  width: 370px;
  height: 96px;
  background: #04AA65;
  border-radius: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 16px 32px;
  cursor: pointer;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 36px;
  line-height: 1em;
  color: #FFFFFF;
}

.confirm-button:hover {
  background: #038a54;
}
</style>