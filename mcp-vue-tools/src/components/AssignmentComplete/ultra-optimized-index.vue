<template>
  <div class="assignment-complete-page">
    <!-- 精确复制Figma设计的布局 -->
    
    <!-- 顶部导航栏 -->
    <div class="top-nav">
      <div class="nav-content">
        <div class="back-section">
          <div class="back-btn">
            <svg width="14" height="25" viewBox="0 0 14 25" fill="none">
              <path d="M12.5 2L2 12.5L12.5 23" stroke="#262626" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <span>返回</span>
          </div>
        </div>
        <div class="title-section">
          <h1>2024数学作业第三次作业</h1>
        </div>
        <div class="actions-section">
          <div class="action-item">
            <div class="icon-wrapper">
              <svg width="28" height="28" viewBox="0 0 28 28" fill="none">
                <rect x="6" y="4" width="16" height="4" fill="#262626"/>
                <rect x="4" y="8" width="20" height="12" rx="2" fill="none" stroke="#262626" stroke-width="2"/>
                <rect x="6" y="16" width="16" height="8" fill="#262626"/>
              </svg>
            </div>
            <span>78.3 MB/s</span>
          </div>
          <div class="divider-line"></div>
          <div class="action-item">
            <div class="icon-wrapper">
              <svg width="28" height="28" viewBox="0 0 28 28" fill="none">
                <circle cx="14" cy="8" r="4" fill="#262626"/>
                <path d="M6 24c0-4.4 3.6-8 8-8s8 3.6 8 8" fill="#262626"/>
              </svg>
            </div>
            <span>王小明</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 进度步骤 -->
    <div class="progress-section">
      <div class="steps-wrapper">
        <div class="step active">
          <div class="step-num">1</div>
          <div class="step-text">布置作业</div>
        </div>
        <div class="step-line">
          <div class="line-dash"></div>
          <div class="line-arrow"></div>
        </div>
        <div class="step">
          <div class="step-num inactive">2</div>
          <div class="step-text inactive">扫描作业</div>
        </div>
        <div class="step-line">
          <div class="line-dash"></div>
          <div class="line-arrow"></div>
        </div>
        <div class="step">
          <div class="step-num inactive">3</div>
          <div class="step-text inactive">AI 批改</div>
        </div>
        <div class="step-line">
          <div class="line-dash"></div>
          <div class="line-arrow"></div>
        </div>
        <div class="step">
          <div class="step-num inactive">4</div>
          <div class="step-text inactive">打印留痕</div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-area">
      <!-- 左侧预览面板 -->
      <div class="left-panel">
        <div class="panel-bg">
          <!-- 状态指示器 -->
          <div class="status-bars">
            <div class="status-item">
              <div class="bar active"></div>
              <span class="status-label">请编辑作业信息</span>
            </div>
            <div class="status-item">
              <div class="bar completed"></div>
              <span class="status-label">已传作业样卷</span>
            </div>
          </div>
          
          <!-- 预览图片区域 -->
          <div class="preview-area">
            <div class="preview-card">
              <div class="card-bg">
                <div class="preview-img">
                  <div class="img-content"></div>
                  <div class="fullscreen-btn">
                    <svg width="26" height="26" viewBox="0 0 26 26" fill="none">
                      <path d="M8 3H5C3.9 3 3 3.9 3 5V8M21 8V5C21 3.9 20.1 3 19 3H16M16 21H19C20.1 21 21 20.1 21 19V16M3 16V19C3 20.1 3.9 21 5 21H8" stroke="white" stroke-width="2"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
            <div class="preview-card">
              <div class="card-bg dashed">
                <div class="preview-img">
                  <div class="img-content empty"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 状态标签 -->
          <div class="status-badges">
            <div class="badge">
              <span>作业样卷 99张</span>
            </div>
            <div class="badge">
              <span>答案卷 1张</span>
            </div>
          </div>

          <!-- 编辑按钮 -->
          <div class="edit-btn">
            <span>编辑作业样卷</span>
          </div>
        </div>
      </div>

      <!-- 右侧表单面板 -->
      <div class="right-panel">
        <div class="panel-bg">
          <div class="form-content">
            <!-- 学科选择 -->
            <div class="form-row">
              <div class="field-label">
                <span class="required">*</span>
                <span>所属学科：</span>
              </div>
              <div class="field-control">
                <div class="select-input">
                  <span>语文</span>
                  <svg width="25" height="17" viewBox="0 0 25 17" fill="none">
                    <path d="M2 2L12.5 12.5L23 2" stroke="rgba(0,0,0,0.25)" stroke-width="2"/>
                  </svg>
                </div>
              </div>
            </div>

            <!-- 年级班级选择 -->
            <div class="form-row">
              <div class="field-label">
                <span class="required">*</span>
                <span>年级/班级：</span>
              </div>
              <div class="field-control">
                <div class="select-group">
                  <div class="select-input">
                    <span>一年级</span>
                    <svg width="25" height="17" viewBox="0 0 25 17" fill="none">
                      <path d="M2 2L12.5 12.5L23 2" stroke="rgba(0,0,0,0.25)" stroke-width="2"/>
                    </svg>
                  </div>
                  <div class="select-input empty">
                    <svg width="25" height="17" viewBox="0 0 25 17" fill="none">
                      <path d="M2 2L12.5 12.5L23 2" stroke="rgba(0,0,0,0.25)" stroke-width="2"/>
                    </svg>
                  </div>
                </div>
                <div class="selected-items">
                  <div class="item-tag">
                    <span>一年级01..</span>
                  </div>
                  <div class="item-tag">
                    <span>+2</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 作业名称 -->
            <div class="form-row">
              <div class="field-label">
                <span class="required">*</span>
                <span>作业名称：</span>
              </div>
              <div class="field-control">
                <div class="select-input wide">
                  <span>化学作业05月21日(周三)</span>
                </div>
              </div>
            </div>

            <!-- 智能批改 -->
            <div class="form-row">
              <div class="field-label">
                <span class="required">*</span>
                <span>智能批改：</span>
              </div>
              <div class="field-control">
                <div class="ai-selection">
                  <div class="ai-option selected">
                    <div class="option-check">
                      <svg width="26" height="26" viewBox="0 0 26 26" fill="none">
                        <circle cx="13" cy="13" r="13" fill="#04AA65"/>
                        <path d="M8 13L11 16L18 9" stroke="white" stroke-width="2"/>
                      </svg>
                    </div>
                    <span>全能王</span>
                  </div>
                  <div class="ai-option">
                    <span>极速版</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 确认按钮 -->
    <div class="confirm-action">
      <span>确认布置作业</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AssignmentCompleteUltraOptimized',
  emits: ['confirm', 'back'],
  data() {
    return {
      formData: {
        subject: '语文',
        grade: '一年级',
        assignmentName: '化学作业05月21日(周三)',
        aiMode: 'premium'
      }
    }
  },
  methods: {
    handleConfirm() {
      this.$emit('confirm', this.formData)
    },
    handleBack() {
      this.$emit('back')
    }
  }
}
</script>

<style scoped>
/* 基础容器 - 精确匹配Figma尺寸 */
.assignment-complete-page {
  width: 1920px;
  height: 1080px;
  position: relative;
  background: #EEEEEE;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  overflow: hidden;
}

/* 顶部导航 - 精确定位 */
.top-nav {
  position: absolute;
  top: 0;
  left: 0;
  width: 1920px;
  height: 90px;
  background: #FFFFFF;
  border-bottom: 1px solid #E8E8E8;
}

.nav-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 40px 0 16px;
  box-sizing: border-box;
}

.back-section {
  flex: 0 0 auto;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 32px;
  height: 64px;
  cursor: pointer;
}

.back-btn span {
  font-size: 30px;
  font-weight: 500;
  color: #262626;
  line-height: 1.333;
}

.title-section {
  flex: 1;
  display: flex;
  justify-content: center;
}

.title-section h1 {
  font-size: 30px;
  font-weight: 500;
  color: #262626;
  line-height: 1.333;
  margin: 0;
}

.actions-section {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  gap: 24px;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 32px;
  height: 58px;
  background: #FFFFFF;
  border: 2px solid #D9D9D9;
  border-radius: 100px;
}

.icon-wrapper {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-item span {
  font-size: 24px;
  font-weight: 500;
  color: #262626;
  line-height: 1.417;
}

.divider-line {
  width: 0;
  height: 22px;
  border-left: 2px solid rgba(38, 38, 38, 0.3);
}

/* 进度步骤 - 精确定位 */
.progress-section {
  position: absolute;
  top: 90px;
  left: 0;
  width: 1920px;
  height: 128px;
  background: #FFFFFF;
  border-bottom: 1px solid #D9D9D9;
  display: flex;
  align-items: center;
  justify-content: center;
}

.steps-wrapper {
  display: flex;
  align-items: center;
  gap: 34px;
}

.step {
  display: flex;
  align-items: center;
  gap: 16px;
}

.step-num {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #04AA65;
  color: #FFFFFF;
  font-size: 24px;
  font-weight: 600;
  line-height: 1.4;
  display: flex;
  align-items: center;
  justify-content: center;
}

.step-num.inactive {
  background: #BFBFBF;
}

.step-text {
  font-size: 30px;
  font-weight: 400;
  color: #04AA65;
  line-height: 1.333;
}

.step-text.inactive {
  color: #8C8C8C;
}

.step-line {
  display: flex;
  align-items: center;
  gap: 1px;
}

.line-dash {
  width: 60px;
  height: 0;
  border-top: 2px dashed #8C8C8C;
}

.line-arrow {
  width: 4px;
  height: 8px;
  border-top: 2px solid #8C8C8C;
  border-right: 2px solid #8C8C8C;
  transform: rotate(45deg);
}

/* 主要内容区域 - 精确定位 */
.content-area {
  position: absolute;
  top: 218px;
  left: 64px;
  display: flex;
  gap: 32px;
}

/* 左侧面板 */
.left-panel {
  width: 912px;
  height: 702px;
}

.panel-bg {
  width: 100%;
  height: 100%;
  background: #FFFFFF;
  border: 1px solid #D9D9D9;
  border-radius: 16px;
  position: relative;
}

/* 状态指示器 */
.status-bars {
  position: absolute;
  top: 24px;
  left: 64px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 20px 0;
}

.bar {
  width: 6px;
  height: 32px;
  background: #04AA65;
}

.status-label {
  font-size: 32px;
  font-weight: 500;
  color: #262626;
  line-height: 1;
}

/* 预览区域 */
.preview-area {
  position: absolute;
  top: 106px;
  left: 64px;
  display: flex;
  gap: 23px;
}

.preview-card {
  width: 417px;
  height: 474px;
}

.card-bg {
  width: 100%;
  height: 100%;
  background: #F7F7F7;
  border: 1px solid #ECECEC;
  border-radius: 16px;
  position: relative;
}

.card-bg.dashed {
  border: 1px dashed #D9D9D9;
}

.preview-img {
  position: absolute;
  top: 16px;
  left: 16.5px;
  width: 385px;
  height: 442px;
  background: #F5F5F5;
  border-radius: 4px;
}

.img-content {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
  border-radius: 4px;
}

.img-content.empty {
  background: #F7F7F7;
}

.fullscreen-btn {
  position: absolute;
  bottom: 32px;
  right: 32px;
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.8;
}

/* 状态标签 */
.status-badges {
  position: absolute;
  bottom: 166px;
  left: 147.5px;
  display: flex;
  gap: 190px;
}

.badge {
  padding: 10px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 100px;
  width: 250px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.badge span {
  font-size: 30px;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 1;
  text-align: center;
}

/* 编辑按钮 */
.edit-btn {
  position: absolute;
  bottom: 86px;
  left: 338px;
  width: 300px;
  height: 80px;
  background: #FFFFFF;
  border: 2px solid #D9D9D9;
  border-radius: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.edit-btn span {
  font-size: 32px;
  font-weight: 400;
  color: #262626;
  line-height: 1.125;
}

/* 右侧面板 */
.right-panel {
  width: 912px;
  height: 702px;
}

/* 表单内容 */
.form-content {
  padding: 128px 32px 32px;
  display: flex;
  flex-direction: column;
  gap: 112px;
}

.form-row {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.field-label {
  display: flex;
  align-items: center;
  gap: 24px;
  height: 40px;
}

.required {
  font-size: 32px;
  font-weight: 400;
  color: #F44444;
  line-height: 1;
}

.field-label span:not(.required) {
  font-size: 30px;
  font-weight: 400;
  color: #262626;
  line-height: 1.333;
}

.field-control {
  width: 663px;
}

.select-input {
  background: #FFFFFF;
  border: 2.5px solid #D9D9D9;
  border-radius: 16px;
  padding: 10px 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 60px;
  box-sizing: border-box;
}

.select-input.wide {
  width: 662.5px;
}

.select-input.empty {
  width: 319px;
}

.select-input span {
  font-size: 14px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.85);
  line-height: 1.571;
}

.select-group {
  display: flex;
  gap: 24px;
}

.selected-items {
  display: flex;
  gap: 24px;
  margin-top: 13px;
}

.item-tag {
  background: #F5F5F5;
  border: 2.2px solid #D9D9D9;
  border-radius: 4.4px;
  padding: 4.4px 17.7px;
  display: flex;
  align-items: center;
}

.item-tag span {
  font-size: 12px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.85);
  line-height: 1.667;
}

/* AI选择 */
.ai-selection {
  display: flex;
  gap: 24px;
}

.ai-option {
  width: 319.28px;
  height: 96px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 32px;
  cursor: pointer;
  box-sizing: border-box;
}

.ai-option.selected {
  background: #DEF8E9;
  border: 2.5px solid #04AA65;
}

.ai-option:not(.selected) {
  background: #EFF3F2;
  border: 2.5px solid transparent;
}

.option-check {
  width: 58.66px;
  height: 58px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ai-option span {
  font-size: 32px;
  font-weight: 500;
  line-height: 1;
}

.ai-option.selected span {
  color: #04AA65;
}

.ai-option:not(.selected) span {
  color: rgba(0, 0, 0, 0.85);
}

/* 确认按钮 */
.confirm-action {
  position: absolute;
  bottom: 64px;
  left: 775px;
  width: 370px;
  height: 96px;
  background: #04AA65;
  border-radius: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.confirm-action span {
  font-size: 36px;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 1;
}

/* 交互效果 */
.back-btn:hover,
.action-item:hover,
.edit-btn:hover,
.ai-option:hover,
.confirm-action:hover {
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.confirm-action:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}
</style>
