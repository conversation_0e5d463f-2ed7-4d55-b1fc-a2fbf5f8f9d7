import puppeteer from 'puppeteer';
import fs from 'fs/promises';
import path from 'path';
import chalk from 'chalk';
import {
  getResultsPath,
  getVueServerUrl,
  ensureDirectory,
  getDefaultConfig
} from '../utils/path-config.js';

export class TakeScreenshotTool {
  constructor() {
    this.description = 'Take screenshot of Vue component using Puppeteer';
    this.inputSchema = {
      type: 'object',
      properties: {
        componentName: {
          type: 'string',
          description: 'Name of the component to screenshot'
        },
        url: {
          type: 'string',
          description: 'Custom URL to screenshot (optional, will use component URL if not provided)'
        },
        selector: {
          type: 'string',
          description: 'CSS selector for the element to screenshot (optional, will use component selector if not provided)'
        },
        outputPath: {
          type: 'string',
          description: 'Custom output path for the screenshot (optional)'
        },
        viewport: {
          type: 'object',
          properties: {
            width: { type: 'number', default: 1152 },
            height: { type: 'number', default: 772 }
          },
          description: 'Viewport size for the screenshot'
        },
        screenshotOptions: {
          type: 'object',
          properties: {
            omitBackground: { type: 'boolean', default: true },
            deviceScaleFactor: { type: 'number', default: 1 },
            fullPage: { type: 'boolean', default: false }
          },
          description: 'Screenshot options'
        },
        waitOptions: {
          type: 'object',
          properties: {
            waitUntil: { type: 'string', default: 'networkidle2' },
            timeout: { type: 'number', default: 10000 },
            additionalWait: { type: 'number', default: 100 }
          },
          description: 'Wait options for page loading'
        },
        projectPath: {
          type: 'string',
          description: 'Path to the MCP Vue tools project (optional, auto-detected)'
        }
      },
      required: ['componentName']
    };
  }

  async execute(args) {
    const config = getDefaultConfig();
    const {
      componentName,
      url,
      selector,
      outputPath,
      viewport = config.defaultViewport,
      screenshotOptions = config.defaultScreenshotOptions,
      waitOptions = config.defaultWaitOptions,
      projectPath
    } = args;

    let browser;
    try {
      // Determine URL and selector
      const targetUrl = url || getVueServerUrl(83, componentName);
      const targetSelector = selector || `#benchmark-container-for-screenshot`;

      // Determine output path
      const resultsDir = getResultsPath(componentName, projectPath);
      await ensureDirectory(resultsDir);
      const screenshotPath = outputPath || path.join(resultsDir, 'actual.png');

      // Launch browser with improved Chrome detection
      const chromePaths = [
        process.env.PUPPETEER_EXECUTABLE_PATH,
        '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
        '/usr/bin/google-chrome',
        '/usr/bin/chromium-browser'
      ].filter(Boolean);

      let executablePath = null;
      for (const chromePath of chromePaths) {
        try {
          await fs.access(chromePath);
          executablePath = chromePath;
          break;
        } catch {
          continue;
        }
      }

      const launchOptions = {
        headless: 'new',
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      };

      if (executablePath) {
        launchOptions.executablePath = executablePath;
      }

      browser = await puppeteer.launch(launchOptions);

      const page = await browser.newPage();

      // Set viewport
      await page.setViewport({
        width: viewport.width,
        height: viewport.height,
        deviceScaleFactor: screenshotOptions.deviceScaleFactor || 1
      });

      // Navigate to page
      await page.goto(targetUrl, { 
        waitUntil: waitOptions.waitUntil,
        timeout: waitOptions.timeout 
      });

      // Wait for Vue app to load
      await page.waitForFunction(() => {
        return document.querySelector('#app') && document.querySelector('#app').innerHTML !== '';
      }, { timeout: 3000 });

      // Wait for target element
      await page.waitForSelector(targetSelector, { timeout: waitOptions.timeout });

      // Additional wait for animations/rendering
      if (waitOptions.additionalWait > 0) {
        await page.waitForTimeout(waitOptions.additionalWait);
      }

      // Find the element
      const element = await page.$(targetSelector);
      if (!element) {
        throw new Error(`Element not found: ${targetSelector}`);
      }

      // Get DOM tree for debugging
      const domTree = await page.evaluate(el => {
        function serializeNode(node) {
          if (node.nodeType === Node.TEXT_NODE) {
            const text = node.textContent.trim();
            return text ? { type: 'text', content: text } : null;
          }
          if (node.nodeType !== Node.ELEMENT_NODE) {
            return null;
          }
          const obj = {
            tag: node.tagName.toLowerCase(),
            attributes: {},
            children: []
          };
          for (const attr of node.attributes) {
            obj.attributes[attr.name] = attr.value;
          }
          for (const child of node.childNodes) {
            const serializedChild = serializeNode(child);
            if (serializedChild) {
              obj.children.push(serializedChild);
            }
          }
          return obj;
        }
        return serializeNode(el);
      }, element);

      // Take screenshot
      if (screenshotOptions.fullPage) {
        await page.screenshot({
          path: screenshotPath,
          omitBackground: screenshotOptions.omitBackground,
          deviceScaleFactor: screenshotOptions.deviceScaleFactor
        });
      } else {
        // Get element bounding box for clipped screenshot
        const boundingBox = await element.boundingBox();
        if (!boundingBox) {
          throw new Error('Unable to get element bounding box');
        }

        await page.screenshot({
          path: screenshotPath,
          omitBackground: screenshotOptions.omitBackground,
          deviceScaleFactor: screenshotOptions.deviceScaleFactor,
          clip: {
            x: boundingBox.x,
            y: boundingBox.y,
            width: boundingBox.width,
            height: boundingBox.height
          }
        });
      }

      return {
        success: true,
        componentName,
        screenshotPath,
        url: targetUrl,
        selector: targetSelector,
        viewport,
        domTree,
        message: `Screenshot saved to: ${screenshotPath}`
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        componentName,
        url: url || `http://localhost:83/component/${componentName}`
      };
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }
}
