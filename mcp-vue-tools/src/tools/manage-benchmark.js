import fs from 'fs/promises';
import path from 'path';
import chalk from 'chalk';

export class ManageBenchmarkTool {
  constructor() {
    this.description = 'Manage benchmark results, reports, and component testing workflow';
    this.inputSchema = {
      type: 'object',
      properties: {
        action: {
          type: 'string',
          enum: ['list', 'clean', 'report', 'status', 'full_test', 'setup_component'],
          description: 'Action to perform'
        },
        componentName: {
          type: 'string',
          description: 'Specific component name (for component-specific actions)'
        },
        projectPath: {
          type: 'string',
          default: '/Users/<USER>/Documents/work/camscanner-cloud-vue3',
          description: 'Path to the Vue project'
        },
        includeDetails: {
          type: 'boolean',
          default: false,
          description: 'Include detailed information in reports'
        }
      },
      required: ['action']
    };
  }

  async execute(args) {
    // Ensure args is defined and apply defaults
    const safeArgs = args || {};
    const {
      action,
      componentName,
      includeDetails = false
    } = safeArgs;

    // Explicitly handle projectPath with fallback
    const projectPath = safeArgs.projectPath || '/Users/<USER>/Documents/work/camscanner-cloud-vue3';

    // Debug logging
    console.log('ManageBenchmark execute:', { action, componentName, projectPath });

    try {
      switch (action) {
        case 'list':
          return await this.listComponents(projectPath, includeDetails);

        case 'clean':
          return await this.cleanResults(projectPath, componentName);

        case 'report':
          return await this.generateReport(projectPath, componentName, includeDetails);

        case 'status':
          return await this.getStatusSafe(projectPath, componentName);
        
        case 'full_test':
          return await this.runFullTest(projectPath, componentName);
        
        case 'setup_component':
          return await this.setupComponent(projectPath, componentName);
        
        default:
          throw new Error(`Unknown action: ${action}`);
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        action
      };
    }
  }

  async listComponents(projectPath, includeDetails) {
    const componentsDir = path.join(projectPath, 'mcp-vue-tools/src/components');
    const resultsDir = path.join(projectPath, 'mcp-vue-tools/results');

    try {
      const componentDirs = await fs.readdir(componentsDir, { withFileTypes: true });
      const components = [];

      for (const dir of componentDirs) {
        if (dir.isDirectory() && dir.name !== 'index.ts') {
          const componentInfo = await this.getComponentInfo(
            path.join(componentsDir, dir.name),
            path.join(resultsDir, dir.name),
            includeDetails
          );
          components.push({
            name: dir.name,
            ...componentInfo
          });
        }
      }

      return {
        success: true,
        action: 'list',
        totalComponents: components.length,
        components,
        componentsDir,
        resultsDir
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        action: 'list'
      };
    }
  }

  async getComponentInfo(componentDir, resultsDir, includeDetails) {
    const info = {
      hasComponent: false,
      hasExpected: false,
      hasActual: false,
      hasDiff: false,
      hasFigmaData: false
    };

    try {
      // Check component files
      const componentFile = path.join(componentDir, 'index.vue');
      const expectedFile = path.join(componentDir, 'expected.png');
      const figmaDataFile = path.join(componentDir, 'figma-data.json');

      info.hasComponent = await this.fileExists(componentFile);
      info.hasExpected = await this.fileExists(expectedFile);
      info.hasFigmaData = await this.fileExists(figmaDataFile);

      // Check result files
      const actualFile = path.join(resultsDir, 'actual.png');
      const diffFile = path.join(resultsDir, 'diff.png');

      info.hasActual = await this.fileExists(actualFile);
      info.hasDiff = await this.fileExists(diffFile);

      if (includeDetails) {
        info.files = {
          component: info.hasComponent ? componentFile : null,
          expected: info.hasExpected ? expectedFile : null,
          figmaData: info.hasFigmaData ? figmaDataFile : null,
          actual: info.hasActual ? actualFile : null,
          diff: info.hasDiff ? diffFile : null
        };

        // Get file sizes
        if (info.hasExpected) {
          const expectedStats = await fs.stat(expectedFile);
          info.expectedSize = expectedStats.size;
        }
        if (info.hasActual) {
          const actualStats = await fs.stat(actualFile);
          info.actualSize = actualStats.size;
        }
      }

      // Determine status
      if (info.hasComponent && info.hasExpected && info.hasActual) {
        info.status = 'ready_for_comparison';
      } else if (info.hasComponent && info.hasExpected) {
        info.status = 'ready_for_screenshot';
      } else if (info.hasComponent) {
        info.status = 'needs_expected_image';
      } else {
        info.status = 'needs_component';
      }

    } catch (error) {
      info.error = error.message;
      info.status = 'error';
    }

    return info;
  }

  async cleanResults(projectPath, componentName) {
    const resultsDir = path.join(projectPath, 'mcp-vue-tools/results');

    try {
      if (componentName) {
        // Clean specific component
        const componentResultsDir = path.join(resultsDir, componentName);
        await this.removeDirectory(componentResultsDir);
        return {
          success: true,
          action: 'clean',
          cleaned: [componentName],
          message: `Cleaned results for ${componentName}`
        };
      } else {
        // Clean all results
        const dirs = await fs.readdir(resultsDir, { withFileTypes: true });
        const cleaned = [];

        for (const dir of dirs) {
          if (dir.isDirectory()) {
            await this.removeDirectory(path.join(resultsDir, dir.name));
            cleaned.push(dir.name);
          }
        }

        return {
          success: true,
          action: 'clean',
          cleaned,
          message: `Cleaned results for ${cleaned.length} components`
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        action: 'clean'
      };
    }
  }

  async generateReport(projectPath, componentName, includeDetails) {
    const components = await this.listComponents(projectPath, true);
    
    if (!components.success) {
      return components;
    }

    const report = {
      success: true,
      action: 'report',
      timestamp: new Date().toISOString(),
      summary: {
        total: components.totalComponents,
        readyForComparison: 0,
        readyForScreenshot: 0,
        needsExpectedImage: 0,
        needsComponent: 0,
        errors: 0
      },
      components: componentName 
        ? components.components.filter(c => c.name === componentName)
        : components.components
    };

    // Calculate summary
    report.components.forEach(component => {
      switch (component.status) {
        case 'ready_for_comparison':
          report.summary.readyForComparison++;
          break;
        case 'ready_for_screenshot':
          report.summary.readyForScreenshot++;
          break;
        case 'needs_expected_image':
          report.summary.needsExpectedImage++;
          break;
        case 'needs_component':
          report.summary.needsComponent++;
          break;
        case 'error':
          report.summary.errors++;
          break;
      }
    });

    return report;
  }

  async getStatus(projectPath, componentName) {
    if (componentName) {
      // Ensure projectPath is valid
      const safePath = projectPath || '/Users/<USER>/Documents/work/camscanner-cloud-vue3';

      const componentDir = path.join(safePath, 'mcp-vue-tools/src/components', componentName);
      const resultsDir = path.join(safePath, 'mcp-vue-tools/results', componentName);
      
      const info = await this.getComponentInfo(componentDir, resultsDir, true);
      
      return {
        success: true,
        action: 'status',
        componentName,
        ...info
      };
    } else {
      return await this.generateReport(projectPath, null, false);
    }
  }

  async setupComponent(projectPath, componentName) {
    if (!componentName) {
      return {
        success: false,
        error: 'Component name is required for setup',
        action: 'setup_component'
      };
    }

    const componentDir = path.join(projectPath, 'mcp-vue-tools/src/components', componentName);
    const resultsDir = path.join(projectPath, 'mcp-vue-tools/results', componentName);

    try {
      // Create directories
      await fs.mkdir(componentDir, { recursive: true });
      await fs.mkdir(resultsDir, { recursive: true });

      return {
        success: true,
        action: 'setup_component',
        componentName,
        componentDir,
        resultsDir,
        message: `Setup complete for ${componentName}`,
        nextSteps: [
          'Create component file: index.vue',
          'Add expected image: expected.png',
          'Optionally add figma-data.json'
        ]
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        action: 'setup_component',
        componentName
      };
    }
  }

  async runFullTest(projectPath, componentName) {
    // This would orchestrate a full test workflow
    // For now, just return the status and next steps
    const status = await this.getStatus(projectPath, componentName);
    
    return {
      success: true,
      action: 'full_test',
      componentName,
      currentStatus: status,
      message: 'Use other tools to complete the full test workflow',
      workflow: [
        '1. Use vue_dev_server to start server',
        '2. Use render_component to verify component loads',
        '3. Use take_screenshot to capture actual image',
        '4. Use compare_images to compare with expected'
      ]
    };
  }

  async fileExists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  async removeDirectory(dirPath) {
    try {
      await fs.rm(dirPath, { recursive: true, force: true });
    } catch (error) {
      // Ignore errors if directory doesn't exist
      if (error.code !== 'ENOENT') {
        throw error;
      }
    }
  }
}
