import { spawn, exec } from 'child_process';
import { promisify } from 'util';
import chalk from 'chalk';
import {
  getMCPToolsPath,
  getVueServerUrl,
  getDefaultConfig
} from '../utils/path-config.js';

const execAsync = promisify(exec);

export class VueDevServerTool {
  constructor() {
    this.description = 'Manage Vue development server (start, stop, check status)';
    this.inputSchema = {
      type: 'object',
      properties: {
        action: {
          type: 'string',
          enum: ['start', 'stop', 'status', 'restart'],
          description: 'Action to perform on the dev server'
        },
        port: {
          type: 'number',
          default: 83,
          description: 'Port number for the dev server'
        },
        projectPath: {
          type: 'string',
          default: process.cwd(),
          description: 'Path to the MCP Vue tools project'
        }
      },
      required: ['action']
    };
  }

  async execute(args) {
    const { action, port = 83, projectPath } = args;

    try {
      switch (action) {
        case 'status':
          return await this.checkServerStatus(port);
        
        case 'start':
          return await this.startServer(projectPath, port);
        
        case 'stop':
          return await this.stopServer(port);
        
        case 'restart':
          await this.stopServer(port);
          await new Promise(resolve => setTimeout(resolve, 2000));
          return await this.startServer(projectPath, port);
        
        default:
          throw new Error(`Unknown action: ${action}`);
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        action
      };
    }
  }

  async checkServerStatus(port) {
    try {
      const response = await fetch(`http://localhost:${port}`);
      return {
        success: true,
        status: 'running',
        port,
        url: `http://localhost:${port}`,
        message: 'Vue dev server is running'
      };
    } catch (error) {
      return {
        success: false,
        status: 'stopped',
        port,
        message: 'Vue dev server is not running'
      };
    }
  }

  async startServer(projectPath, port) {
    try {
      // Check if server is already running
      const status = await this.checkServerStatus(port);
      if (status.success) {
        return {
          success: true,
          status: 'already_running',
          port,
          url: `http://localhost:${port}`,
          message: 'Vue dev server is already running'
        };
      }

      // Start the server
      const child = spawn('yarn', ['dev'], {
        cwd: projectPath,
        detached: true,
        stdio: 'ignore'
      });

      child.unref();

      // Wait for server to start
      let attempts = 0;
      const maxAttempts = 30;
      
      while (attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        const serverStatus = await this.checkServerStatus(port);
        if (serverStatus.success) {
          return {
            success: true,
            status: 'started',
            port,
            url: `http://localhost:${port}`,
            pid: child.pid,
            message: 'Vue dev server started successfully'
          };
        }
        attempts++;
      }

      throw new Error('Server failed to start within timeout period');

    } catch (error) {
      return {
        success: false,
        error: error.message,
        action: 'start'
      };
    }
  }

  async stopServer(port) {
    try {
      // Find and kill processes using the port
      const { stdout } = await execAsync(`lsof -ti:${port}`);
      const pids = stdout.trim().split('\n').filter(pid => pid);

      if (pids.length === 0) {
        return {
          success: true,
          status: 'already_stopped',
          port,
          message: 'No process found on the specified port'
        };
      }

      for (const pid of pids) {
        await execAsync(`kill -9 ${pid}`);
      }

      return {
        success: true,
        status: 'stopped',
        port,
        killedPids: pids,
        message: `Stopped ${pids.length} process(es) on port ${port}`
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        action: 'stop'
      };
    }
  }
}
