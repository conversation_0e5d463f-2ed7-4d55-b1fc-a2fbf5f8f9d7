import puppeteer from 'puppeteer';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { PNG } from 'pngjs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * 使用benchmark的方法重新截图，确保尺寸匹配
 */
async function takeFixedScreenshot(componentName, options = {}) {
  console.log(`🔧 修复 ${componentName} 的截图尺寸...`);
  
  let browser;
  try {
    // 使用与benchmark相同的浏览器配置
    const executablePath = process.env.PUPPETEER_EXECUTABLE_PATH || '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome';
    
    browser = await puppeteer.launch({
      executablePath,
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // 使用与benchmark完全相同的viewport设置
    await page.setViewport({ 
      width: 1152, 
      height: 772, 
      deviceScaleFactor: 3  // 3倍图
    });
    
    // 访问专用的截图页面
    const url = `http://localhost:83/screenshot/${componentName}`;
    console.log(`📡 访问: ${url}`);

    await page.goto(url, { waitUntil: 'networkidle2' });

    // 等待组件加载
    await page.waitForSelector('#component-screenshot-container', { timeout: 10000 });
    
    // 等待额外时间确保组件完全渲染
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 获取组件容器元素
    const containerElement = await page.$('#component-screenshot-container');
    if (!containerElement) {
      throw new Error('找不到组件容器元素');
    }

    // 检查容器内是否有组件
    const hasComponent = await page.evaluate(() => {
      const container = document.querySelector('#component-screenshot-container');
      return container && container.children.length > 0;
    });
    
    if (!hasComponent) {
      throw new Error('组件容器为空，组件可能没有正确加载');
    }
    
    // 获取容器的实际尺寸
    const containerBox = await containerElement.boundingBox();
    console.log(`📏 容器尺寸: ${containerBox.width}x${containerBox.height}`);
    
    // 截图保存路径
    const resultsDir = path.join(__dirname, '../results', componentName);
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }
    
    const outputPath = path.join(resultsDir, 'actual.png');
    
    // 使用element.screenshot确保只截取组件部分
    await containerElement.screenshot({
      path: outputPath,
      omitBackground: true
    });
    
    // 同时复制到public目录
    const publicPath = path.join(__dirname, '../public/results', componentName, 'actual.png');
    const publicDir = path.dirname(publicPath);
    if (!fs.existsSync(publicDir)) {
      fs.mkdirSync(publicDir, { recursive: true });
    }
    fs.copyFileSync(outputPath, publicPath);
    
    console.log(`✅ ${componentName} 截图已保存: ${outputPath}`);
    
    // 获取新的图片尺寸
    const buffer = fs.readFileSync(outputPath);
    const png = PNG.sync.read(buffer);
    
    console.log(`📐 新截图尺寸: ${png.width}x${png.height}`);
    
    return {
      success: true,
      dimensions: { width: png.width, height: png.height },
      path: outputPath
    };
    
  } catch (error) {
    console.error(`❌ ${componentName} 截图失败:`, error.message);
    return {
      success: false,
      error: error.message
    };
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

/**
 * 修复所有组件的截图尺寸
 */
async function fixAllComponentScreenshots() {
  const components = ['ModalRemoveMember', 'ExchangeSuccess', 'AssignmentComplete', 'ScanComplete'];
  const results = {};
  
  console.log('🚀 开始修复所有组件的截图尺寸...\n');
  
  for (const component of components) {
    const result = await takeFixedScreenshot(component);
    results[component] = result;
    
    // 添加延迟避免浏览器资源冲突
    await new Promise(resolve => setTimeout(resolve, 2000));
    console.log(''); // 空行分隔
  }
  
  // 输出总结
  console.log('📊 修复结果总结:');
  for (const [component, result] of Object.entries(results)) {
    if (result.success) {
      console.log(`✅ ${component}: ${result.dimensions.width}x${result.dimensions.height}`);
    } else {
      console.log(`❌ ${component}: ${result.error}`);
    }
  }
  
  return results;
}

/**
 * 检查Figma期望图片的尺寸
 */
function checkExpectedDimensions() {
  const components = ['ModalRemoveMember', 'ExchangeSuccess', 'AssignmentComplete', 'ScanComplete'];
  
  console.log('📋 Figma期望图片尺寸:');
  
  for (const component of components) {
    const expectedPath = path.join(__dirname, '../results', component, `${component}_expected.png`);
    
    if (fs.existsSync(expectedPath)) {
      try {
        const buffer = fs.readFileSync(expectedPath);
        const png = PNG.sync.read(buffer);
        console.log(`📐 ${component}: ${png.width}x${png.height}`);
      } catch (error) {
        console.log(`❌ ${component}: 无法读取图片 - ${error.message}`);
      }
    } else {
      console.log(`⚠️  ${component}: 期望图片不存在`);
    }
  }
}

// 命令行工具
if (import.meta.url === `file://${process.argv[1]}`) {
  const command = process.argv[2];
  
  switch (command) {
    case 'check':
      checkExpectedDimensions();
      break;
    case 'fix':
      const componentName = process.argv[3];
      if (componentName) {
        takeFixedScreenshot(componentName);
      } else {
        fixAllComponentScreenshots();
      }
      break;
    default:
      console.log(`
🔧 截图尺寸修复工具

用法:
  node fix-screenshot-dimensions.js check           - 检查期望图片尺寸
  node fix-screenshot-dimensions.js fix             - 修复所有组件截图
  node fix-screenshot-dimensions.js fix <组件名>     - 修复指定组件截图

示例:
  node fix-screenshot-dimensions.js check
  node fix-screenshot-dimensions.js fix ModalRemoveMember
  node fix-screenshot-dimensions.js fix
      `);
  }
}

export { takeFixedScreenshot, fixAllComponentScreenshots, checkExpectedDimensions };
