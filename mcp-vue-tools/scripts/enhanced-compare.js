import fs from 'fs';
import path from 'path';
import { PNG } from 'pngjs';
import pixelmatch from 'pixelmatch';
import sharp from 'sharp';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * 检测差异区域的坐标
 */
function detectDiffRegions(diffData, width, height, threshold = 5) {
  const regions = [];
  const visited = new Set();
  
  // 扫描所有像素，寻找差异区域
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const idx = (width * y + x) << 2;
      const key = `${x},${y}`;
      
      // 如果是差异像素且未访问过
      if (!visited.has(key) && isDiffPixel(diffData, idx)) {
        const region = floodFill(diffData, width, height, x, y, visited);
        
        // 只保留足够大的区域
        if (region.pixels.length >= threshold) {
          const bounds = calculateBounds(region.pixels);
          regions.push({
            ...bounds,
            pixelCount: region.pixels.length,
            center: {
              x: Math.round((bounds.left + bounds.right) / 2),
              y: Math.round((bounds.top + bounds.bottom) / 2)
            }
          });
        }
      }
    }
  }
  
  // 按区域大小排序
  return regions.sort((a, b) => b.pixelCount - a.pixelCount);
}

/**
 * 判断是否为差异像素
 */
function isDiffPixel(data, idx) {
  // 检查红色通道，差异像素通常是红色
  return data[idx] > 200 && data[idx + 1] < 100 && data[idx + 2] < 100;
}

/**
 * 洪水填充算法找连通区域
 */
function floodFill(data, width, height, startX, startY, visited) {
  const pixels = [];
  const stack = [{x: startX, y: startY}];
  
  while (stack.length > 0) {
    const {x, y} = stack.pop();
    const key = `${x},${y}`;
    
    if (visited.has(key) || x < 0 || x >= width || y < 0 || y >= height) {
      continue;
    }
    
    const idx = (width * y + x) << 2;
    if (!isDiffPixel(data, idx)) {
      continue;
    }
    
    visited.add(key);
    pixels.push({x, y});
    
    // 添加相邻像素
    stack.push({x: x + 1, y});
    stack.push({x: x - 1, y});
    stack.push({x, y: y + 1});
    stack.push({x, y: y - 1});
  }
  
  return {pixels};
}

/**
 * 计算区域边界
 */
function calculateBounds(pixels) {
  let left = Infinity, right = -Infinity;
  let top = Infinity, bottom = -Infinity;
  
  for (const {x, y} of pixels) {
    left = Math.min(left, x);
    right = Math.max(right, x);
    top = Math.min(top, y);
    bottom = Math.max(bottom, y);
  }
  
  return {
    left,
    right,
    top,
    bottom,
    width: right - left + 1,
    height: bottom - top + 1
  };
}

/**
 * 将3倍图坐标转换为1倍图坐标
 */
function convertTo1xCoordinates(regions, scale = 3) {
  return regions.map(region => ({
    ...region,
    left: Math.round(region.left / scale),
    right: Math.round(region.right / scale),
    top: Math.round(region.top / scale),
    bottom: Math.round(region.bottom / scale),
    width: Math.round(region.width / scale),
    height: Math.round(region.height / scale),
    center: {
      x: Math.round(region.center.x / scale),
      y: Math.round(region.center.y / scale)
    }
  }));
}

/**
 * 根据坐标匹配Figma元素
 */
function matchFigmaElements(diffRegions, figmaData) {
  const matches = [];
  
  if (!figmaData || !figmaData.nodes) {
    console.warn('⚠️  Figma数据不完整，无法进行元素匹配');
    return matches;
  }
  
  for (const region of diffRegions) {
    const matchedElements = [];
    
    // 递归搜索所有节点
    function searchNodes(nodes) {
      for (const node of nodes) {
        if (node.boundingBox) {
          const bbox = node.boundingBox;
          
          // 检查是否有重叠
          if (isOverlapping(region, bbox)) {
            const overlap = calculateOverlap(region, bbox);
            matchedElements.push({
              id: node.id,
              name: node.name,
              type: node.type,
              boundingBox: bbox,
              overlapPercentage: overlap
            });
          }
        }
        
        // 递归搜索子节点
        if (node.children) {
          searchNodes(node.children);
        }
      }
    }
    
    searchNodes(figmaData.nodes);
    
    // 按重叠度排序
    matchedElements.sort((a, b) => b.overlapPercentage - a.overlapPercentage);
    
    matches.push({
      region,
      elements: matchedElements.slice(0, 3) // 只保留前3个最匹配的元素
    });
  }
  
  return matches;
}

/**
 * 检查两个矩形是否重叠
 */
function isOverlapping(region, bbox) {
  return !(region.right < bbox.x || 
           region.left > bbox.x + bbox.width ||
           region.bottom < bbox.y || 
           region.top > bbox.y + bbox.height);
}

/**
 * 计算重叠百分比
 */
function calculateOverlap(region, bbox) {
  const overlapLeft = Math.max(region.left, bbox.x);
  const overlapRight = Math.min(region.right, bbox.x + bbox.width);
  const overlapTop = Math.max(region.top, bbox.y);
  const overlapBottom = Math.min(region.bottom, bbox.y + bbox.height);
  
  if (overlapLeft >= overlapRight || overlapTop >= overlapBottom) {
    return 0;
  }
  
  const overlapArea = (overlapRight - overlapLeft) * (overlapBottom - overlapTop);
  const regionArea = region.width * region.height;
  
  return (overlapArea / regionArea * 100);
}

/**
 * 增强版图片对比 - 包含差异区域检测
 */
async function enhancedCompareImages(expectedPath, actualPath, diffOutputPath, componentName) {
  console.log('🔍 开始增强版图片对比...');
  
  try {
    // 读取图片
    let expectedBuffer = fs.readFileSync(expectedPath);
    let actualBuffer = fs.readFileSync(actualPath);

    const expectedSharp = sharp(expectedBuffer);
    const actualSharp = sharp(actualBuffer);

    const expectedMeta = await expectedSharp.metadata();
    const actualMeta = await actualSharp.metadata();

    console.log(`📐 期望图片尺寸: ${expectedMeta.width} × ${expectedMeta.height}`);
    console.log(`📐 实际图片尺寸: ${actualMeta.width} × ${actualMeta.height}`);
    
    // 如果尺寸不匹配，调整期望图片尺寸
    if (expectedMeta.width !== actualMeta.width || expectedMeta.height !== actualMeta.height) {
      console.warn('⚠️  图片尺寸不匹配，正在调整期望图片尺寸...');
      
      expectedBuffer = await expectedSharp
        .resize(actualMeta.width, actualMeta.height, { 
          fit: 'fill',
          kernel: sharp.kernel.nearest 
        })
        .png()
        .toBuffer();
      console.log('✅ 期望图片已调整为实际图片尺寸');
    } else {
      console.log('✅ 图片尺寸完全匹配，无需调整');
    }

    // 使用 PNG.js 解析图片
    const expectedPng = PNG.sync.read(expectedBuffer);
    const actualPng = PNG.sync.read(actualBuffer);
    const { width, height } = actualPng;

    // 创建差异图
    const diff = new PNG({ width, height });

    // 进行像素匹配
    const diffPixelCount = pixelmatch(expectedPng.data, actualPng.data, diff.data, width, height, {
      threshold: 0.1,
      includeAA: false,
      alpha: 0.1,
      diffColor: [255, 0, 0], // 红色
      aaColor: [255, 255, 0]  // 黄色
    });

    // 保存差异图
    fs.writeFileSync(diffOutputPath, PNG.sync.write(diff));

    const totalPixels = width * height;
    const matchPercentage = ((totalPixels - diffPixelCount) / totalPixels * 100).toFixed(2);

    console.log(`\n📊 对比结果:`);
    console.log(`   匹配度: ${matchPercentage}%`);
    console.log(`   差异像素: ${diffPixelCount}/${totalPixels}`);
    console.log(`   差异图已保存: ${diffOutputPath}`);

    // 检测差异区域
    console.log('\n🔍 检测差异区域...');
    const diffRegions3x = detectDiffRegions(diff.data, width, height, 25);
    const diffRegions1x = convertTo1xCoordinates(diffRegions3x, 3);
    
    console.log(`📍 发现 ${diffRegions1x.length} 个主要差异区域:`);
    diffRegions1x.forEach((region, index) => {
      console.log(`   区域 ${index + 1}: (${region.left},${region.top}) → (${region.right},${region.bottom})`);
      console.log(`            尺寸: ${region.width}×${region.height}, 中心: (${region.center.x},${region.center.y})`);
      console.log(`            像素数: ${region.pixelCount}`);
    });

    // 尝试加载Figma数据进行元素匹配
    const figmaDataPath = path.join(path.dirname(expectedPath), 'complete-figma-data.json');
    let figmaMatches = [];
    
    if (fs.existsSync(figmaDataPath)) {
      try {
        const figmaData = JSON.parse(fs.readFileSync(figmaDataPath, 'utf8'));
        figmaMatches = matchFigmaElements(diffRegions1x, figmaData);
        
        console.log('\n🎯 Figma元素匹配分析:');
        figmaMatches.forEach((match, index) => {
          console.log(`\n   差异区域 ${index + 1}:`);
          if (match.elements.length > 0) {
            match.elements.forEach((element, i) => {
              console.log(`     ${i + 1}. ${element.name} (${element.type})`);
              console.log(`        位置: (${element.boundingBox.x},${element.boundingBox.y}) ${element.boundingBox.width}×${element.boundingBox.height}`);
              console.log(`        重叠度: ${element.overlapPercentage.toFixed(1)}%`);
            });
          } else {
            console.log(`     ❌ 未找到匹配的Figma元素`);
          }
        });
      } catch (error) {
        console.warn('⚠️  无法解析Figma数据:', error.message);
      }
    } else {
      console.warn('⚠️  未找到Figma数据文件，跳过元素匹配');
    }

    // 保存详细分析结果
    const analysisPath = path.join(path.dirname(diffOutputPath), 'diff-analysis.json');
    const analysisData = {
      timestamp: new Date().toISOString(),
      componentName,
      matchPercentage: parseFloat(matchPercentage),
      diffPixels: diffPixelCount,
      totalPixels,
      dimensions: { width, height },
      diffRegions: diffRegions1x,
      figmaMatches,
      scale: 3
    };
    
    fs.writeFileSync(analysisPath, JSON.stringify(analysisData, null, 2));
    console.log(`\n💾 详细分析已保存: ${analysisPath}`);

    return {
      success: true,
      matchPercentage: parseFloat(matchPercentage),
      diffPixels: diffPixelCount,
      totalPixels,
      dimensions: { width, height },
      diffRegions: diffRegions1x,
      figmaMatches,
      analysisPath
    };

  } catch (error) {
    console.error('❌ 对比失败:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

// 命令行工具
if (import.meta.url === `file://${process.argv[1]}`) {
  const componentName = process.argv[2];
  if (!componentName) {
    console.error('用法: node enhanced-compare.js <组件名>');
    process.exit(1);
  }
  
  const resultsDir = path.join(__dirname, '../results', componentName);
  const expectedPath = path.join(resultsDir, `${componentName}_expected.png`);
  const actualPath = path.join(resultsDir, 'actual.png');
  const diffPath = path.join(resultsDir, 'diff.png');
  
  enhancedCompareImages(expectedPath, actualPath, diffPath, componentName);
}

export { enhancedCompareImages, detectDiffRegions, matchFigmaElements };
