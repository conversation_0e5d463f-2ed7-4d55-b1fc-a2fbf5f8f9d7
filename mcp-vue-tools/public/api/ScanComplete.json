{"componentName": "ScanComplete", "timestamp": "2025-07-13T14:19:14.632Z", "summary": {"matchPercentage": 91.82, "pixelMatch": 367277, "componentCreated": true, "componentRendered": true, "screenshotTaken": true, "comparisonAvailable": true, "files": {"component": "/mcp-vue-tools/src/components/ScanComplete/index.vue", "screenshot": "/results/ScanComplete/actual.png", "expected": "/results/ScanComplete/expected.png", "diff": "/results/ScanComplete/diff.png"}, "urls": {"test": "http://localhost:83/component/ScanComplete", "figma": "https://www.figma.com/design/WLaxbF2J6FAm6eBmXmG4tx/v20.14.0-v20.5.0%E8%9C%9C%E8%9C%82%E5%AE%B6%E6%A0%A1%E6%A0%A1%E5%9B%AD%E7%89%88--%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1%E7%89%88%E6%9C%AC?node-id=12739-182336&m=dev"}}, "comparison": {"expectedDimensions": {"width": 2640, "height": 1701}, "actualDimensions": {"width": 2640, "height": 1701}, "diffDimensions": {"width": 2640, "height": 1701}, "dimensionMismatch": false}, "steps": {"create": {"success": true, "message": "Component created successfully"}, "render": {"success": true, "message": "Component rendered successfully"}, "screenshot": {"success": true, "message": "Screenshot captured successfully"}, "comparison": {"success": true, "message": "Image comparison completed with 91.82% match"}}}