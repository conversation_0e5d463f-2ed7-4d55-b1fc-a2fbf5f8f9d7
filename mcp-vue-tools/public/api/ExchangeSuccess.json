{"componentName": "ExchangeSuccess", "timestamp": "2025-07-13T14:19:11.569Z", "summary": {"matchPercentage": 87.66, "pixelMatch": 338274, "componentCreated": true, "componentRendered": true, "screenshotTaken": true, "comparisonAvailable": true, "files": {"component": "/mcp-vue-tools/src/components/ExchangeSuccess/index.vue", "screenshot": "/results/ExchangeSuccess/actual.png", "expected": "/results/ExchangeSuccess/expected.png", "diff": "/results/ExchangeSuccess/diff.png"}, "urls": {"test": "http://localhost:83/component/ExchangeSuccess", "figma": "https://www.figma.com/design/D59IG2u1r4gfGQ56qdIlyB/6.92.0?node-id=5276-10661&m=dev"}}, "comparison": {"expectedDimensions": {"width": 1125, "height": 2436}, "actualDimensions": {"width": 1125, "height": 2436}, "diffDimensions": {"width": 1125, "height": 2436}, "dimensionMismatch": false}, "steps": {"create": {"success": true, "message": "Component created successfully"}, "render": {"success": true, "message": "Component rendered successfully"}, "screenshot": {"success": true, "message": "Screenshot captured successfully"}, "comparison": {"success": true, "message": "Image comparison completed with 87.66% match"}}}