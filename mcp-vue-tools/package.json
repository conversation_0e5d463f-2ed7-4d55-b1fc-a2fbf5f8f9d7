{"name": "figma-restoration-kit", "version": "2.0.0", "description": "Standalone Figma Restoration Kit - Complete MCP tools for Vue component rendering and Figma design restoration", "type": "module", "main": "src/server.js", "scripts": {"start": "node src/server.js", "mcp": "node --watch src/server.js", "dev": "vite", "build": "vite build", "preview": "vite preview", "test": "node test-installation.js", "clean": "node test-clean-tools.mjs", "install-kit": "chmod +x scripts/install.sh && ./scripts/install.sh", "setup": "chmod +x scripts/setup.sh && ./scripts/setup.sh"}, "dependencies": {"@modelcontextprotocol/sdk": "^0.4.0", "@vitejs/plugin-vue": "^5.0.0", "@vue/compiler-sfc": "^3.4.0", "chalk": "^5.3.0", "element-plus": "^2.0.0", "pixelmatch": "^5.3.0", "pngjs": "^7.0.0", "puppeteer": "^21.0.0", "sharp": "^0.34.3", "svgo": "^3.0.0", "vite": "^5.0.0", "vue": "^3.4.0", "vue-router": "^4.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "nodemon": "^3.0.0", "typescript": "^5.0.0"}, "peerDependencies": {"@modelcontextprotocol/server-figma": "^0.1.0"}, "keywords": ["figma-restoration", "mcp", "vue", "figma", "screenshot", "testing", "component-restoration", "design-to-code", "standalone-kit"], "author": "Figma Restoration Kit Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/figma-restoration-kit.git"}, "engines": {"node": ">=18.0.0"}, "files": ["src/", "config/", "docs/", "scripts/", "examples/", "assets/", "README.md", "LICENSE", "package.json", "vite.config.js"]}