{"mcpServers": {"figma-restoration-kit": {"command": "node", "args": ["/Users/<USER>/Documents/work/camscanner-cloud-vue3/mcp-vue-tools/src/server.js"], "cwd": "/Users/<USER>/Documents/work/camscanner-cloud-vue3/mcp-vue-tools", "env": {"PUPPETEER_EXECUTABLE_PATH": "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome", "NODE_ENV": "development", "PROJECT_ROOT": "/Users/<USER>/Documents/work/camscanner-cloud-vue3"}}}}