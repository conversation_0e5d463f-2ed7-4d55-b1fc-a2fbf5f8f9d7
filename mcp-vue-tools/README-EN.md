# Figma Restoration Kit

A comprehensive standalone toolkit for restoring Figma designs to Vue components using MCP (Model Context Protocol) tools.

## 🚀 Features

- **Figma Design Analysis**: Extract and analyze Figma design data using MCP tools
- **Vue Component Generation**: AI-powered conversion from Figma JSON to Vue components  
- **Visual Comparison**: Automated screenshot comparison with pixel-perfect accuracy
- **Testing Framework**: Comprehensive testing tools for component validation
- **Standalone Package**: Complete separation from main projects, works as a submodule
- **Element Plus Integration**: Built-in support for Element Plus components
- **Responsive Design**: Support for responsive component generation

## 📦 Installation

### As a Git Submodule (Recommended)

```bash
# Add as submodule to your project
git submodule add https://github.com/your-org/figma-restoration-kit.git mcp-vue-tools

# Initialize and setup
cd mcp-vue-tools
yarn install
chmod +x scripts/install.sh
./scripts/install.sh
```

### Standalone Installation

```bash
# Clone the repository
git clone https://github.com/your-org/figma-restoration-kit.git
cd figma-restoration-kit

# Install dependencies
yarn install

# Run setup
yarn setup
```

## 🔧 Quick Start

1. **Install Dependencies**
   ```bash
   yarn install
   ```

2. **Start the MCP Server**
   ```bash
   yarn mcp
   ```

3. **Configure Your IDE**
   - Copy configuration from `config/mcp-config.template.json`
   - Update paths to match your project structure
   - Restart your IDE (VSCode/Cursor)

## 🛠 Available MCP Tools

### Core Tools

1. **vue_dev_server** - Manage Vue development server (start, stop, status)
2. **save_vue_component** - Save AI-generated Vue component code for testing
3. **render_component** - Render Vue component in browser and verify loading
4. **take_screenshot** - Capture component screenshots using Puppeteer
5. **compare_images** - Compare actual vs expected images with pixelmatch
6. **manage_benchmark** - Manage testing workflow and reports
7. **validate_restoration** - Complete validation workflow for Figma restoration

### Figma Integration

- Extract design data from Figma URLs
- Analyze component structure and layout
- Generate semantic Vue component code
- Validate visual accuracy with original designs

## 📖 Usage Examples

### Basic Figma Restoration Workflow

```javascript
// 1. Extract Figma data (use Figma MCP tools)
const figmaData = await getFigmaData({
  fileKey: "your-figma-file-key",
  nodeId: "your-node-id"
});

// 2. Generate and test Vue component
await validateRestoration({
  componentName: "MyComponent", 
  vueCode: "generated-vue-code",
  expectedImageUrl: "figma-design-image-url"
});

// 3. Take screenshot and compare
await takeScreenshot({
  componentName: "MyComponent"
});

await compareImages({
  componentName: "MyComponent",
  threshold: 0.1
});
```

### Component Development Workflow

```javascript
// Start development server
await vue_dev_server({ action: "start" });

// Save component for testing
await save_vue_component({
  componentName: "TestComponent",
  vueCode: `
    <template>
      <div class="test-component">
        <h1>Hello World</h1>
      </div>
    </template>
  `
});

// Render and screenshot
await render_component({ componentName: "TestComponent" });
await take_screenshot({ componentName: "TestComponent" });
```

## 📁 Directory Structure

```
mcp-vue-tools/
├── README.md                 # Chinese documentation
├── README-EN.md              # English documentation  
├── docs/                     # Detailed documentation
├── config/                   # Configuration templates
├── scripts/                  # Installation and setup scripts
├── examples/                 # Example components and workflows
├── src/                      # Source code
│   ├── server.js             # MCP server
│   ├── tools/                # MCP tool implementations
│   └── utils/                # Utility functions
├── output/                   # Generated components
├── results/                  # Screenshots and comparison results
├── assets/                   # Static assets and images
└── temp/                     # Temporary files
```

## ⚙️ Configuration

### MCP Server Configuration

Copy and customize the template:

```bash
cp config/mcp-config.template.json config/mcp-config.json
```

Update the configuration with your project paths:

```json
{
  "mcpServers": {
    "figma-restoration-kit": {
      "command": "node",
      "args": ["src/server.js"],
      "cwd": "/path/to/your/project/mcp-vue-tools",
      "env": {
        "PUPPETEER_EXECUTABLE_PATH": "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
        "NODE_ENV": "development"
      }
    }
  }
}
```

### Cursor Rules Integration

Copy the cursor rules template to your project:

```bash
cp config/cursor-rules.template.md ../cursor-rules.md
```

## 🧪 Testing

### Run Installation Tests

```bash
yarn test
```

### Test Individual Components

```bash
# Test a specific component
node test-installation.js ComponentName

# Clean up test results
yarn clean
```

### Validation Workflow

1. Generate Vue component from Figma data
2. Render component in browser environment
3. Capture screenshot at 3x scale
4. Compare with original Figma design
5. Generate diff analysis and improvement suggestions

## 📚 Documentation

- [Installation Guide](docs/installation.md) - Detailed setup instructions
- [Workflow Documentation](docs/workflow.md) - Complete restoration process
- [API Reference](docs/api-reference.md) - MCP tools documentation
- [Troubleshooting](docs/troubleshooting.md) - Common issues and solutions
- [Examples](examples/) - Sample components and workflows

## 🔧 Development

### Start Development Server

```bash
yarn mcp
```

### Add New MCP Tools

1. Create tool implementation in `src/tools/`
2. Register tool in `src/server.js`
3. Add documentation and examples
4. Test with sample components

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Update documentation
6. Submit a pull request

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🆘 Support

- Check [troubleshooting guide](docs/troubleshooting.md)
- Review [examples](examples/) for common use cases
- Open an issue on GitHub
- Check existing documentation in `docs/`

---

**Note**: This kit is designed to be completely independent and can be integrated into any Vue project as a submodule without affecting the main project structure.
