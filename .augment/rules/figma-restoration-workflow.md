---
type: "always_apply"
---

# Figma 组件还原标准工作流程 v2.0

## 🎯 核心原则（必须严格遵守）
1. **严格按照工作流程执行** - 不可跳过任何步骤，必须按顺序执行
2. **基于原始Figma数据** - 使用完整的MCP JSON数据，深度10层，不使用简化版本
3. **素材优先下载** - 必须先分析并下载所有素材再进行还原
4. **精确位置还原** - 基于boundingBox数据实现像素级精确定位
5. **目标95%+还原度** - 追求高精度还原，以ScanResult(93.62%)为标杆

## 🔄 标准工作流程（严格按顺序执行）

### 第一步：获取原始Figma数据 ✅
```javascript
// 使用MCP工具获取完整的原始数据
get_figma_data_figma-local({
  fileKey: "从URL中提取",
  nodeId: "从URL中提取", 
  depth: 10  // 必须使用深度10获取完整层级
})
```

**重要提醒**：
- ❌ 不要使用简化或过滤后的JSON数据
- ✅ 必须使用原始MCP返回的完整JSON数据
- ✅ 保存完整数据到 `results/{ComponentName}/complete-figma-data.json`

### 第二步：分析素材需求 🔍
基于原始JSON数据识别需要下载的素材：

**必须下载的素材类型**：
- **IMAGE-SVG类型** - 所有矢量图标和装饰图
- **INSTANCE类型** - 组件实例（如Logo、品牌标识）
- **特殊节点** - 包含图片填充的节点
- **功能图标** - 按钮图标、状态图标、操作图标

**分析方法**：
```javascript
// 在JSON中搜索这些类型
"type": "IMAGE-SVG"
"type": "INSTANCE" 
"componentId": "xxx"  // 组件实例
"fills": "fill_xxx"   // 可能包含图片
```

### 第三步：批量下载素材 📥
```javascript
download_figma_images_figma-local({
  fileKey: "文件Key",
  nodes: [
    {"nodeId": "节点ID", "fileName": "描述性文件名.svg"}
  ],
  localPath: "/path/to/components/{ComponentName}/images",
  svgOptions: {
    "includeId": false,
    "outlineText": true, 
    "simplifyStroke": true
  }
})
```

**素材下载检查清单**：
- ✅ 所有IMAGE-SVG节点已识别
- ✅ 所有INSTANCE节点已识别  
- ✅ 文件名具有描述性（如：success_icon.svg）
- ✅ 素材保存到正确的images目录
- ✅ 验证素材文件完整性

### 第四步：分析层级结构 🏗️
基于boundingBox和layout数据分析：

**位置信息提取**：
```json
"boundingBox": {
  "x": 120,      // 精确X坐标
  "y": 104,      // 精确Y坐标  
  "width": 134,  // 精确宽度
  "height": 32   // 精确高度
}
```

**布局信息提取**：
```json
"layout": {
  "mode": "column",        // 布局模式：column/row/none
  "alignItems": "center",  // 对齐方式
  "gap": 18,              // 间距
  "padding": "80px 120px" // 内边距
}
```

### 第五步：创建Vue组件 🎨
```vue
<template>
  <div class="component-name">
    <!-- 基于层级结构的HTML -->
    <div class="section" style="position: absolute; top: {y}px; left: {x}px;">
      <img src="./images/素材名.svg" alt="描述" />
    </div>
  </div>
</template>

<style scoped>
/* 精确位置还原 */
.element {
  position: absolute;
  top: {boundingBox.y}px;
  left: {boundingBox.x}px; 
  width: {boundingBox.width}px;
  height: {boundingBox.height}px;
}
</style>
```

### 第六步：测试验证 🧪
1. **下载期望图片**：
```javascript
download_figma_images_figma-local({
  fileKey: "文件Key",
  nodes: [{"nodeId": "主节点ID", "fileName": "{ComponentName}_expected.png"}],
  localPath: "/path/to/results/{ComponentName}",
  pngScale: 3  // 3倍图确保高清
})
```

2. **截图对比**：
```bash
node scripts/fix-screenshot-dimensions.js fix {ComponentName}
node scripts/smart-compare.js single {ComponentName}
```

## 📁 文件命名规范

### 组件目录结构
```
src/components/{ComponentName}/
├── index.vue                    # 主组件文件
├── images/                      # 素材目录
│   ├── logo.svg                # Logo文件
│   ├── success_icon.svg        # 状态图标
│   ├── {function}_icon.svg     # 功能图标
│   └── {purpose}_decoration.svg # 装饰图片

results/{ComponentName}/
├── {ComponentName}_expected.png # 期望图片（3倍图）
├── actual.png                  # 实际截图
├── diff.png                    # 差异图片
└── complete-figma-data.json    # 完整Figma数据
```

### 素材命名规范
- **Logo文件**: `logo.svg` 或 `{brand}_logo.svg`
- **状态图标**: `{state}_icon.svg` (如: success_icon.svg, error_icon.svg)
- **功能图标**: `{function}_icon.svg` (如: pdf_word_icon.svg, scan_id_icon.svg)
- **装饰图片**: `{purpose}_decoration.svg` (如: card_decoration.svg)
- **按钮图标**: `{action}_icon.svg` (如: back_icon.svg, download_icon.svg)

## 📊 质量标准

### 还原度等级
- **🏆 优秀**: 95%+ 还原度 (目标标准)
- **👍 良好**: 90-95% 还原度  
- **⚠️ 需改进**: 85-90% 还原度
- **❌ 不合格**: <85% 还原度

### 验收标准检查清单
- [ ] **原始数据获取** - 使用完整MCP JSON数据，深度10层
- [ ] **素材完整下载** - 所有IMAGE-SVG和INSTANCE节点已下载
- [ ] **文件命名规范** - 符合命名规范，对比脚本可正常工作
- [ ] **尺寸精确匹配** - 截图尺寸与期望图片匹配
- [ ] **位置精确还原** - 关键元素位置误差<5px
- [ ] **颜色准确匹配** - 使用Figma原始色值
- [ ] **字体正确应用** - 字体、字号、字重匹配设计稿
- [ ] **还原度达标** - 目标95%+，最低85%

## 🚀 成功案例参考

### ScanResult组件 (93.62%还原度) 🏆
**成功要素**：
- ✅ 完整原始数据：使用depth:10获取完整JSON
- ✅ 素材完整下载：success_check_icon.svg
- ✅ 精确布局还原：基于layout数据的Flexbox布局
- ✅ 字体系统匹配：PingFang SC + zihunbiantaoti
- ✅ 颜色精确匹配：直接使用Figma色值 (#04AA65, #262626等)
- ✅ 一次性成功：无需迭代优化

### ExchangeSuccess组件 (87.66%还原度) ⚠️
**成功要素**：
- ✅ 9个素材完整下载：Logo、图标、装饰图等
- ✅ 精确位置定位：基于boundingBox的absolute定位
- ✅ 渐变效果还原：金色渐变文字和背景
- ✅ 移动端适配：375×812尺寸完美匹配

## ⚠️ 常见错误避免

### 数据获取错误
- ❌ 使用简化或过滤后的JSON数据
- ❌ depth参数设置过小，遗漏深层节点
- ✅ 必须使用原始MCP返回的完整JSON，depth:10

### 素材处理错误  
- ❌ 遗漏IMAGE-SVG或INSTANCE类型节点
- ❌ 文件命名不规范，导致对比脚本失败
- ✅ 完整分析所有素材需求，规范命名

### 布局实现错误
- ❌ 忽略boundingBox数据，使用相对布局
- ❌ 不使用Figma原始色值，自行调色
- ✅ 严格按照boundingBox实现精确定位

### 测试验证错误
- ❌ 期望图片文件名格式错误
- ❌ 跳过尺寸验证步骤
- ✅ 严格按照文件命名规范，完整测试流程

## 🔧 故障排除指南

### 对比脚本报错
**问题**: `ENOENT: no such file or directory, open 'xxx_expected.png'`
**解决**: 检查文件名格式，必须是 `{ComponentName}_expected.png`

### 还原度过低
**问题**: 还原度<85%
**解决**: 
1. 检查是否遗漏素材下载
2. 验证位置是否使用boundingBox数据
3. 确认颜色是否使用Figma原始值

### 素材显示异常
**问题**: 图标不显示或尺寸错误
**解决**:
1. 检查素材路径是否正确
2. 验证SVG文件是否完整下载
3. 调整CSS中的width/height

---

**重要提醒**: 这个流程已经通过ScanResult(93.62%)和ExchangeSuccess(87.66%)验证，严格按照此流程执行可以确保高质量的Figma组件还原！
