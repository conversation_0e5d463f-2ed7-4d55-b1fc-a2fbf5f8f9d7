---
description: 
globs: 
alwaysApply: true
type: "always_apply"
---
# Figma 设计稿还原项目 - Cursor Rules

## 基础开发规则

### 语言与沟通
1. **保持使用中文对话**，无论用户以何种语言提问，均以中文进行回复和沟通
2. **全力以赴，不要有所保留**。无论用户提出什么问题，都要尽最大努力、用最全面和详细的方式帮助用户解决问题，不敷衍、不遗漏、不推诿
3. **遵循 Vue3 最佳实践**，不要在模板上定义或使用变量

### 包管理工具
- 项目使用 **yarn** 作为包管理工具
- 启动开发服务器：`yarn dev`
- **不要使用 npm 或 pnpm 启动服务器**
- **安装依赖时需要用户确认**，不使用 npm 或 pnpm

### 文件组织规范
- **素材和图标管理**：所有SVG图标和位图素材必须放在组件同级别的`images/`文件夹中
- **避免内联SVG**：不要将SVG代码直接写在Vue组件中，使用外部文件引用
- **图像访问路径**：将素材复制到`public/images/`目录，使用`/images/filename.svg`路径引用
- **文件命名规范**：图标和素材文件使用kebab-case命名格式

## Figma还原调试原则 (2025-07-14)

### 三级优先级调试法
1. **🔴 大区域差异** - 素材问题（最高优先级）
   - 还原度 < 90%：重新下载Figma素材
   - 四象限差异：检查背景图和基础素材

2. **🟡 普通元素差异** - 布局问题（中等优先级）
   - 还原度 90-95%：基于boundingBox精确定位
   - 非必要不使用绝对定位，优先flex布局

3. **🟢 字体差异** - 可忽略（最低优先级）
   - 还原度 > 95%：浏览器渲染差异，按原则忽略

### 核心经验法则
- **简单即美**：背景图 > 复杂CSS结构
- **素材优先**：Figma切图 > 代码模拟效果
- **布局选择**：flex布局 > 绝对定位

### 快速调试命令
```bash
# 差异分析
node scripts/figma-diff-analyzer.js ComponentName

# 重新截图
node scripts/fix-screenshot-dimensions.js fix ComponentName
```