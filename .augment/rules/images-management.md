---
type: "always_apply"
description: "Images and assets management rules for Figma restoration"
---

# 图像和素材管理规范

## 📁 文件组织结构

### 组件目录标准结构
```
src/components/ComponentName/
├── index.vue          # 主组件文件
├── images/           # 素材和图标文件夹（必需）
│   ├── icon-name.svg # SVG图标文件
│   ├── image-name.png # 位图文件
│   ├── logo.svg      # 标志文件
│   └── background.jpg # 背景图片
└── metadata.json     # 组件元数据
```

### Public目录结构
```
public/
├── images/           # Web可访问的图像目录
│   ├── icon-name.svg # 从组件images复制的文件
│   ├── image-name.png
│   └── ...
└── api/             # API数据文件
```

## 🎯 素材处理原则

### 1. 分离原则
- **严格禁止**将SVG代码内联到Vue组件中
- **必须**将所有图标和素材作为独立文件存储
- **保持**组件代码的简洁性和可维护性

### 2. 存储位置
- **组件级素材**：存放在`src/components/ComponentName/images/`
- **全局素材**：存放在`public/images/`
- **临时下载**：先下载到`results/ComponentName/`，再移动到正确位置

### 3. 访问方式
```vue
<!-- 正确的引用方式 -->
<img src="/images/icon-name.svg" alt="Icon description" />

<!-- 错误的内联方式 -->
<svg>...</svg>
```

## 📝 命名规范

### 文件命名标准
- **格式**：使用kebab-case格式
- **描述性**：文件名应清楚描述用途
- **一致性**：同类文件使用统一的命名模式

### 命名示例
```
✅ 正确命名：
- user-avatar.png
- arrow-right.svg
- company-logo.svg
- background-pattern.jpg
- i-ve-changed.svg

❌ 错误命名：
- userAvatar.png
- ArrowRight.svg
- logo1.svg
- bg.jpg
- vector.svg
```

## 🔄 工作流程

### Figma组件还原流程
1. **下载素材**：使用MCP工具下载到results目录
2. **创建images文件夹**：在组件目录下创建images文件夹
3. **移动文件**：将素材从results移动到组件images文件夹
4. **复制到public**：将素材复制到public/images/以便Web访问
5. **更新组件**：在Vue组件中使用外部文件引用
6. **验证访问**：确保图像在浏览器中正确显示

### 命令示例
```bash
# 创建组件images目录
mkdir -p src/components/ComponentName/images

# 移动下载的素材
mv results/ComponentName/icon.svg src/components/ComponentName/images/icon-name.svg

# 复制到public目录
cp src/components/ComponentName/images/icon-name.svg public/images/
```

## 🎨 图像类型处理

### SVG图标
- **用途**：界面图标、装饰元素、简单图形
- **优势**：矢量格式、文件小、可缩放
- **处理**：保持原始SVG代码，不做修改

### 位图图像
- **用途**：照片、复杂图像、背景图
- **格式**：PNG（透明背景）、JPG（照片）、WebP（现代格式）
- **优化**：适当压缩以平衡质量和文件大小

### 特殊处理
- **Figma导出**：使用3x缩放确保高清显示
- **透明背景**：PNG格式保持透明度
- **颜色准确性**：确保颜色与设计稿一致

## ⚠️ 注意事项

### 禁止行为
1. **不要**将SVG代码直接粘贴到Vue模板中
2. **不要**使用相对路径引用组件内的图像
3. **不要**忽略图像的alt属性
4. **不要**使用过大的图像文件

### 最佳实践
1. **始终**为图像添加有意义的alt属性
2. **确保**图像文件名具有描述性
3. **保持**images文件夹的整洁和组织
4. **验证**所有图像在不同设备上的显示效果

## 🔍 质量检查

### 文件检查清单
- [ ] 图像文件存放在正确的images文件夹中
- [ ] 文件命名符合kebab-case规范
- [ ] 图像已复制到public/images/目录
- [ ] Vue组件使用正确的外部引用路径
- [ ] 所有图像都有适当的alt属性
- [ ] 图像在浏览器中正确显示

### 性能检查
- [ ] 图像文件大小合理（SVG < 50KB，PNG < 500KB）
- [ ] 没有未使用的图像文件
- [ ] 图像格式选择合适
- [ ] 加载速度满足要求

## 📚 相关工具

### MCP工具
- `download_figma_images_figma-local`：下载Figma图像
- `save_vue_component_figma-restoration-kit`：保存组件
- `take_screenshot_figma-restoration-kit`：截图验证

### 文件操作
```bash
# 批量重命名（如需要）
rename 's/oldname/newname/' *.svg

# 检查文件大小
du -h images/*.svg

# 验证SVG文件
xmllint --noout images/*.svg
```

通过遵循这些规范，确保Figma组件还原项目中的图像和素材管理规范、高效、可维护。
