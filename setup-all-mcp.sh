#!/bin/bash

# 完整的 MCP 服务器集成安装脚本

echo "🚀 开始安装和配置所有 MCP 服务器..."

# 检查当前目录
if [ ! -f "package.json" ]; then
    echo "❌ 请在项目根目录中运行此脚本"
    exit 1
fi

# 1. 安装 Vue Figma Tools
echo "📦 1. 安装 Vue Figma Tools..."
if [ -d "mcp-vue-tools" ]; then
    cd mcp-vue-tools
    if [ -f "install-mcp.sh" ]; then
        chmod +x install-mcp.sh
        ./install-mcp.sh
        if [ $? -eq 0 ]; then
            echo "   ✅ Vue Figma Tools 安装成功"
        else
            echo "   ❌ Vue Figma Tools 安装失败"
            exit 1
        fi
    else
        echo "   ❌ 找不到 Vue Figma Tools 安装脚本"
        exit 1
    fi
    cd ..
else
    echo "   ❌ 找不到 mcp-vue-tools 目录"
    exit 1
fi

# 2. 检查 Weather MCP Server
echo "📦 2. 检查 Weather MCP Server..."
if [ -d "weather-mcp-server" ]; then
    cd weather-mcp-server
    
    # 检查依赖是否已安装
    if [ ! -d "node_modules" ]; then
        echo "   📥 安装 Weather MCP 依赖..."
        npm install
        if [ $? -eq 0 ]; then
            echo "   ✅ Weather MCP 依赖安装成功"
        else
            echo "   ❌ Weather MCP 依赖安装失败"
            exit 1
        fi
    else
        echo "   ✅ Weather MCP 依赖已存在"
    fi
    
    # 测试 Weather MCP 服务器
    echo "   🧪 测试 Weather MCP 服务器..."
    timeout 5s node index.js > /dev/null 2>&1 &
    SERVER_PID=$!
    sleep 2
    
    if kill -0 $SERVER_PID 2>/dev/null; then
        echo "   ✅ Weather MCP 服务器测试成功"
        kill $SERVER_PID 2>/dev/null
    else
        echo "   ❌ Weather MCP 服务器测试失败"
        exit 1
    fi
    
    cd ..
else
    echo "   ❌ 找不到 weather-mcp-server 目录"
    exit 1
fi

# 3. 创建完整的 MCP 配置
echo "📝 3. 创建完整的 MCP 配置..."

# 创建集成配置文件
cat > mcp-complete-config.json << 'EOF'
{
  "mcpServers": {
    "vue-figma-tools": {
      "command": "node",
      "args": ["src/server.js"],
      "cwd": "/Users/<USER>/Documents/work/camscanner-cloud-vue3/mcp-vue-tools",
      "env": {
        "PUPPETEER_EXECUTABLE_PATH": "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
        "NODE_ENV": "development"
      }
    },
    "weather": {
      "command": "node",
      "args": ["index.js"],
      "cwd": "/Users/<USER>/Documents/work/camscanner-cloud-vue3/weather-mcp-server",
      "env": {
        "NODE_ENV": "development"
      }
    },
    "figma": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-figma"],
      "env": {
        "FIGMA_PERSONAL_ACCESS_TOKEN": "YOUR_FIGMA_TOKEN_HERE"
      }
    }
  }
}
EOF

# 创建仅包含本地服务器的配置（不包含 Figma）
cat > mcp-local-config.json << 'EOF'
{
  "mcpServers": {
    "vue-figma-tools": {
      "command": "node",
      "args": ["src/server.js"],
      "cwd": "/Users/<USER>/Documents/work/camscanner-cloud-vue3/mcp-vue-tools",
      "env": {
        "PUPPETEER_EXECUTABLE_PATH": "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
        "NODE_ENV": "development"
      }
    },
    "weather": {
      "command": "node",
      "args": ["index.js"],
      "cwd": "/Users/<USER>/Documents/work/camscanner-cloud-vue3/weather-mcp-server",
      "env": {
        "NODE_ENV": "development"
      }
    }
  }
}
EOF

echo "   ✅ MCP 配置文件创建成功"

# 4. 创建启动脚本
echo "🔧 4. 创建启动脚本..."

cat > start-all-mcp.sh << 'EOF'
#!/bin/bash

echo "🚀 启动所有 MCP 服务器..."

# 启动 Vue Figma Tools
echo "📦 启动 Vue Figma Tools..."
cd mcp-vue-tools
export PUPPETEER_EXECUTABLE_PATH="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
export NODE_ENV="development"
node src/server.js &
VUE_PID=$!
echo "   ✅ Vue Figma Tools 已启动 (PID: $VUE_PID)"
cd ..

# 启动 Weather MCP
echo "🌤️ 启动 Weather MCP..."
cd weather-mcp-server
export NODE_ENV="development"
node index.js &
WEATHER_PID=$!
echo "   ✅ Weather MCP 已启动 (PID: $WEATHER_PID)"
cd ..

echo "🎉 所有 MCP 服务器已启动！"
echo "📋 进程 ID:"
echo "   Vue Figma Tools: $VUE_PID"
echo "   Weather MCP: $WEATHER_PID"
echo ""
echo "⚠️  按 Ctrl+C 停止所有服务器"

# 等待用户中断
trap "echo '🛑 停止所有服务器...'; kill $VUE_PID $WEATHER_PID 2>/dev/null; exit 0" INT
wait
EOF

chmod +x start-all-mcp.sh

echo "   ✅ 启动脚本创建成功"

# 5. 运行最终测试
echo "🧪 5. 运行最终集成测试..."

# 测试 Vue Figma Tools
echo "   测试 Vue Figma Tools..."
cd mcp-vue-tools
if [ -f "test-installation.js" ]; then
    node test-installation.js > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "   ✅ Vue Figma Tools 测试通过"
    else
        echo "   ❌ Vue Figma Tools 测试失败"
    fi
else
    echo "   ⚠️  Vue Figma Tools 测试脚本不存在"
fi
cd ..

# 测试 Weather MCP
echo "   测试 Weather MCP..."
cd weather-mcp-server
timeout 3s node index.js > /dev/null 2>&1 &
TEST_PID=$!
sleep 1
if kill -0 $TEST_PID 2>/dev/null; then
    echo "   ✅ Weather MCP 测试通过"
    kill $TEST_PID 2>/dev/null
else
    echo "   ❌ Weather MCP 测试失败"
fi
cd ..

echo ""
echo "🎉 MCP 服务器集成安装完成！"
echo ""
echo "📋 可用的配置文件:"
echo "   • mcp-complete-config.json  - 完整配置（包含 Figma）"
echo "   • mcp-local-config.json     - 本地配置（Vue + Weather）"
echo ""
echo "🔧 使用方法:"
echo "   1. 复制配置文件内容到 MCP 客户端"
echo "   2. 或使用启动脚本: ./start-all-mcp.sh"
echo ""
echo "📦 已安装的 MCP 服务器:"
echo "   • Vue Figma Tools (8个工具) - Figma 设计还原和组件测试"
echo "   • Weather MCP (1个工具)     - 天气查询服务"
echo "   • Figma MCP (可选)          - 需要配置 FIGMA_PERSONAL_ACCESS_TOKEN"
echo ""
echo "🌟 下一步:"
echo "   1. 在 MCP 客户端中导入配置"
echo "   2. 如需 Figma 功能，请设置 FIGMA_PERSONAL_ACCESS_TOKEN"
echo "   3. 重启 VSCode 使配置生效"
